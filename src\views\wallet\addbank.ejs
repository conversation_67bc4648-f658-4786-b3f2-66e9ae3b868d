<!DOCTYPE html>
<html translate="no" data-dpr="1" style="font-size: 48.16px">

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="robots" content="noindex,nofollow" />
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport" />
    <title>Add Bank</title>
    <link rel="stylesheet" href="./index_files/index-********.css">
    <link rel="stylesheet" href="../index_files/index-********.css">
    <link href="/css/wallet/main.css" rel="stylesheet" />
    <link href="/css/wallet/chunk_2-1.css" rel="stylesheet" />
    <link href="/css/wallet/chunk_2-2.css" rel="stylesheet" />
    <link href="/css/wallet/chunk_2-3.css" rel="stylesheet" />
    <link rel="shortcut icon" href="/images/bitbug_favicon.ico" type="image/x-icon" />
    <style>
        html,
        body {
            height: 100%;
            width: 100%;
            background-color: #090909;
            padding: 0;
            margin: 0;
        }

        .mian[data-v-7aa0f84a] {
            background-color: #fbfcfd
        }

        .mian .bank[data-v-7aa0f84a] {
            background: #292929;
            padding: .4rem
        }

        .mian .bank .box[data-v-7aa0f84a] {
            background: #3f3f3f;
            color: #f3c300;
            border-radius: .26667rem;
            padding: .4rem
        }

        .mian .bank .box .item[data-v-7aa0f84a] {
            margin-bottom: .26667rem
        }

        .mian .bank .box .item .lab[data-v-7aa0f84a] {
            font-size: .4rem;
            margin-bottom: .26667rem
        }

        .mian .bank .box .item .input .ipt[data-v-7aa0f84a] {
            width: 100%;
            padding: 0 .4rem;
            height: .93333rem;
            line-height: .93333rem;
            border-radius: .13333rem;
            border: .05333rem solid #f3c300
        }

        .mian .bank .bank-btn .btn[data-v-7aa0f84a] {
            width: 50%;
            height: 0.93333rem;
            color: #8f5206;
            font-size: .4rem;
            font-weight: 700;
            letter-spacing: .01333rem;
            border: none;
            border-radius: 1.06667rem;
            background: -webkit-linear-gradient(top, #FAE59F 0%, #C4933F 100%);
            background: linear-gradient(180deg, #FAE59F 0%, #C4933F 100%)
        }
    </style>
</head>

<body style="font-size: 12px">
    <div id="app">
        <div data-v-7aa0f84a="" class="mian">
            <div data-v-106b99c8="" data-v-7aa0f84a="" class="navbar">
                <div data-v-106b99c8="" class="navbar-left" onclick="location.href='/wallet/withdrawal'">
                    <div data-v-106b99c8="" class="bank c-row c-row-middle-center">
                        <img data-v-106b99c8="" src="/images/back.c3244ab0.png" class="navbar-back" />
                    </div>
                </div>
                <div data-v-106b99c8="" class="navbar-title">Add your details</div>
                <div data-v-106b99c8="" class="navbar-right"></div>
            </div>
            <div data-v-7aa0f84a="" class="bank">
                <div data-v-7aa0f84a="" class="box">
                    <div data-v-7aa0f84a="" class="c-row m-b-10 c-row-middle">
                        <div data-v-7aa0f84a="" class="van-image" style="width: 40px; height: 40px">
                            <img src="/images/pay_icon_debitcard_red.webp" class="van-image__img" />
                        </div>
                        <div data-v-7aa0f84a="" class="p-l-15">Add your details</div>
                    </div>
                    <div data-v-7aa0f84a="" class="item">
                        <div data-v-7aa0f84a="" class="lab">Bank name</div>
                        <div data-v-7aa0f84a="" class="input">
                            <input data-v-7aa0f84a="" type="text" placeholder="Enter Your Bank Name" class="ipt" />
                        </div>
                    </div>
                    <div data-v-7aa0f84a="" class="item">
                        <div data-v-7aa0f84a="" class="lab">Your Full Name</div>
                        <div data-v-7aa0f84a="" class="input">
                            <input data-v-7aa0f84a="" type="text" placeholder="Enter Your Bank Name"
                                oninput="value=value.replace(/[^a-zA-Z\s+$]/g,'').toUpperCase()" class="ipt" />
                        </div>
                    </div>
                    <div data-v-7aa0f84a="" class="item">
                        <div data-v-7aa0f84a="" class="lab">Bank Account number</div>
                        <div data-v-7aa0f84a="" class="input">
                            <input data-v-7aa0f84a="" type="number" placeholder="Enter Your Bank Account Number"
                                oninput="value=value.replace(/[^\d]/g,'')" class="ipt" />
                        </div>
                    </div>
                    <!---->
                    <div data-v-7aa0f84a="" class="item">
                        <div data-v-7aa0f84a="" class="lab">IFSC Code</div>
                        <div data-v-7aa0f84a="" class="input">
                            <input data-v-7aa0f84a="" type="text" placeholder="Enter Your IFSC Code" class="ipt" />
                        </div>
                    </div>
                    <div data-v-7aa0f84a="" class="item">
                        <div data-v-7aa0f84a="" class="lab">Phone Number</div>
                        <div data-v-7aa0f84a="" class="input">
                            <input data-v-7aa0f84a="" maxlength="10" type="mob" placeholder="It Is Required"
                                class="ipt" />
                        </div>
                    </div>
                    
             <div data-v-7aa0f84a="" class="item">
             <div data-v-7aa0f84a="" class="lab">Crypto Address(USDT)</div>
             <div data-v-7aa0f84a="" class="input">
             <input data-v-7aa0f84a=""  type="text" placeholder="Crypto Address(USDT)"
             class="ipt" />
             </div>
             </div>
                    
                    <!---->
                </div>
                <div data-v-7aa0f84a="" class="c-row c-row-center m-t-20 bank-btn">
                    <button data-v-7aa0f84a=""
                        class="btn van-button van-button--default van-button--normal van-button--block van-button--round">
                        <div data-v-7aa0f84a="" class="van-button__content">
                            <span data-v-7aa0f84a="" class="van-button__text">
                               Add your details
                            </span>
                        </div>
                    </button>
                </div>
            </div>
            <div data-v-7692a079="" data-v-7aa0f84a="" class="Loading c-row c-row-middle-center" style="display: none">
                <div data-v-7692a079="" class="van-loading van-loading--circular">
                    <span data-v-7692a079="" class="van-loading__spinner van-loading__spinner--circular"
                        style="width: auto; height: auto; -webkit-animation-duration: 1s; animation-duration: 1s;">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                            viewBox="0 0 200 200" width="200" height="200" preserveAspectRatio="xMidYMid meet"
                            style="width: 100%;height: 100%;transform: translate3d(0px, 0px, 0px);content-visibility: visible;">
                            <g transform="matrix(-0.5984085202217102,0.8011911511421204,-0.8011911511421204,-0.5984085202217102,239.9599609375,79.72173309326172)"
                                style="display: block;">
                                <image width="200px" height="200px" preserveAspectRatio="xMidYMid slice"
                                    xlink:href="/index_files/loadingspinner.png"></image>
                            </g>
                            <g transform="matrix(0.9408400058746338,0,0,0.9408400058746338,20.969436645507812,79.30152130126953)"
                                style="display: block;">
                               
                            </g>
                        </svg>
                    </span>
                    <img src="/index_files/h5setting_202401100608011fs2.png" style="position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);width: 80%;height: 80%;margin-top: 10px;">
                </div>
            </div>
            <!---->
        </div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="/js/client.js"></script>
    <script>
        function alertMess(text, sic) {
            $('body').append(
                `
                <div data-v-1dcba851="" class="msg">
                    <div data-v-1dcba851="" class="msg-content v-enter-active v-enter-to" style=""> ${text} </div>
                </div>
                `
            );
            setTimeout(() => {
                $('.msg .msg-content').removeClass('v-enter-active v-enter-to');
                $('.msg .msg-content').addClass('v-leave-active v-leave-to');
                setTimeout(() => {
                    $('.msg').remove();
                }, 100);
                sic.removeClass('block-click');
            }, 1000);
        }
        
        
      $.ajax({
                type: "GET",
                url: "/api/webapi/GetUserInfo",
                dataType: "json",
                success: function (response) {
                     let user = response.data;
                  console.log(user);    
        $('.ipt:eq(0)').val(user.banks.name_bank);
        $('.ipt:eq(1)').val(user.banks.name_user);
        $('.ipt:eq(2)').val(user.banks.stk);
        $('.ipt:eq(3)').val(user.banks.email);
        $('.ipt:eq(4)').val(user.banks.tinh);
        $('.ipt:eq(5)').val(user.banks.cryptoAdd);
        
        
            
            
                }
            });
        
        
        
        
        $('button').click(function (e) {
            e.preventDefault();
            let name_bank = $('.ipt:eq(0)').val().trim();
            let name_user = $('.ipt:eq(1)').val().trim();
            let stk = $('.ipt:eq(2)').val().trim();
            let email = $('.ipt:eq(3)').val().trim();
            let tinh = $('.ipt:eq(4)').val().trim();
            let cryptoAdd = $('.ipt:eq(5)').val().trim();
            
            
            if (name_bank && name_user && stk && email && tinh) {
                $.ajax({
                    type: "POST",
                    url: "/api/webapi/addBank",
                    data: {
                        name_bank: name_bank,
                        name_user: name_user,
                        stk: stk,
                        email: email,
                        tinh: tinh,
                        cryptoAdd:cryptoAdd
                    },
                    dataType: "json",
                    success: function (response) {
                        console.log(response);
                        alertMess(response.message, $(this));
                        setTimeout(() => {
                            if (response.message) {
                               window.location.href = "/wallet/withdrawal";
                            }
                        }, 1200);
                    }
                });
            } else {
                alertMess('Please enter full information', $(this));
            }
        });
    </script>
</body>

</html>