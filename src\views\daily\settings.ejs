<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Setting</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <link rel="stylesheet" href="/plugins/fontawesome-free/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="/dist/css/adminlte.min.css">
    <link rel="stylesheet" href="/css/admin.css">
    <link href="//cdn.jsdelivr.net/npm/@sweetalert2/theme-dark@4/dark.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
    <style>
        .form-group {
            margin-top: 20px; 
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 5px #2ecc71;
        }
        
        .form-group button {
            margin-top: 30px;
        }
    </style>
</head>

<body class="hold-transition sidebar-mini">
    <div class="wrapper">
        <%- include('nav') %>
            <div class="content-wrapper">
                <section class="content-header">
                    <div class="container-fluid">
                        <div class="row mb-2">
                            <div class="col-sm-6">
                                <h1>Setting</h1>
                            </div>
                        </div>
                    </div>
                    <!-- /.container-fluid -->
                </section>

                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-12">
                            <div style="padding: 10px 20px;margin-bottom: 200px;">

                                <div class="form-group">
                                    <div class="text-center">
                                        <label for="telegram">Telegram (Set up for members to use to contact you)</label>
                                    </div>
                                    <label for="telegram" style="color: #3498db;">Link Telegram</label>
                                    <input type="text" class="form-control" id="telegram" placeholder="Enter Telegram Link" value=""><br>
                                    <button type="submit" class="btn btn-primary telegram" style="width: 100%;">Edit</button>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>
    <script src="/plugins/jquery/jquery.min.js"></script>
    <script src="/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="/dist/js/adminlte.min.js"></script>
    <script src="/js/admin/admin.js"></script>
    <script src="//cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.js"></script>
    <script>
        $.ajax({
            type: "POST",
            url: "/manager/settings/list",
            data: {
                
            },
            dataType: "json",
            success: function (response) {
                $('input').val(response.telegram2);
            }
        });
        $('.telegram').click(function (e) { 
            e.preventDefault();
            let value = $('input').val().trim();
            if (value) {
                $.ajax({
                    type: "POST",
                    url: "/manager/settings/list",
                    data: {
                        type: 'edit',
                        value: value,
                    },
                    dataType: "json",
                    success: function (response) {
                        alert(response.message);
                    }
                });
            } else {
                alert("Please enter full information");
            }
        });
    </script>
</body>

</html>