server {
    server_name  socket.aviator.brtmultisoftware.com;

    location / {
        proxy_pass http://127.0.0.1:19020;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header REMOTE-HOST $remote_addr;
        proxy_connect_timeout 30s;
        proxy_read_timeout 86400s;
        proxy_send_timeout 30s;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    access_log /www/wwwlogs/socket.aviator.brtmultisoftware.com.log;
    error_log /www/wwwlogs/socket.aviator.brtmultisoftware.com.error.log;






   

}
server {
    if ($host = socket.aviator.brtmultisoftware.com) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


    server_name  socket.aviator.brtmultisoftware.com;
    listen 80;
    return 404; # managed by Certbot


}