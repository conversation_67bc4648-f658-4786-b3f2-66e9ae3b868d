<!DOCTYPE html>
<html translate="no" data-dpr="1" style="font-size: 48.16px;">

<head>
    <meta charset="utf-8">
    <meta name="csrf-token" content="" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="robots" content="noindex,nofollow">
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport">
    <title>Wallet</title>
    <link href="/css/wallet/main.css" rel="stylesheet" />
    <link href="/css/wallet/chunk_2-1.css" rel="stylesheet" />
    <link href="/css/wallet/chunk_2-2.css" rel="stylesheet" />
    <link href="/css/wallet/chunk_2-3.css" rel="stylesheet" />
    <link rel="shortcut icon" href="/images/bitbug_favicon.ico" type="image/x-icon">
    <style>
        html,
        body {
            height: 100%;
            width: 100%;
            background-color: #090909;
            padding: 0;
            margin: 0;
        }
    </style>
</head>

<body style="font-size: 12px;">
    <div id="app">
        <div data-v-7b283485="" class="mian">
            <div data-v-106b99c8="" data-v-7b283485="" class="navbar">
                <div data-v-106b99c8="" class="navbar-left"></div>
                <div data-v-106b99c8="" class="navbar-title"> Wallet </div>
                <div data-v-106b99c8="" class="navbar-right" onclick="location.href='/keFuMenu'">
                    <div data-v-7b283485="" data-v-106b99c8="" class="c-row">
                        <img data-v-7b283485="" data-v-106b99c8="" src="/images/audio.webp" class="audio">
                    </div>
                </div>
            </div>
            <div data-v-7b283485="" class="wallet">
                <div data-v-7b283485="" class="wallet-user c-tc">
                    <div data-v-7b283485="" class="c-row c-row-center img">
                        <div data-v-7b283485="" class="van-image" border-radius="60px"
                            style="width: 60px; height: 60px;">
                            <img src="/images/avatar.svg" class="van-image__img">
                        </div>
                    </div>
                    <div data-v-7b283485="" class="name"></div>
                </div>
                <div data-v-7b283485="" class="wallet-box">
                    <div data-v-7b283485="" class="box">
                        <div data-v-7b283485="" class="icon1"></div>
                        <div data-v-7b283485="" class="title">Wallet</div>
                        <div data-v-7b283485="" class="balance m-t-10">
                            <div data-v-7b283485="" class="txt c-row c-row-middle">
                                <img data-v-7b283485="" width="22px" height="18px" src="/images/king (2).png"
                                    class="m-r-5 img"> My balance：
                            </div>
                            <div data-v-7b283485="" class="c-row balanceMoney c-row-middle-center">
                                <div data-v-7b283485="" class="money">
                                    <!---->
                                    <div data-v-7b283485="">₹<span data-v-7b283485="" class="p-l-10">Loading...</span>
                                    </div>
                                </div>
                                <div data-v-7b283485="" id="reload">
                                    <div data-v-7b283485="" class="van-image img m-l-15"
                                        style="width: 25px; height: 25px;">
                                        <img src="/images/reload.png" class="van-image__img">
                                    </div>
                                </div>
                            </div>
                            <div data-v-7b283485="" class="info c-row">
                                <div data-v-7b283485="" class="item">
                                    <div data-v-7b283485="">Total Recharge</div>
                                    <!---->
                                    <div data-v-7b283485="">₹ 0.00</div>
                                </div>
                                <div data-v-7b283485="" class="item">
                                    <div data-v-7b283485="">Total Withdrawal</div>
                                    <!---->
                                    <div data-v-7b283485="">₹ 0.00</div>
                                </div>
                            </div>
                        </div>
                        <div data-v-7b283485="" class="c-row c-row-between total-btn m-t-30">
                            <div data-v-7b283485="" class="item c-row c-row-center"
                                onclick="location.href='/wallet/withdrawal'">
                                <div data-v-7b283485="" class="li"> WITHDRAW </div>
                            </div>
                            <div data-v-7b283485="" class="item c-row c-row-center"
                                onclick="location.href='/wallet/recharge'">
                                <div data-v-7b283485="" class="li"> RECHARGE </div>
                            </div>
                        </div>
                    </div>
                    <div data-v-7b283485="" class="list m-t-15">
                        <div data-v-7b283485="" class="item c-row c-row-between"
                            onclick="location.href='/wallet/rechargerecord'">
                            <div data-v-7b283485="" class="c-row c-row-middle">
                                <img data-v-7b283485="" width="35px" height="35px" src="/images/recharge.png"
                                    class="chackImg">
                                <span data-v-7b283485="" class="name">Recharge history</span>
                            </div>
                            <div data-v-7b283485="">
                                <i data-v-7b283485="" class="van-icon van-icon-arrow"
                                    style="color: rgb(84, 94, 104); font-size: 16px;">
                                    <!---->
                                </i>
                            </div>
                        </div>
                        <div data-v-7b283485="" class="item c-row c-row-between"
                            onclick="location.href='/wallet/withdrawalrecord'">
                            <div data-v-7b283485="" class="c-row c-row-middle">
                                <img data-v-7b283485="" width="35px" height="35px" src="/images/withdraw.png"
                                    class="chackImg">
                                <span data-v-7b283485="" class="name">Withdrawal History</span>
                            </div>
                            <div data-v-7b283485="">
                                <i data-v-7b283485="" class="van-icon van-icon-arrow"
                                    style="color: rgb(84, 94, 104); font-size: 16px;">
                                    <!---->
                                </i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <%- include('../nav') -%>
        </div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="/js/client.js"></script>
    <script>

        $('#reload .van-image').click(function (e) {
            e.preventDefault();
            $(this).addClass('action block-click');
            setTimeout(() => {
                $(this).removeClass('action block-click');
            }, 2500);
            $.ajax({
                type: "GET",
                url: "/api/webapi/GetUserInfo",
                dataType: "json",
                success: function (response) {
                    let user = response.data;
                    $('.wallet-user .name').text(user.name_user);
                    $('.money').text("₹" + user.money_user + ".00");
                }
            });
        });
        $.ajax({
            type: "GET",
            url: "/api/webapi/GetUserInfo",
            dataType: "json",
            success: function (response) {
                let user = response.data;
                $('.wallet-user .name').text(user.name_user);
                $('.money').text("₹" + user.money_user + ".00");
                $('.balance .item:eq(0) div:eq(1)').text("₹" + response.totalRecharge + ".00");
                $('.balance .item:eq(1) div:eq(1)').text("₹" + response.totalWithdraw + ".00");
            }
        });
    </script>
</body>

</html>