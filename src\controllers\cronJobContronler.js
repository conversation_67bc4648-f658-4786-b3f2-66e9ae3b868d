import connection from "../config/connectDB";
import winGoController from "./winGoController";
import k5Controller from "./k5Controller";
import k3Controller from "./k3Controller";
import trxController from "./trxController";
import mycronController from "./mycronController";
import cron from 'node-cron';

const cronJobGame1p = (io) => {

    cron.schedule('*/1 * * * *', async() => {
        await winGoController.addWinGo(1);
        await winGoController.handlingWinGo1P(1);
        const [winGo1] = await connection.execute('SELECT * FROM `wingo` WHERE `game` = "wingo" ORDER BY `id` DESC LIMIT 2 ', []);
        const data = winGo1; // Cầu mới chưa có kết quả
        io.emit('data-server', { data: data });

        await k5Controller.add5D(1);
        await k5Controller.handling5D(1);
        const [k5D] = await connection.execute('SELECT * FROM 5d WHERE `game` = 1 ORDER BY `id` DESC LIMIT 2 ', []);
        const data2 = k5D; // Cầu mới chưa có kết quả
        io.emit('data-server-5d', { data: data2, 'game': '1' });

        await k3Controller.addK3(1);
        await k3Controller.handlingK3(1);
        const [k3] = await connection.execute('SELECT * FROM k3 WHERE `game` = 1 ORDER BY `id` DESC LIMIT 2 ', []);
        const data3 = k3; // Cầu mới chưa có kết quả
        io.emit('data-server-k3', { data: data3, 'game': '1' });



        // await sleep(2000);

        // TRX 1 minute game
        try {
            await trxController.addTRXGo(1);
            await trxController.handlingTRX1P(1);
            const [trxWin] = await connection.execute('SELECT * FROM `trx` WHERE `game` = "trx" ORDER BY `id` DESC LIMIT 2 ', []);
            const trxWindata = trxWin; // Cầu mới chưa có kết quả
            if (trxWindata && trxWindata.length > 0) {
                console.log('Emitting TRX 1min data:', trxWindata.length, 'records');
                io.emit('data-server-trx', { data: trxWindata });
            } else {
                console.log('No TRX 1min data to emit');
            }
        } catch (error) {
            console.error('Error in TRX 1min cron job:', error);
        }


    });

    cron.schedule('*/3 * * * *', async() => {

        // TRX 3 minute game
        await trxController.addTRXGo(3);
        await trxController.handlingTRX1P(3);
        const [trxWin] = await connection.execute('SELECT * FROM `trx` WHERE `game` = "trx3" ORDER BY `id` DESC LIMIT 2 ', []);
        const trxWindata = trxWin; // Cầu mới chưa có kết quả
        if (trxWindata && trxWindata.length > 0) {
            io.emit('data-server-trx', { data: trxWindata });
        }


        await winGoController.addWinGo(3);
        await winGoController.handlingWinGo1P(3);
        const [winGo1] = await connection.execute('SELECT * FROM `wingo` WHERE `game` = "wingo3" ORDER BY `id` DESC LIMIT 2 ', []);
        const data = winGo1; // Cầu mới chưa có kết quả
        io.emit('data-server', { data: data });

        await k5Controller.add5D(3);
        await k5Controller.handling5D(3);
        const [k5D] = await connection.execute('SELECT * FROM 5d WHERE `game` = 3 ORDER BY `id` DESC LIMIT 2 ', []);
        const data2 = k5D; // Cầu mới chưa có kết quả
        io.emit('data-server-5d', { data: data2, 'game': '3' });

        await k3Controller.addK3(3);
        await k3Controller.handlingK3(3);
        const [k3] = await connection.execute('SELECT * FROM k3 WHERE `game` = 3 ORDER BY `id` DESC LIMIT 2 ', []);
        const data3 = k3; // Cầu mới chưa có kết quả
        io.emit('data-server-k3', { data: data3, 'game': '3' });
    });

    cron.schedule('*/5 * * * *', async() => {

        // TRX 5 minute game
        await trxController.addTRXGo(5);
        await trxController.handlingTRX1P(5);
        const [trxWin] = await connection.execute('SELECT * FROM `trx` WHERE `game` = "trx5" ORDER BY `id` DESC LIMIT 2 ', []);
        const trxWindata = trxWin; // Cầu mới chưa có kết quả
        if (trxWindata && trxWindata.length > 0) {
            io.emit('data-server-trx', { data: trxWindata });
        }


        await winGoController.addWinGo(5);
        await winGoController.handlingWinGo1P(5);
        const [winGo1] = await connection.execute('SELECT * FROM `wingo` WHERE `game` = "wingo5" ORDER BY `id` DESC LIMIT 2 ', []);
        const data = winGo1; // Cầu mới chưa có kết quả
        io.emit('data-server', { data: data });

        await k5Controller.add5D(5);
        await k5Controller.handling5D(5);
        const [k5D] = await connection.execute('SELECT * FROM 5d WHERE `game` = 5 ORDER BY `id` DESC LIMIT 2 ', []);
        const data2 = k5D; // Cầu mới chưa có kết quả
        io.emit('data-server-5d', { data: data2, 'game': '5' });

        await k3Controller.addK3(5);
        await k3Controller.handlingK3(5);
        const [k3] = await connection.execute('SELECT * FROM k3 WHERE `game` = 5 ORDER BY `id` DESC LIMIT 2 ', []);
        const data3 = k3; // Cầu mới chưa có kết quả
        io.emit('data-server-k3', { data: data3, 'game': '5' });
    });
    cron.schedule('*/10 * * * *', async() => {

        // TRX 10 minute game
        await trxController.addTRXGo(10);
        await trxController.handlingTRX1P(10);
        const [trxWin] = await connection.execute('SELECT * FROM `trx` WHERE `game` = "trx10" ORDER BY `id` DESC LIMIT 2 ', []);
        const trxWindata = trxWin; // Cầu mới chưa có kết quả
        if (trxWindata && trxWindata.length > 0) {
            io.emit('data-server-trx', { data: trxWindata });
        }

        await k5Controller.add5D(5);
        await k5Controller.handling5D(5);
        const [k5D] = await connection.execute('SELECT * FROM 5d WHERE `game` = 5 ORDER BY `id` DESC LIMIT 2 ', []);
        const data2 = k5D; // Cầu mới chưa có kết quả
        io.emit('data-server-5d', { data: data2, 'game': '5' });

        await k3Controller.addK3(5);
        await k3Controller.handlingK3(5);
        const [k3] = await connection.execute('SELECT * FROM k3 WHERE `game` = 5 ORDER BY `id` DESC LIMIT 2 ', []);
        const data3 = k3; // Cầu mới chưa có kết quả
        io.emit('data-server-k3', { data: data3, 'game': '5' });
    });

    cron.schedule('*/30 * * * * *', async() => {




        await winGoController.addWinGo(10);
        await winGoController.handlingWinGo1P(10);
        const [winGo1] = await connection.execute('SELECT * FROM `wingo` WHERE `game` = "wingo10" ORDER BY `id` DESC LIMIT 2', []);
        const data = winGo1; // Cầu mới chưa có kết quả
        io.emit('data-server', { data: data });




    });

    cron.schedule('* * 0 * * *', async() => {
        await connection.execute('UPDATE users SET roses_today = ?', [0]);
        await connection.execute('UPDATE point_list SET money = ?', [0]);
    });
    // every day at 11:45 PM
    cron.schedule('45 23 * * *', async() => {
        await mycronController.stakingIncome();
    });


    // every day at 12:08 am
    cron.schedule('52 23 * * *', async() => {
        await mycronController.DailyStakingLevelIncome();
    });
}
cron.schedule('* * * * *', async() => {
    try {
        console.log('Running the cron job...');

        const games = ['wingo', 'wingo3', 'wingo5', 'wingo10'];
        let currentDate = new Date();

        let year = currentDate.getFullYear();
        let month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
        let day = currentDate.getDate().toString().padStart(2, '0');
        let formattedDate = `${year}${month}${day}`;
        let timeNow = currentDate.toISOString().slice(0, 19).replace('T', ' ');
        let currentTime = Date.now();

        for (let game of games) {
            // Fetch the most recent entry for the game sorted by date_time
            const [rows] = await connection.execute(
                `SELECT id, status, period FROM wingo 
                 WHERE game = ? 
                 ORDER BY date_time DESC LIMIT 1`, [game]
            );

            if (rows.length > 0) {
                const { id, status, period: originalPeriod } = rows[0];

                if (status === 1) { // Update status if it's 1
                    await connection.execute(
                        `UPDATE wingo SET status = 0 WHERE id = ?`, [id]
                    );
                    console.log(`Updated status to 0 for game: ${game}`);

                    // Increment the period for the new entry
                    let originalString = originalPeriod.toString();
                    let datePart = originalString.slice(0, 8);
                    let remainingPart = originalString.slice(8);
                    let incrementedPart = (parseInt(remainingPart) + 1).toString();
                    var newPeriod = `${formattedDate}${incrementedPart}`;
                } else {
                    console.log(`No update needed for ${game}, most recent status is already 0`);
                    continue; // Skip inserting a new row if status is already 0
                }
            } else {
                // If no previous entry exists, start with "1" after the date
                var newPeriod = `${formattedDate}1`;
            }

            // Insert a new row for the current game
            const sql = `INSERT INTO wingo (period, amount, game, status, time, date_time) 
                         VALUES (?, ?, ?, ?, ?, ?)`;
            await connection.execute(sql, [Number(newPeriod), 0, game, 0, currentTime, timeNow]);

            console.log(`New record inserted successfully for game: ${game}`);
        }

    } catch (error) {
        console.error('Error running the cron job:', error.message);
    }
});
module.exports = {
    cronJobGame1p
};