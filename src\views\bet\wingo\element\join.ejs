<div class="van-overlay" style="z-index: 2031; display: none;"></div>
<div data-v-a9660e98="" class="popup-join van-popup van-popup--round van-popup--bottom" style="transform: translateY(400px);max-width: 10rem; left: auto; z-index: 2032; display: none;">
  <div data-v-a9660e98="" class="betting-mark colorred">
    <div data-v-a9660e98="" class="head">
      <div data-v-a9660e98="" class="box">
        <div data-v-a9660e98="" class="con">1 Minute</div>
        <div data-v-a9660e98="" class="color" style="color: rgb(109, 167, 244);">Choose<span data-v-a9660e98="" class="p-l-10">Red</span></div>
      </div>
    </div>
    <div data-v-a9660e98="" class="info">
      <div data-v-a9660e98="" class="item c-row c-row-between">
        <div data-v-a9660e98="" class="tit">Amount</div>
        <div data-v-a9660e98="" class="c-row amount-box" data-money="1">
          <div data-x="1" data-v-a9660e98="" class="li" style="background-color: rgb(251, 78, 78); color: rgb(255, 255, 255);">1</div>
          <div data-x="10" data-v-a9660e98="" class="li" style="background-color: rgb(240, 240, 240); color: rgb(0, 0, 0);">10</div>
          <div data-x="100" data-v-a9660e98="" class="li" style="background-color: rgb(240, 240, 240); color: rgb(0, 0, 0);">100</div>
          <div data-x="1000" data-v-a9660e98="" class="li" style="background-color: rgb(240, 240, 240); color: rgb(0, 0, 0);">1000</div>
        </div>
      </div>
      <div data-v-a9660e98="" class="item c-row c-row-between">
        <div data-v-a9660e98="" class="tit">Quantity</div>
        <div data-v-a9660e98="" class="c-row c-row-between stepper-box">
          <div data-v-a9660e98="" class="li minus" style="background-color: rgb(240, 240, 240); color: rgb(200, 201, 204);">-</div>
          <div data-v-a9660e98="" class="digit-box van-cell van-field">
            <div class="van-cell__value van-cell__value--alone van-field__value">
              <div class="van-field__body">
                    <input type="tel" inputmode="numeric" class="van-field__control" oninput="value=value.replace(/\D/g,'')" value="1">
                </div>
            </div>
          </div>
          <div data-v-a9660e98="" class="li plus c-row c-row-middle-center" style="background-color: rgb(251, 78, 78); color: rgb(255, 255, 255);">+</div>
        </div>
      </div>
      <div data-v-a9660e98="" class="item c-row c-flew-end">
        <div data-v-a9660e98="" class="c-row multiple-box">
          <div data-x="1" data-v-a9660e98="" class="li" style="background-color: rgb(251, 78, 78); color: rgb(255, 255, 255);">X1</div>
          <div data-x="5" data-v-a9660e98="" class="li" style="background-color: rgb(240, 240, 240); color: rgb(0, 0, 0);">X5</div>
          <div data-x="10" data-v-a9660e98="" class="li" style="background-color: rgb(240, 240, 240); color: rgb(0, 0, 0);"> X10 </div>
          <div data-x="20" data-v-a9660e98="" class="li" style="background-color: rgb(240, 240, 240); color: rgb(0, 0, 0);"> X20 </div>
          <div data-x="50" data-v-a9660e98="" class="li" style="background-color: rgb(240, 240, 240); color: rgb(0, 0, 0);">X50</div>
          <div data-x="100" data-v-a9660e98="" class="li" style="background-color: rgb(240, 240, 240); color: rgb(0, 0, 0);">X100</div>
        </div>
      </div>
      <div data-v-a9660e98="" class="item c-row c-row-middle">
        <div data-v-a9660e98="" role="checkbox" tabindex="0" aria-checked="true" class="van-checkbox">
          <div class="van-checkbox__icon van-checkbox__icon--square van-checkbox__icon--checked"><i class="van-icon van-icon-success" style="border-color: rgb(244, 69, 62); background-color: rgb(244, 69, 62);">
              <!---->
            </i></div><span class="van-checkbox__label">
            <div data-v-a9660e98="" class="agree p-r-15">Rules</div>
          </span>
        </div><span data-v-a9660e98="" class="txt">Pre-Selling</span>
      </div>
    </div>
    <div data-v-a9660e98="" class="foot c-row">
      <div data-v-a9660e98="" class="left"> Cancel </div>
      <div data-v-a9660e98="" class="right" style="background-color: rgb(251, 78, 78);"><span data-v-a9660e98="" class="p-r-5">Total Amount</span>
        <!----><span data-v-a9660e98="">1 </span>
      </div>
    </div>
  </div>
</div>