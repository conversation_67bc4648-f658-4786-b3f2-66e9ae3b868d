div.dt-button-collection {
  overflow: visible !important;
  z-index: 2002 !important;
}
div.dt-button-collection div.dtsb-searchBuilder {
  width: 99% !important;
  padding-left: 10px !important;
  padding-right: 10px !important;
}

div.dt-button-collection.dtb-collection-closeable div.dtsb-titleRow {
  padding-right: 40px;
}

.dtsb-greyscale {
  border: 1px solid #cecece !important;
}

div.dtsb-logicContainer .dtsb-greyscale {
  border: none !important;
}

div.dtsb-searchBuilder {
  justify-content: space-evenly;
  cursor: default;
  margin-bottom: 1em;
  text-align: left;
}
div.dtsb-searchBuilder button.dtsb-button,
div.dtsb-searchBuilder select {
  font-size: 1em;
}
div.dtsb-searchBuilder div.dtsb-titleRow {
  justify-content: space-evenly;
  margin-bottom: 0.5em;
}
div.dtsb-searchBuilder div.dtsb-titleRow div.dtsb-title {
  display: inline-block;
  padding-top: 6px;
}
div.dtsb-searchBuilder div.dtsb-titleRow div.dtsb-title:empty {
  display: inline;
}
div.dtsb-searchBuilder div.dtsb-titleRow button.dtsb-clearAll {
  float: right;
  margin-bottom: 0.333em;
}
div.dtsb-searchBuilder div.dtsb-vertical .dtsb-value, div.dtsb-searchBuilder div.dtsb-vertical .dtsb-data, div.dtsb-searchBuilder div.dtsb-vertical .dtsb-condition {
  display: block;
}
div.dtsb-searchBuilder div.dtsb-group {
  position: relative;
  clear: both;
  margin-bottom: 0.8em;
}
div.dtsb-searchBuilder div.dtsb-group button.dtsb-clearGroup {
  margin: 2px;
  text-align: center;
  padding: 0;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-logicContainer {
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
  position: absolute;
  margin-top: 0.8em;
  margin-right: 0.8em;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria {
  margin-bottom: 0.8em;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria select.dtsb-dropDown,
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria input.dtsb-input {
  padding: 0.4em;
  margin-right: 0.8em;
  max-width: 20em;
  background-color: rgba(200, 200, 200, 0.3);
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria select.dtsb-dropDown option.dtsb-notItalic,
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria input.dtsb-input option.dtsb-notItalic {
  font-style: normal;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria select.dtsb-italic {
  font-style: italic;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-buttonContainer {
  float: right;
  display: inline-block;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-buttonContainer button.dtsb-delete, div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-buttonContainer button.dtsb-right, div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-buttonContainer button.dtsb-left {
  margin-right: 0.8em;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-buttonContainer button.dtsb-delete:last-child, div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-buttonContainer button.dtsb-right:last-child, div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-buttonContainer button.dtsb-left:last-child {
  margin-right: 0;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria span.dtsp-joiner {
  margin-right: 0.8em;
}

div.dtsb-searchBuilder div.dtsb-titleRow {
  height: 40px;
}
div.dtsb-searchBuilder div.dtsb-titleRow div.dtsb-title {
  padding-top: 10px;
}
div.dtsb-searchBuilder div.dtsb-group button.dtsb-clearGroup {
  margin-right: 8px;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria .form-control {
  width: auto;
  display: inline-block;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria select.dtsb-condition {
  border-color: #28a745;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria select.dtsb-data {
  border-color: #dc3545;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria select.dtsb-value, div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria input.dtsb-value {
  border-color: #007bff;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-logicContainer {
  border-radius: 4px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: flex-start;
  margin-top: 10px;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-logicContainer button.dtsb-logic {
  border: none;
  border-radius: 0px;
  flex-grow: 1;
  flex-shrink: 0;
  flex-basis: 3em;
  margin: 0px;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-logicContainer button.dtsb-clearGroup {
  border: none;
  border-radius: 0px;
  width: 2em;
  margin: 0px;
}

div.dt-button-collection div.dtsb-searchBuilder {
  padding-left: 10px;
  padding-right: 10px;
}
