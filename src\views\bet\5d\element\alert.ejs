<div class="van-overlay" style="z-index: 2031; display: none;"></div>
<%- include('support.ejs') -%>
<div id="box-join" data-v-42f27458="" class="van-popup van-popup--round van-popup--bottom" style="max-width: 10rem; left: auto; z-index: 2032;transform: translateY(1000px)">
    <div data-v-42f27458="" class="betting-mark">
        <div data-v-42f27458="" class="bet-box">
            <div data-v-42f27458="" class="bet-mask p-l-10 p-r-10">
                <div data-v-42f27458="" class="flex-row-start bet-type-group a-b-c-d-e">
                    <div data-v-42f27458="" data-join="a" class="d5-bet-type active">A</div>
                    <div data-v-42f27458="" data-join="b" class="d5-bet-type">B</div>
                    <div data-v-42f27458="" data-join="c" class="d5-bet-type">C</div>
                    <div data-v-42f27458="" data-join="d" class="d5-bet-type">D</div>
                    <div data-v-42f27458="" data-join="e" class="d5-bet-type">E</div>
                    <div data-v-42f27458="" data-join="total" class="d5-bet-type">SUM</div>
                </div>
                <div data-v-42f27458="">
                    <div data-v-42f27458="" class="flex-row-between small-big">
                        <div data-v-42f27458="" data-join="active1" join="b" class="bet-type-btn flex-row-between">
                            Big <span data-v-42f27458="">2</span>
                        </div>
                        <div data-v-42f27458="" data-join="active2" join="s" class="bet-type-btn flex-row-between">
                            Small <span data-v-42f27458="">2</span>
                        </div>
                        <div data-v-42f27458="" data-join="active3" join="l" class="bet-type-btn flex-row-between">
                            Odd <span data-v-42f27458="">2</span>
                        </div>
                        <div data-v-42f27458="" data-join="active4" join="c" class="bet-type-btn flex-row-between">
                            Even <span data-v-42f27458="">2</span>
                        </div>
                    </div>
                    <div data-v-42f27458="" class="c-row c-flex-warp c-row-center 0-9">
                        <div data-v-42f27458="" txt="0" class="c-row c-row-middle-center bet-num-line">
                            <div data-v-42f27458="" class="bet-box-num">
                                0
                                <div data-v-42f27458="">9</div>
                            </div>
                        </div>
                        <div data-v-42f27458="" txt="1" class="c-row c-row-middle-center bet-num-line">
                            <div data-v-42f27458="" class="bet-box-num">
                                1
                                <div data-v-42f27458="">9</div>
                            </div>
                        </div>
                        <div data-v-42f27458="" txt="2" class="c-row c-row-middle-center bet-num-line">
                            <div data-v-42f27458="" class="bet-box-num">
                                2
                                <div data-v-42f27458="">9</div>
                            </div>
                        </div>
                        <div data-v-42f27458="" txt="3" class="c-row c-row-middle-center bet-num-line">
                            <div data-v-42f27458="" class="bet-box-num">
                                3
                                <div data-v-42f27458="">9</div>
                            </div>
                        </div>
                        <div data-v-42f27458="" txt="4" class="c-row c-row-middle-center bet-num-line">
                            <div data-v-42f27458="" class="bet-box-num">
                                4
                                <div data-v-42f27458="">9</div>
                            </div>
                        </div>
                        <div data-v-42f27458="" txt="5" class="c-row c-row-middle-center bet-num-line">
                            <div data-v-42f27458="" class="bet-box-num">
                                5
                                <div data-v-42f27458="">9</div>
                            </div>
                        </div>
                        <div data-v-42f27458="" txt="6" class="c-row c-row-middle-center bet-num-line">
                            <div data-v-42f27458="" class="bet-box-num">
                                6
                                <div data-v-42f27458="">9</div>
                            </div>
                        </div>
                        <div data-v-42f27458="" txt="7" class="c-row c-row-middle-center bet-num-line">
                            <div data-v-42f27458="" class="bet-box-num">
                                7
                                <div data-v-42f27458="">9</div>
                            </div>
                        </div>
                        <div data-v-42f27458="" txt="8" class="c-row c-row-middle-center bet-num-line">
                            <div data-v-42f27458="" class="bet-box-num">
                                8
                                <div data-v-42f27458="">9</div>
                            </div>
                        </div>
                        <div data-v-42f27458="" txt="9" class="c-row c-row-middle-center bet-num-line">
                            <div data-v-42f27458="" class="bet-box-num">
                                9
                                <div data-v-42f27458="">9</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div data-v-42f27458="" class="info">
            <div data-v-42f27458="" class="item c-row c-row-between">
                <div data-v-42f27458="" class="tit">Amount</div>
                <div data-v-42f27458="" class="c-row amount-box">
                    <div data-v-42f27458="" value="1" class="action li">1</div>
                    <div data-v-42f27458="" value="10" class="li">10</div>
                    <div data-v-42f27458="" value="100" class="li">100</div>
                    <div data-v-42f27458="" value="1000" class="li">1000</div>
                </div>
            </div>
            <div data-v-42f27458="" class="item c-row c-row-between">
                <div data-v-42f27458="" class="tit">Quantity</div>
                <div data-v-42f27458="" class="c-row c-row-between stepper-box" id="plus-minus">
                    <div data-v-42f27458="" class="li minus">-</div>
                    <div data-v-42f27458="" class="digit-box van-cell van-field">
                        <div class="van-cell__value van-cell__value--alone van-field__value">
                            <div class="van-field__body">
                                <input type="tel" inputmode="numeric" oninput="value=value.replace(/\D/g,'')" value="1" class="van-field__control" id="value-join" />
                            </div>
                        </div>
                    </div>
                    <div data-v-42f27458="" class="li plus c-row c-row-middle-center action">+</div>
                </div>
            </div>
            <div data-v-42f27458="" class="item c-row c-flew-end">
                <div data-v-42f27458="" class="c-row multiple-box">
                    <div data-v-42f27458="" amount="1" class="li action">X1</div>
                    <div data-v-42f27458="" amount="5" class="li">X5</div>
                    <div data-v-42f27458="" amount="10" class="li">X10</div>
                    <div data-v-42f27458="" amount="20" class="li">X20</div>
                    <div data-v-42f27458="" amount="50" class="li">X50</div>
                    <div data-v-42f27458="" amount="100" class="li">X100</div>
                </div>
            </div>
            <div data-v-42f27458="" class="item c-row c-row-middle">
                <div data-v-42f27458="" role="checkbox" tabindex="0" aria-checked="true" class="van-checkbox">
                    <div class="van-checkbox__icon van-checkbox__icon--square van-checkbox__icon--checked">
                        <i class="van-icon van-icon-success"
                            style="border-color: rgb(244, 69, 62);background-color: rgb(244, 69, 62);">
                            <!---->
                        </i>
                    </div>
                    <span class="van-checkbox__label">
                        <div data-v-42f27458="" class="agree p-r-15">I agree to</div>
                    </span>
                </div>
                <span data-v-42f27458="" class="txt" id="quytacs">Rules</span>
            </div>
        </div>
        <div data-v-42f27458="" class="foot c-row">
            <div data-v-42f27458="" class="left canned-alert">Cancel</div>
            <div data-v-42f27458="" class="right">
                <span id="result" list-join="" join="a" value="1"></span>
                <span data-v-42f27458="" class="p-r-5">Total Amount</span>
                <span data-v-42f27458="" id="total">1 TRX </span>
            </div>
        </div>
    </div>
</div>