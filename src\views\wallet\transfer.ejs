<!DOCTYPE html>
<html translate="no" data-dpr="1" style="font-size: 48.16px;">

<head>
    <meta charset="utf-8">
    <meta name="csrf-token" content="" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="robots" content="noindex,nofollow">
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport">
    <title>Transfer</title>
    <link rel="stylesheet" href="./index_files/index-96409872.css">
    <link rel="stylesheet" href="../index_files/index-96409872.css">
    <link href="/css/wallet/main.css" rel="stylesheet" />
    <link href="/css/wallet/chunk_2-1.css" rel="stylesheet" />
    <link href="/css/wallet/chunk_2-2.css" rel="stylesheet" />
    <link href="/css/wallet/chunk_2-3.css" rel="stylesheet" />
    <link rel="shortcut icon" href="/images/bitbug_favicon.ico" type="image/x-icon">
    <style>
        html,
        body {
            height: 100%;
            width: 100%;
            background-color: #090909;
            padding: 0;
            margin: 0;
        }
    </style>
</head>

<body style="font-size: 12px;">
    <div id="app">
        <div data-v-7b283485="" class="mian">
            <div data-v-106b99c8="" data-v-7b283485="" class="navbar">
                <div data-v-106b99c8="" class="navbar-left" onclick="location.href='/wallet'">
                    <div data-v-106b99c8="" class="bank c-row c-row-middle-center">
                        <i class="fa-light fa-angle-left fa-fade fa-xl center" style="color: #FFF"></i>
                    </div>
                </div>
                <div data-v-106b99c8="" class="navbar-title"> Fund Transfer </div>
                <div data-v-106b99c8="" class="navbar-right" onclick="location.href='/keFuMenu'">
                    <div data-v-7b283485="" data-v-106b99c8="" class="c-row">
                        <i class="fa-fade fa center" style="color: #FFF">
                            <img data-v-7b283485="" data-v-106b99c8="" src="/images/audio.webp" class="audio">
                        </i>
                    </div>
                </div>
            </div>
            <div data-v-7b283485="" class="wallet">
                <div data-v-7b283485="" class="wallet-user c-tc">
                    <div data-v-7b283485="" class="c-row c-row-center img">
                        <div data-v-7b283485="" class="van-image" border-radius="60px"
                            style="width: 60px; height: 60px;">
                            <img src="/images/avatar.svg" class="van-image__img">
                        </div>
                    </div>
                    <div data-v-7b283485="" class="name"></div>
                </div>
                <div data-v-7b283485="" class="wallet-box">
                    <div data-v-7b283485="" class="box">
                        <div data-v-7b283485="" class="icon1"></div>
                        <div data-v-7b283485="" class="title">Fund Transfer</div>
                        <div data-v-7b283485="" class="balance m-t-10">
                            <div data-v-7b283485="" class="txt c-row c-row-middle">
                                <img data-v-7b283485="" width="22px" height="18px" src="/images/king (2).png"
                                    class="m-r-5 img"> My balance：
                            </div>
                            <div data-v-7b283485="" class="c-row balanceMoney c-row-middle-center">
                                <div data-v-7b283485="" class="money">
                                    <!---->
                                    <div data-v-7b283485="">₹<span data-v-7b283485="" class="p-l-10">Loading...</span>
                                    </div>
                                </div>
                                <div data-v-7b283485="" id="reload">
                                    <div data-v-7b283485="" class="van-image img m-l-15"
                                        style="width: 25px; height: 25px;">
                                        <img src="/images/reload.png" class="van-image__img">
                                    </div>
                                </div>
                            </div>
                            <div data-v-7b283485="" class="info c-row">
                                <div data-v-7b283485="" class="item">
                                    <div data-v-7b283485="">Total Recharge</div>
                                    <!---->
                                    <div data-v-7b283485="">₹ 0.00</div>
                                </div>
                                <div data-v-7b283485="" class="item">
                                    <div data-v-7b283485="">Total Withdrawal</div>
                                    <!---->
                                    <div data-v-7b283485="">₹ 0.00</div>
                                </div>
                            </div>
                        </div>
                        <div data-v-a0753f48="" class="mian-from">
                            <div data-v-a0753f48="" class="lab">
                                <span data-v-a0753f48=""> </span>
                            </div>
                            <div data-v-a0753f48="" class="item c-row c-row-center first">
                                <div data-v-a0753f48="" class="c-row number">
                                    <span data-v-a0753f48="" class="c-row c-row-middle-center">
                                        <img data-v-a0753f48=""
                                            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAGDSURBVHgB7ZnNTcNAEIXfOOGeEuiAcEFEuVBCJILELXEF0EGcDqAChxtS0gO+QMTNVID7IHgY83Mju4uyzghpPsmnfWvN8/5YmgcYxk5QqPAxv+h3iCcM9NEiUlDJ3L0dpPdVoN7P8914VjMy7BNCNpis5n6ZB5Xifwgw4TSwzi8PQZtXKNJhOj5Jl+W28cQ1mfF2DWU2qKeucacBIjqCMr4anAb+A2ZAGzOgjRnQxgxoYwa0MQPamAFtzIA2ZsDYEWdb5Sk/HxGSHlThapCuim2jXcfMpiNwJS84gy7F9/Mrdoi16SIuhWy8yiWQ7naPwCNEIpoBAi1Op8s0RLvOxwuZMEEEom2hmvklVCurUCIS8VZAbiz5st7CEnCv/rrdEIOIZ4ClFY8Hn6r+/PXEKb7BrlFt3AHHHw5mW/hq8CQ0yQLKEA5uXONOA8Mmm2J4k8K2kHBx7otbg2JWuR4zUc6wR5rih+kq8+mCg+4msSQJ/bjl3Ixkz7/L1h06kknDiMgH3mlqrSokw/4AAAAASUVORK5CYII="
                                            class="mobile" style="height: 0.53333rem; width: 0.53333rem">
                                    </span>
                                    <div data-v-a0753f48="" class="p-l-5">+91</div>
                                </div>
                                <input data-v-a0753f48="" maxlength="16" type="text" placeholder="Phone Number"
                                    style="background: #484848; outline: none">
                                <!-- oninput="value=value.replace(/\D/g,'')" -->
                            </div>
                            <div data-v-a0753f48="" class="item c-row c-row-center">
                                <span data-v-a0753f48="" class="img c-row c-row-middle-center">
                                    <img data-v-a0753f48="" src="/images/wallet2.png" class="" width="20" height="20" />
                                </span>
                                <input data-v-d8986e5e="" data-v-a0753f48="" placeholder="Enter Amount"
                                    style="background: #484848; outline: none">
                            </div>


                            <div data-v-a0753f48="" class="mian-btn">
                                <button data-v-a0753f48=""
                                    class="gradient van-button van-button--default van-button--normal van-button--block van-button--round">
                                    <div data-v-a0753f48="" class="van-button__content">
                                        <span data-v-a0753f48="" class="van-button__text">
                                            <span data-v-a0753f48="">Send Money</span>
                                        </span>
                                    </div>
                                </button>

                            </div>


                            <div class="alert-toast"></div>
                        </div>
                    </div>

                </div>
                <div data-v-7c8bbbf4="" class="promotion">
                    <div data-v-7c8bbbf4="" class="box">
                        <div data-v-7c8bbbf4="" class="tit c-row c-row-between">Transferred Balance</div>
                        <div data-v-7c8bbbf4="" class="table">
                            <div data-v-7c8bbbf4="" class="hd van-row">
                                <div data-v-7c8bbbf4="" class="c-tc van-ellipsis van-col van-col--8">Phone number</div>
                                <div data-v-7c8bbbf4="" class="c-tc van-ellipsis van-col van-col--8">Amount</div>
                                <div data-v-7c8bbbf4="" class="c-tc van-ellipsis van-col van-col--8">Status</div>
                            </div>
                            <div class="bd van-row lisys">
                                <!-- Transaction history data will be dynamically populated here -->
                            </div>
                        </div>
                    </div>
                    <div data-v-7c8bbbf4="" class="promotion">
                        <div data-v-7c8bbbf4="" class="box">
                            <div data-v-7c8bbbf4="" class="tit c-row c-row-between">Received Balance</div>
                            <div data-v-7c8bbbf4="" class="table">
                                <div data-v-7c8bbbf4="" class="hd van-row">
                                    <div data-v-7c8bbbf4="" class="c-tc van-ellipsis van-col van-col--8">Phone number
                                    </div>
                                    <div data-v-7c8bbbf4="" class="c-tc van-ellipsis van-col van-col--8">Amount</div>
                                    <div data-v-7c8bbbf4="" class="c-tc van-ellipsis van-col van-col--8">Date</div>
                                </div>
                                <div class="bd van-row lisys-r">
                                    <!-- Transaction history data will be dynamically populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-3966082f="" class="box">
                    <div data-v-3966082f="" class="list m-b-20">
                        <div data-v-3966082f="" role="feed" class="van-list">
                            <div class="lisys">

                            </div>
                            <div data-v-a9660e98="" class="p-t-5 p-b-5">
                                <div data-v-a9660e98="" class="van-empty">
                                    <div class="van-empty__image">
                                        <img src="/images/empty-image-default.png" />
                                    </div>
                                    <div class="van-list__finished-text">No more Available</div>
                                    <div class="van-list__placeholder"></div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <%- include('../nav') -%>
                </div>
            </div>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
            <script src="/js/client.js"></script>
            <script>
                $.ajax({
                    type: "GET",
                    url: "/api/webapi/GetUserInfo",
                    dataType: "json",
                    success: function (response) {
                        let user = response.data;
                        $('.wallet-user .name').text(user.name_user);
                        $('.money').text("₹" + user.money_user.toFixed(2));
                        $('.balance .item:eq(0) div:eq(1)').text("₹" + response.totalRecharge.toFixed(2));
                        $('.balance .item:eq(1) div:eq(1)').text("₹" + response.totalWithdraw.toFixed(2));
                    }
                });

                function alertMess(text) {
                    let length = $('.alert-toast .msg').length;
                    if (length == 0) {
                        $('.alert-toast').append(
                            `
                    <div data-v-1dcba851="" class="msg">
                        <div data-v-1dcba851="" class="msg-content" style=""> ${text} </div>
                    </div>
                    `
                        );
                        setTimeout(() => {
                            $('.msg').fadeOut();
                            setTimeout(() => {
                                $('.alert-toast .msg').remove();
                            }, 100);
                        }, 1500);
                    }
                }

                function validateForm(phone, amount, text) {
                    if (!amount || !phone) {
                        alertMess(text);
                        return false;
                    } else {
                        return true;
                    }
                }
                $('button').click(async (e) => {
                    e.preventDefault();
                    let phone = $('input:eq(0)').val().trim();
                    let amount = $('input:eq(1)').val().trim();
                    let status = validateForm(phone, amount, 'Please fill in the necessary part.');
                    if (status) {
                        $('.van-overlay').fadeIn(10);
                        $.ajax({
                            type: "POST",
                            url: "/api/webapi/transfer",
                            data: {
                                phone: phone,
                                amount: amount,
                            },
                            dataType: "json",
                            success: function (response) {
                            
                            
                            console.log(response);
                                // if (response.status === true) {
                                // $('.van-overlay').fadeOut(300);
                                // $('.Loading').fadeIn(10);
                                alertMess(response.message);
                                // setTimeout(() => {
                                //     location.href = '/home';
                                // }, 500);
                                // } else {
                                //     // $('.van-overlay').fadeOut(300);
                                //     alertMess(response.message);
                                // }
                            }
                        });
                    }

                });

                function loadTransactionHistory(datas) {
                    let html = datas.map((data) => {
                        return `
            <div data-v-7c8bbbf4="" class="bd van-row">
                <div data-v-7c8bbbf4="" class="c-tc van-ellipsis van-col van-col--8">${data.receiver_phone}</div> 
                <div data-v-7c8bbbf4="" class="c-tc van-col van-col--8">${data.amount}</div>
                <div data-v-7c8bbbf4="" class="c-tc van-col van-col--8">${data.datetime.slice(0, 10)}</div>
            </div>
            `;
                    }).join('');

                    $('.promotion .bd.lisys').html(html);
                }

                $.ajax({
                    type: "GET",
                    url: "/api/webapi/transfer_history",
                    dataType: "json",
                    success: function (response) {
                        if (response.status === true) {
                            loadTransactionHistory(response.datas);
                        }
                    }
                });

                function loadTransactionReceiveHistory(receive) {
                    let html = receive.map((r_data) => {
                        return `
            <div data-v-7c8bbbf4="" class="bd van-row">
                <div data-v-7c8bbbf4="" class="c-tc van-ellipsis van-col van-col--8">${r_data.sender_phone}</div>
                <div data-v-7c8bbbf4="" class="c-tc van-col van-col--8">${r_data.amount}</div>
                <div data-v-7c8bbbf4="" class="c-tc van-col van-col--8">${r_data.datetime.slice(0, 10)}</div>
            </div>
            `;
                    }).join('');

                    $('.promotion .bd.lisys-r').html(html);
                }

                $.ajax({
                    type: "GET",
                    url: "/api/webapi/transfer_history",
                    dataType: "json",
                    success: function (response) {
                        if (response.status === true) {

                            loadTransactionReceiveHistory(response.receive);
                        }

                    }
                });



            </script>


</body>

</html>