<!DOCTYPE html>
<html translate="no" data-dpr="4" style="font-size: 44.64px">

<head>
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="robots" content="noindex,nofollow" />
  <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport" />
  <title>Wingo 5</title>
  <script src="/js/block.js"></script>
  <link rel="stylesheet" href="/css/bet/wingo/main.css" />
  <link rel="stylesheet" href="/css/bet/wingo/chunk-4e7d3985.25c9d2c0.css" />
  <link rel="stylesheet" href="/css/bet/wingo/chunk-4e7d3985.25c9d2c1.css" />
  <link rel="stylesheet" href="/css/bet/wingo/chunk-4e7d3985.25c9d2c2.css" />
  <link rel="stylesheet" href="/css/bet/wingo/chunk-4e7d3985.25c9d2c3.css" />
  <link rel="stylesheet" href="/css/bet/wingo/chunk-4e7d3985.25c9d2c4.css" />
  <link rel="shortcut icon" href="/images/bitbug_favicon.ico" type="image/x-icon">
  <style>
    html,
    body {
      height: 100%;
      width: 100%;
      background-color: #090909;
      padding: 0;
      margin: 0;
    }

    .block-click {
      pointer-events: none;
    }
  </style>

  <style>
    /* The Modal (background) */
    .modal {
      display: blocked;
      /* Hidden by default */
      position: fixed;
      /* Stay in place */
      z-index: 20001;
      /* Sit on top */
      left: 0;
      top: 0;
      width: 100%;
      /* Full width */
      height: 100%;
      /* Full height */
      overflow: auto;
      /* Enable scroll if needed */
    }

    .text-over-image {
      position: absolute;
      /* Position relative to the parent element */
      top: 35%;
      /* Center vertically */
      left: 50%;
      /* Center horizontally */
      transform: translate(-50%, -50%);
      /* Adjust the positioning */
      color: #fff;
      /* Text color */
      font-size: 24px;
      /* Text size */
      font-weight: bold;
      /* Text weight */
      text-align: center;
      /* Center text */
    }

    .text-over-image-l {
      position: absolute;
      /* Position relative to the parent element */
      top: 48%;
      /* Center vertically */
      left: 49%;
      /* Center horizontally */
      transform: translate(-50%, -50%);
      /* Adjust the positioning */
      color: #fff;
      /* Text color */
      font-size: 10px;
      /* Text size */
      font-weight: bold;
      /* Text weight */
      text-align: center;
      /* Center text */
    }

    /* Modal Content */
    .modal-content {
      text-align: -webkit-center;
      position: relative;
      top: 20%;
    }

    .modal-content img {
      width: 280px;
      /* Use this to ensure the image is full width */
      height: auto;
      /* Adjust height automatically */
      display: block;
      /* Change display to block to avoid extra space */
    }

    /* The Close Button */
    .close {
      color: #aaaaaa;
      float: right;
      font-size: 28px;
      font-weight: bold;
    }

    .close:hover,
    .close:focus {
      color: #000;
      text-decoration: none;
      cursor: pointer;
    }

    /* Circle button style */
    .close-btn {
      border: 2px solid #fff;
      /* Black border */
      border-radius: 50%;
      /* Circle shape */
      background-color: transparent;
      /* Transparent background */
      width: 30px;
      /* Width and height should be equal for a circle */
      height: 30px;
      /* Width and height should be equal for a circle */
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      margin-top: 10px;
    }

    .close-btn:hover {
      background-color: #ddd;
      /* Light grey background on hover */
    }

    /* "X" line style */
    .close-x {
      font-size: 35px;
      /* Large font size for the "X" */
      font-family: none;
      line-height: 1;
      /* Tight line height for the "X" */
      color: #fff;
      /* Black color for the "X" */
      padding: 0;
      /* No padding */
      margin: 0;
      /* No margin */
    }

   .text-over-image-2 {
      position: absolute;
      top: 66%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #ff850f;
      font-size: 25px;
      font-weight: bold;
      text-align: center;
    }

    .text-over-image-3 {
      position: absolute;
      top: 70%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #8c4500;
      font-size: 12px;
      font-weight: none;
      text-align: center;
    }

    .btn-boox {
      background: linear-gradient(to bottom, #20d90a, #aae69d);
      border: none;
      color: white;
      padding: 5px 6px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 16px;
      margin: 4px 4px;
      cursor: pointer;
      font-family: monospace;
      border-radius: 5px;
    }
  </style>
</head>

<body style="font-size: 12px">
  <div id="app">
    <div data-v-a9660e98="" class="mian game">
      <div data-v-106b99c8="" data-v-a9660e98="" class="navbar">
        <div data-v-106b99c8="" class="navbar-left" onclick="location.href='/home'">
          <div data-v-a9660e98="" data-v-106b99c8="" class="c-row c-row-middle-center">
            <img data-v-a9660e98="" data-v-106b99c8="" src="/images/back.c3244ab0.png" class="navbar-back" />
          </div>
        </div>
        <div data-v-106b99c8="" class="navbar-title">
          <div data-v-a9660e98="" data-v-106b99c8="" class="c-row c-row-middle-center">
            <img data-v-a9660e98="" data-v-106b99c8="" src="/index_files/h5setting_202401100608011fs2.png" class="logo"
              style="width: 2.98667rem;height: 1.12rem;background-repeat: no-repeat;background-size: 2.98667rem 1.12rem;background-position: center;">
          </div>
        </div>
        <div data-v-106b99c8="" class="navbar-right">
          <div data-v-a9660e98="" data-v-106b99c8="" class="c-row navbarR">
            <div data-v-a9660e98="" data-v-106b99c8="" class="c-row item c-row-middle-center"
              onclick="location.href='/keFuMenu'">
              <img data-v-a9660e98="" data-v-106b99c8="" src="/images/audio.webp" class="item-audio" />
            </div>
            <div data-v-a9660e98="" data-v-106b99c8="" class="c-row item c-row-middle-center">
              <img data-v-a9660e98="" data-v-106b99c8="" src="/images/volume-up-line.webp" class="item-volume" />
            </div>
          </div>
        </div>
      </div>
      <!---------->
      <%- include('element/info.ejs') -%>
        <!---------->
        <div data-v-a9660e98="" class="game-betting">
          <div data-v-a9660e98="" class="tab">
            <%- include('element/e_tab_time.ejs') -%>
          </div>
          <div data-v-a9660e98="" class="content">
            <div data-v-a9660e98="" class="time-box c-row c-row-between m-b-10" style="
            width: calc(100% - 0.69333rem);
            height: 2.66667rem;
            margin: 0.48rem auto 0;
            background-image: url(/assets/png/diban-f8c2e476.png);
            background-repeat: no-repeat;
            background-size: 100% 100%;
            background-position: 0.01333rem center;
            display: -webkit-box;
            display: -webkit-flex;
            display: flex;
            -webkit-box-align: center;
            -webkit-align-items: center;
            align-items: center;
            -webkit-box-pack: center;
            -webkit-justify-content: center;
            justify-content: center;
            position: relative;
            color: #8f5206;">
              <div data-v-a9660e98="" class="info">
                <div data-v-a9660e98="" class="txt">Periods</div>
                <div data-v-a9660e98="" class="number">Loading............</div>
              </div>
              <div data-v-a9660e98="" class="out">
                <div data-v-a9660e98="" class="txt">
                  Time remaining
                </div>
                <div data-v-a9660e98="" class="number c-row c-row-middle c-flew-end">
                  <div data-v-a9660e98="" class="item">0</div>
                  <div data-v-a9660e98="" class="item">0</div>
                  <div data-v-a9660e98="" class="item c-row c-row-middle">
                    :
                  </div>
                  <div data-v-a9660e98="" class="item">0</div>
                  <div data-v-a9660e98="" class="item">0</div>
                </div>
              </div>
            </div>
            <%- include('element/e_bet.ejs') -%>
          </div>
        </div>
        <div data-v-a9660e98="" class="game-list p-b-20">
          <%- include('element/list-order.ejs') -%>
            <%- include('element/join.ejs') -%>

        </div>
        <!---->
        <!---->
        <!---->
        <div data-v-a9660e98="" id="moveBox" class="moveBox" style="display: none">
          <div data-v-a9660e98="" class="moveHead c-row c-row-between c-row-middle">
            <div data-v-a9660e98="" class="close">Close</div>
            <div data-v-a9660e98="">
              <span data-v-a9660e98="">Full Screen</span>
            </div>
          </div>
          <div data-v-a9660e98="" class="long">
            <div data-v-a9660e98="" class="van-tabs van-tabs--line">
              <div class="van-tabs__wrap van-hairline--top-bottom">
                <div role="tablist" class="van-tabs__nav van-tabs__nav--line">
                  <div role="tab" aria-selected="true" class="van-tab van-tab--active">
                    <span class="van-tab__text van-tab__text--ellipsis">Bệt rồng mới nhất</span>
                  </div>
                  <div role="tab" class="van-tab">
                    <span class="van-tab__text van-tab__text--ellipsis">My Bets</span>
                  </div>
                  <div class="van-tabs__line"></div>
                </div>
              </div>
              <div class="van-tabs__content">
                <div data-v-a9660e98="" role="tabpanel" class="van-tab__pane" style="">
                  <div data-v-a9660e98="" class="longlist"></div>
                </div>
                <div data-v-a9660e98="" role="tabpanel" class="van-tab__pane" style="display: none">
                  <!---->
                </div>
              </div>
            </div>
          </div>
        </div>
        <div data-v-a9660e98="" id="sipachp">
          <!-- <audio data-v-a9660e98="" id="voice1" muted="muted">
                        <source data-v-a9660e98="" src="/audio/di1.da40b233.mp3" type="audio/mpeg" />
                    </audio>
                    <audio data-v-a9660e98="" id="voice2" muted="muted">
                        <source data-v-a9660e98="" src="/audio/di2.317de251.mp3" type="audio/mpeg" />
                    </audio> -->
        </div>
        <div data-v-a9660e98="" class="popup-qt van-popup van-popup--center"
          style="width: 80%; border-radius: 10px; max-width: 340px; z-index: 2040;display: none;">
          <div data-v-a9660e98="" class="rule-box">
            <div data-v-a9660e98="" class="title c-row c-row-middle-center">Pre-Selling Rules</div>
            <div data-v-a9660e98="" class="info">
              <div data-v-a9660e98="" class="comment"> To protect users' rights - legitimate interests
                participate in the pre-sale and maintain the normal operating sequence of the pre-sale, the rules
                rules developed under the agreement in accordance with the relevant agreements and rules of
                national laws and regulations. Chapter 1 Definition1.1 Definition of presale: refer to
                A sales model in which a seller offers a bundle of products or services to collect orders
                Consumer ordering through pre-sale and delivery product tools
                goods and services to the seller as agreed in advance. 1.2 The pre-sale model is the "Signed ." model
                "Deposit" is referred to as a fixed amount of pre-sold goods
                available. The "deposit" can participate in mini games and have a chance to win a lot
                profit. Deposits can be exchanged directly for goods but the deposit amount
                Not for exchange.1.3 Pre-sale products: Products provided by the seller
                By using the pre-sale product tool, only pre-sale words are highlighted on the
                the title or on the product detail page, in addition other products do not use
                Pre-sale product tools are not pre-sale products.1.4 Pre-sale system:
                Access to the system of product tools provided to support sales by selling model
                before. 1.5 Pre-sale goods price: is the price of goods before sale. The price of the goods
                Pre-sale consists of two parts: selling price and payment.</div>
              <div data-v-a9660e98="" class="rule-btn c-row m-t-20 c-row-center">
                <button data-v-a9660e98=""
                  class="btn van-button van-button--default van-button--normal van-button--block van-button--round"
                  style="color: rgb(255, 255, 255); background: rgb(242, 65, 59); border-color: rgb(242, 65, 59);">
                  <div data-v-a9660e98="" class="van-button__content">
                    <span data-v-a9660e98="" class="van-button__text">
                      <span data-v-a9660e98="">I Know</span>
                    </span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
        <%- include('element/alert2.ejs') -%>
    </div>
    <div data-v-7692a079="" data-v-a9660e98="" class="Loading c-row c-row-middle-center" style="display: none;">
      <div data-v-7692a079="" class="van-loading van-loading--circular">
        <span data-v-7692a079="" class="van-loading__spinner van-loading__spinner--circular"
          style="-webkit-animation-duration: 1s; animation-duration: 1s;">
          <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 200 200"
            width="200" height="200" preserveAspectRatio="xMidYMid meet"
            style="width: 100%;height: 100%;transform: translate3d(0px, 0px, 0px);content-visibility: visible;">
            <g transform="matrix(-0.5984085202217102,0.8011911511421204,-0.8011911511421204,-0.5984085202217102,239.9599609375,79.72173309326172)"
              style="display: block;">
              <image width="200px" height="200px" preserveAspectRatio="xMidYMid slice"
                xlink:href="/index_files/loadingspinner.png"></image>
            </g>
            <g transform="matrix(0.9408400058746338,0,0,0.9408400058746338,20.969436645507812,79.30152130126953)"
              style="display: block;">
             <!--  <image style="position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);width: 80%;height: 80%;margin-top: 10px;" preserveAspectRatio="xMidYMid slice"
                xlink:href="/index_files/h5setting_202401100608011fs2.png"></image> -->
            </g>
          </svg>
        </span>
        <img src="/index_files/h5setting_202401100608011fs2.png" style="position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);width: 80%;height: 80%;margin-top: 10px;">
      </div>
    </div>
  </div>
  <div id="myModal" class="modal" style="display: none">

    <!-- Modal content -->
    <div class="modal-content">

      <img src="/missningBg-9deda6ac.png" alt="Popup Image">
      <div class="text-over-image">
        <!-- Your text here -->
        <div id="myModal_header">Congratulations</div><br>
      </div>
      <div class="text-over-image-l">
        <!-- Your text here -->
        <div id="lottery_result"  style="    margin-left: 80px;    display: flex;    justify-content: center;    align-items: center;    text-align: center;" >Lottery Result: <span class="btn-boox">Green</span><span class="btn-boox">7</span><span
            class="btn-boox">Big</span></div><br>
      </div>
      <div class="text-over-image-2">
        <!-- Your text here -->
        <div id="myModal_result">Win: 50</div><br>
      </div>
      <div class="text-over-image-3">
        <!-- Your text here -->
        <div id="myModal_result_Period">Period: 1 Minute 1234567890 </div><br>
      </div>
      <div class="close-btn">
        <span class="close-x">&times;</span>
      </div>
    </div>
  </div>

  <script>
    // Get the modal
    var modal = document.getElementById("myModal");

    // Get the element that closes the modal
    var closeBtn = document.querySelector(".close-btn");



    // When the user clicks on the close button, close the modal
    closeBtn.onclick = function () {
      modal.style.display = "none";
    }

    // When the user clicks anywhere outside of the modal, also close it
    window.onclick = function (event) {
      if (event.target == modal) {
        modal.style.display = "none";
      }
    }


  </script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
  <script src="/js/client.js"></script>
  <script src="https://cdn.socket.io/4.4.1/socket.io.min.js"
    integrity="sha384-fKnu0iswBIqkjxrhQCTZ7qlLHOFEgNkRmK2vaO/LbTZSXdJfAu6ewRBdwHPhBo/H"
    crossorigin="anonymous"></script>
  <script src="/js/wingo5.js"></script>
</body>

</html>