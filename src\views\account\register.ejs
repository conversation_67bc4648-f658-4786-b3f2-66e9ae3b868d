<!DOCTYPE html>
<html lang="en" translate="no" data-dpr="1" style="font-size: 40px;">

<head>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link rel="shortcut icon" href="/images/bitbug_favicon.ico" type="image/x-icon">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="google" content="notranslate">
    <meta name="robots" content="noindex,nofollow">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Register</title>
    <link rel="stylesheet" href="./index_files/index-96409872.css">
    <link rel="stylesheet" href="./index_files/modules-96c1e775.css">
    <link rel="stylesheet" href="./index_files/page-activity-d48e62db.css">
    <link rel="stylesheet" href="./index_files/page-home-58543d87.css">
    <link rel="stylesheet" href="./index_files/page-login-ef3d1f88.css">
    <link rel="stylesheet" href="./index_files/page-main-ff2e7571.css">
    <link rel="stylesheet" href="./index_files/index-98687087.css">
</head>

<body style="font-size: 12px;">
    <div id="app" data-v-app="" style="background: #292929">
        <div data-v-1690b988="" class="ar-loading-view"
            style="display: none; --7688837a: &#39;Roboto&#39;, &#39;Inter&#39;, sans-serif;">
            <div data-v-1690b988="" class="loading-wrapper">
                <div data-v-1690b988="" class="loading-animat"><svg xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 200 200" width="200" height="200"
                        preserveAspectRatio="xMidYMid meet"
                        style="width: 100%; height: 100%; transform: translate3d(0px, 0px, 0px); content-visibility: visible;">
                        <defs>
                            <clippath id="__lottie_element_2">
                                <rect width="200" height="200" x="0" y="0"></rect>
                            </clippath>
                            <image
                                href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAKgAAAAsBAMAAAADRO5JAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJFBMVEXzwwBHcEzzwgDzwwD0wwDzwwD0xADzwwDzwwDxwQDzwwD0wwDcY0swAAAADHRSTlP/AD+/In+jY9wQT49dhnwkAAADuUlEQVRIx+WXz08TQRTHh/4GLky7tAUutNaq4QKCJtxaKFh7ag2NopdWSJVwKYJoPLWxAY0XNiTG6EUuJN6oF/8934+Z2S22tCQ1MXEOzM7uzGfevPd9r4OQA7fT7KAzxaATZ0p3d/alPVTox0ih/TQvW8OEXmxUzsql2e3iwkDQG4t9W8kKHK/lWrcPlta/1lrRvvOXRF30bb7p3XZeri6/zJ4mnj3tv8AnVpJ9W0pOHb0Prm5vFUL3m7L/gsSAPn1gn21nUjs/CvZQJSW3X7Srw9apXN3Lnw0deo32r0Ffl8tljtvNcvkxd9Q2RbqG7QDqBer2wFkztXJ+5Gbkbn1QFeW8RN08SNI8haArGp0qLe/IDPXHGnkIA6+DnGvAWEEbHgNt4oMlekBDNkOFKhk0wYGu0jeGhkXAQAkf6wUVVQXllXnRAY25PsmIELaGjuNDtCfUo6E+dSA3tOiGZpQrEUr4k27Q0CLuqaEhWyr/Guik+xCyIsSogWI+1B1okHMf3gTxtAEABbZwXhtCgf2vcw2dxeG9VwzFUwQNdIF30VAf65SgYbAqg6/W6PzoQm9CWu8UFPfYlPKNrT3oNVC0WXSHTrGlPjqKX8bZXt0soWebGCY01C/ldA8omBZk6Ax+z/Ak6XLpnhnV9annldujLugY+pShOfjTZOgURrSuBKhapGOPhpYIQcHmCXEp+tJIKsFQXJO9EGJE7kPWsn1x1CNmcdVI1m+gbfm9J3RMKiiEMlFBaMWk9gRCMiopYQcv65OhozSxOzSgofMa+g12sjU0gNEKZFleniJtBzMb6KcG9t2P33Ydn8I4aRwbp3gITnlANU/IaHiCk3tBjqHin9DxJPzxMRRdxrpyoFE6LkNRXj/T9AmgczBah0gb6DjWUyUpAPgZmleeELYDjVE8GBoVJoMBmlLJXrxS/FYDYwYaEssOlBOTobpCgJSU97HCdYVanKaeQoVijDkSOnTEinF+ztCKhlYJyntku0I/s6XaBqUWA81ohdBJhJISQqPsii5Qb01wmhpdf+mExgw0Zi5MfoJS4Q3KzuiPGEm1NBQriTLI05HvCI071zB7XjkW/NULupdxDNW/SQY63VBQnP0omUzjAoLOUrHpAQ0qx3kTjHnbAZXrCqp+U7FuLRA0Qi+6Q4FF0E8JXUOfdEBlmqBhRlPvIWiYXlyG0nUzBUlu5XK5h66CB6/vuIYreE+16LYKBRL7w1oti/eUDbhe1HbxklHjVv0vLmhXQAf4R+K6zfdXoL8B/7cyv853sCEAAAAASUVORK5CYII=">
                            </image>
                            <image
                                href="data:image/png;base64,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">
                            </image>
                        </defs>
                        <g clip-path="url(#__lottie_element_2)">
                            <g class="ai"
                                transform="matrix(0.9995260834693909,-0.03078274428844452,0.03078274428844452,0.9995260834693909,-3.0308837890625,3.1256637573242188)"
                                opacity="1" style="display: block;">
                                <image width="200px" height="200px" preserveAspectRatio="xMidYMid slice"
                                    href="data:image/png;base64,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">
                                </image>
                            </g>
                            <g class="png"
                                transform="matrix(0.8019599914550781,0,0,0.8019599914550781,32.63536071777344,82.35688018798828)"
                                opacity="1" style="display: block;">
                                <image width="168px" height="44px" preserveAspectRatio="xMidYMid slice"
                                    href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAKgAAAAsBAMAAAADRO5JAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJFBMVEXzwwBHcEzzwgDzwwD0wwDzwwD0xADzwwDzwwDxwQDzwwD0wwDcY0swAAAADHRSTlP/AD+/In+jY9wQT49dhnwkAAADuUlEQVRIx+WXz08TQRTHh/4GLky7tAUutNaq4QKCJtxaKFh7ag2NopdWSJVwKYJoPLWxAY0XNiTG6EUuJN6oF/8934+Z2S22tCQ1MXEOzM7uzGfevPd9r4OQA7fT7KAzxaATZ0p3d/alPVTox0ih/TQvW8OEXmxUzsql2e3iwkDQG4t9W8kKHK/lWrcPlta/1lrRvvOXRF30bb7p3XZeri6/zJ4mnj3tv8AnVpJ9W0pOHb0Prm5vFUL3m7L/gsSAPn1gn21nUjs/CvZQJSW3X7Srw9apXN3Lnw0deo32r0Ffl8tljtvNcvkxd9Q2RbqG7QDqBer2wFkztXJ+5Gbkbn1QFeW8RN08SNI8haArGp0qLe/IDPXHGnkIA6+DnGvAWEEbHgNt4oMlekBDNkOFKhk0wYGu0jeGhkXAQAkf6wUVVQXllXnRAY25PsmIELaGjuNDtCfUo6E+dSA3tOiGZpQrEUr4k27Q0CLuqaEhWyr/Guik+xCyIsSogWI+1B1okHMf3gTxtAEABbZwXhtCgf2vcw2dxeG9VwzFUwQNdIF30VAf65SgYbAqg6/W6PzoQm9CWu8UFPfYlPKNrT3oNVC0WXSHTrGlPjqKX8bZXt0soWebGCY01C/ldA8omBZk6Ax+z/Ak6XLpnhnV9annldujLugY+pShOfjTZOgURrSuBKhapGOPhpYIQcHmCXEp+tJIKsFQXJO9EGJE7kPWsn1x1CNmcdVI1m+gbfm9J3RMKiiEMlFBaMWk9gRCMiopYQcv65OhozSxOzSgofMa+g12sjU0gNEKZFleniJtBzMb6KcG9t2P33Ydn8I4aRwbp3gITnlANU/IaHiCk3tBjqHin9DxJPzxMRRdxrpyoFE6LkNRXj/T9AmgczBah0gb6DjWUyUpAPgZmleeELYDjVE8GBoVJoMBmlLJXrxS/FYDYwYaEssOlBOTobpCgJSU97HCdYVanKaeQoVijDkSOnTEinF+ztCKhlYJyntku0I/s6XaBqUWA81ohdBJhJISQqPsii5Qb01wmhpdf+mExgw0Zi5MfoJS4Q3KzuiPGEm1NBQriTLI05HvCI071zB7XjkW/NULupdxDNW/SQY63VBQnP0omUzjAoLOUrHpAQ0qx3kTjHnbAZXrCqp+U7FuLRA0Qi+6Q4FF0E8JXUOfdEBlmqBhRlPvIWiYXlyG0nUzBUlu5XK5h66CB6/vuIYreE+16LYKBRL7w1oti/eUDbhe1HbxklHjVv0vLmhXQAf4R+K6zfdXoL8B/7cyv853sCEAAAAASUVORK5CYII=">
                                </image>
                            </g>
                        </g>
                    </svg></div>
                <div data-v-1690b988="" class="com__box" style="display: none;">
                    <div class="loading" data-v-1690b988="">
                        <div class="shape shape-1" data-v-1690b988=""></div>
                        <div class="shape shape-2" data-v-1690b988=""></div>
                        <div class="shape shape-3" data-v-1690b988=""></div>
                        <div class="shape shape-4" data-v-1690b988=""></div>
                    </div>
                </div>
            </div>
            <div data-v-1690b988="" class="skeleton-wrapper" style="display: none;">
                <div data-v-1690b988="" class="van-skeleton van-skeleton--animate">
                    <div class="van-skeleton__content">
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 60%;"></div>
                    </div>
                </div>
                <div data-v-1690b988="" class="van-skeleton van-skeleton--animate">
                    <div class="van-skeleton-avatar van-skeleton-avatar--round"></div>
                    <div class="van-skeleton__content">
                        <h3 class="van-skeleton-title"></h3>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 60%;"></div>
                    </div>
                </div>
                <div data-v-1690b988="" class="van-skeleton van-skeleton--animate">
                    <div class="van-skeleton__content">
                        <h3 class="van-skeleton-title"></h3>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 60%;"></div>
                    </div>
                </div>
            </div>
        </div>
        <div data-v-7e82aefe="" class="resgister__C" style="--7688837a: &#39;Roboto&#39;, &#39;Inter&#39;, sans-serif;">
            <div data-v-58c5e826="" data-v-7e82aefe="" class="navbar">
                <div data-v-58c5e826="" class="navbar-fixed" style="background: rgb(63, 63, 63);">
                    <div data-v-58c5e826="" class="navbar__content">

                        <div data-v-58c5e826="" class="navbar__content-left" onclick="location.href='/login'"><i
                                data-v-58c5e826="" class="van-badge__wrapper van-icon van-icon-arrow-left"></i></div>

                        <div data-v-58c5e826="" class="navbar__content-center">
                            <div data-v-58c5e826="" class="headLogo"
                                style="background-image: url(./index_files/h5setting_202401100608011fs2.png);"></div>
                            <div data-v-58c5e826="" class="navbar__content-title"></div>
                        </div>
                        <div data-v-58c5e826="" class="navbar__content-right">
                            <div data-v-187d09fa="" data-v-7e82aefe="">
                                <div data-v-187d09fa="" class="right">
                                    <div data-v-187d09fa="" class="img"><img data-v-187d09fa="" class=""
                                            data-origin="https://www.bigdaddygame2.com/assets/png/en-4b649537.png"
                                            src="./index_files/en-4b649537.png"></div><span data-v-187d09fa=""
                                        class="lang-text">EN</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div data-v-7e82aefe="" data-v-7ee4aaeb="" class="resgister__C-heading">
                <h1 data-v-7e82aefe="" class="resgister__C-heading__title">Register</h1>
                <div data-v-7e82aefe="" class="resgister__C-heading__subTitle"><span data-v-7e82aefe="">Please register
                        by phone number or email</span></div>
            </div>
            <div data-v-7e82aefe="" class="login_container-tab">
                <div data-v-7e82aefe="" class="tab active">
                    <div data-v-7e82aefe="" class="phonebg phoneactive"></div>
                    <div data-v-7e82aefe="" class="font30 phonefont30active">Register your phone</div>
                </div>
            </div>
            <div data-v-7e82aefe="" class="resgister__C-form">
                <div data-v-7e82aefe="" class="tab-content activecontent">
                    <div data-v-23d378a6="" data-v-7e82aefe="" class="register__container">
                        <div data-v-93f53084="" data-v-23d378a6="" class="phoneInput__container">
                            <div data-v-93f53084="" class="phoneInput__container-label"><img data-v-93f53084=""
                                    src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAGDSURBVHgB7ZnNTcNAEIXfOOGeEuiAcEFEuVBCJILELXEF0EGcDqAChxtS0gO+QMTNVID7IHgY83Mju4uyzghpPsmnfWvN8/5YmgcYxk5QqPAxv+h3iCcM9NEiUlDJ3L0dpPdVoN7P8914VjMy7BNCNpis5n6ZB5Xifwgw4TSwzi8PQZtXKNJhOj5Jl+W28cQ1mfF2DWU2qKeucacBIjqCMr4anAb+A2ZAGzOgjRnQxgxoYwa0MQPamAFtzIA2ZsDYEWdb5Sk/HxGSHlThapCuim2jXcfMpiNwJS84gy7F9/Mrdoi16SIuhWy8yiWQ7naPwCNEIpoBAi1Op8s0RLvOxwuZMEEEom2hmvklVCurUCIS8VZAbiz5st7CEnCv/rrdEIOIZ4ClFY8Hn6r+/PXEKb7BrlFt3AHHHw5mW/hq8CQ0yQLKEA5uXONOA8Mmm2J4k8K2kHBx7otbg2JWuR4zUc6wR5rih+kq8+mCg+4msSQJ/bjl3Ixkz7/L1h06kknDiMgH3mlqrSokw/4AAAAASUVORK5CYII="
                                    class="phoneInput__container-label__icon"><span data-v-93f53084="">Phone
                                    number</span></div>
                            <div data-v-93f53084="" class="phoneInput__container-input">
                                <div data-v-6f85c91a="" data-v-93f53084="" class="dropdown">
                                    <div data-v-6f85c91a="" class="dropdown__value"><span
                                            data-v-6f85c91a="">+91</span><i data-v-6f85c91a=""
                                            class="van-badge__wrapper van-icon van-icon-arrow-down"></i></div>
                                    <div data-v-6f85c91a="" class="dropdown__list">
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+1</span> USA</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+66</span> Thailand</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+62</span> Indonesia</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+95</span> Myanmar</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+971</span> United Arab Emirates (‫الإمارات العربية
                                            المتحدة‬‎)</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+54</span> Argentina</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+880</span> Bangladesh (বাংলাদেশ)</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+975</span> Bhutan (འབྲུག)</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+267</span> Botswana</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+1</span> Canada</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+235</span> Chad (Tchad)</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+269</span> Comoros (‫جزر القمر‬‎)</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+20</span> Egypt (‫مصر‬‎)</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+251</span> Ethiopia</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+33</span> France</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+995</span> Georgia (საქართველო)</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+233</span> Ghana (Gaana)</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item active"><span
                                                data-v-6f85c91a="">+91</span> India (भारत)</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+39</span> Italy (Italia)</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+7</span> Kazakhstan (Казахстан)</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+254</span> Kenya</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+231</span> Liberia</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+218</span> Libya (‫ليبيا‬‎)</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+212</span> Morocco (‫المغرب‬‎)</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+977</span> Nepal (नेपाल)</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+234</span> Nigeria</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+92</span> Pakistan (‫پاکستان‬‎)</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+51</span> Peru (Perú)</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+7</span> Russia (Россия)</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+250</span> Rwanda</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+27</span> South Africa</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+94</span> Sri Lanka (ශ්‍රී ලංකාව)</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+249</span> Sudan (‫السودان‬‎)</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+255</span> Tanzania</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+90</span> Turkey (Türkiye)</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+39</span> Vatican City (Città del Vaticano)</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+212</span> Western Sahara (‫الصحراء الغربية‬‎)</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+260</span> Zambia</div>
                                        <div data-v-6f85c91a="" class="dropdown__list-item"><span
                                                data-v-6f85c91a="">+263</span> Zimbabwe</div>
                                    </div>
                                </div>

                                <input data-v-93f53084="" type="text" name="username" id="username"
                                    oninput="value=value.replace(/\D/g,'')" placeholder="Please enter the phone number"
                                    required>

                            </div>
                        </div>
                        <div data-v-23d378a6="" class="tip"></div>
                        <div data-v-57d49070="" data-v-23d378a6="" class="passwordInput__container">
                            <div data-v-57d49070="" class="passwordInput__container-label"><img data-v-57d49070=""
                                    class="passwordInput__container-label__icon"
                                    data-origin="https://www.bigdaddygame2.com/assets/png/password-b827c2b3.png"
                                    src="./index_files/password-b827c2b3.png"><span data-v-57d49070="">Set
                                    password</span></div>
                            <div data-v-57d49070="" class="passwordInput__container-input">

                                <input data-v-57d49070="" name="password" id="password"
                                    οnkeyup="value=value.replace(/[^\w\.\/]/ig,'')" type="password"
                                    placeholder="Please enterSet password" maxlength="32" autocomplete="new-password"
                                    required><img data-v-57d49070="" id="eyeicon"
                                    src="./index_files/eyeInvisible-2fa5c152.png" class="eye" onclick="showPass()">

                            </div>
                        </div>
                        <div data-v-23d378a6="" class="register__container-tip" style="display: none;">
                            <div data-v-23d378a6="" class="tipbg"></div><span data-v-23d378a6="">The password must be at
                                least 8 digits and must contain letters + numbers</span>
                        </div>
                        <div data-v-23d378a6="" class="register__container-invitation">
                            <div data-v-23d378a6="" class="register__container-invitation__label"><img
                                    data-v-23d378a6="" class="register__container-invitation__label-icon"
                                    data-origin="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAL+SURBVHgB7Zg/TNNBFMffu9+PggpKUheMJHXBtcTEpIQB4mRiVAYIGzZMbhodTXRwdXFww8pGQBMG40pNUEZdXJg6ELcqSU2tkf6e71piaNq7vt+P6x/C7zPwo713v3vfu3fv3hUgJuZ0gxCB7ZXFSwoPUkqpMX5BgoBGIAKI+CcI4AdRUAzIL0wvr32HkIQSsL1ye8RTQzOINAYdAAFLf6vD76eXX5fkfYRo5we8oVtRZ1tKWBEKhHgqca3Tzmv0GJ76NSO1Fwn4tj6f4HidgC6hQ/TDy5uDEluRgJ8l7yJ0mfPnLiQldiIBAz4NQ5eRjumDM3CC4zdNAGetVgBlfuzy86vNrhoEohByJoAgeKSfkrTGNhn+axWgEBIgQJyFBBSlhrxKv8ERzlYACZ4DqssiYwr2otUAzbjbA4gc27QrtAVXuAyhnuAwC2kwyZv5CZgzUdFD9Sog2gNHOF0BTqOcXaxpNFklSoNDnK4AAu0Q4FWWYjhFsahtANztAdchxA7CC7uD7pzXnLZNrDcpjfMcnjnyXVGcPpvfN9EYbljkE7gcZpO3Xc8vubujFfR1ZrkPluzCr1pvV98cGZbrpuAe/5O0vC8P5D3OZNcK1jfZGj/l5tNcm2+x0ShIIFjle8Nnuwnd4McCiMCCRzB3PbthnBijgJ3cYgqwusVDpiAMFhEcGlN8CC9BCNjB/UE6uDKZ3dxv1W7exFh9Gtr5+ohLh7PcQBTnNVz4jZbBf2Bq980dKX2MhLdQF/G/ZB5n5yNfSRWiFv6sVZtRADt/3BOTN2jzSkTDHAktQ6gW/**********************************************************/gilLpzz0CUS0amozCggoMHbqLljIZN/mTa1GAVPZd5skvqB0hloYkzdrs7FmISR/Ts8A9ADtPBI+bHcjswqoddYzwJcU6C55nrzJTHbjTTtDcclfrwarM4Rw57C8TYmvmm3Rq0w8WfCRP+RtMR8TE9PIP8Jj6yZh+V37AAAAAElFTkSuQmCC"
                                    src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAL+SURBVHgB7Zg/TNNBFMffu9+PggpKUheMJHXBtcTEpIQB4mRiVAYIGzZMbhodTXRwdXFww8pGQBMG40pNUEZdXJg6ELcqSU2tkf6e71piaNq7vt+P6x/C7zPwo713v3vfu3fv3hUgJuZ0gxCB7ZXFSwoPUkqpMX5BgoBGIAKI+CcI4AdRUAzIL0wvr32HkIQSsL1ye8RTQzOINAYdAAFLf6vD76eXX5fkfYRo5we8oVtRZ1tKWBEKhHgqca3Tzmv0GJ76NSO1Fwn4tj6f4HidgC6hQ/TDy5uDEluRgJ8l7yJ0mfPnLiQldiIBAz4NQ5eRjumDM3CC4zdNAGetVgBlfuzy86vNrhoEohByJoAgeKSfkrTGNhn+axWgEBIgQJyFBBSlhrxKv8ERzlYACZ4DqssiYwr2otUAzbjbA4gc27QrtAVXuAyhnuAwC2kwyZv5CZgzUdFD9Sog2gNHOF0BTqOcXaxpNFklSoNDnK4AAu0Q4FWWYjhFsahtANztAdchxA7CC7uD7pzXnLZNrDcpjfMcnjnyXVGcPpvfN9EYbljkE7gcZpO3Xc8vubujFfR1ZrkPluzCr1pvV98cGZbrpuAe/5O0vC8P5D3OZNcK1jfZGj/l5tNcm2+x0ShIIFjle8Nnuwnd4McCiMCCRzB3PbthnBijgJ3cYgqwusVDpiAMFhEcGlN8CC9BCNjB/UE6uDKZ3dxv1W7exFh9Gtr5+ohLh7PcQBTnNVz4jZbBf2Bq980dKX2MhLdQF/G/ZB5n5yNfSRWiFv6sVZtRADt/3BOTN2jzSkTDHAktQ6gW/**********************************************************/gilLpzz0CUS0amozCggoMHbqLljIZN/mTa1GAVPZd5skvqB0hloYkzdrs7FmISR/Ts8A9ADtPBI+bHcjswqoddYzwJcU6C55nrzJTHbjTTtDcclfrwarM4Rw57C8TYmvmm3Rq0w8WfCRP+RtMR8TE9PIP8Jj6yZh+V37AAAAAElFTkSuQmCC"><span
                                    data-v-23d378a6="">Invite code</span></div>
                            <div data-v-23d378a6="" class="register__container-invitation__input">

                                <input data-v-23d378a6="" name="invite" id="invite" type="text"
                                    auto-complete="new-password" autocomplete="off" name="userNumber"
                                    placeholder="Please enter the invitation code" maxlength="20" required>

                            </div>
                        </div>
                        <div class="alert-toast" style="color:red"></div>
                        <div data-v-23d378a6="" class="register__container-remember">
                            <div data-v-23d378a6="" role="checkbox" class="van-checkbox" tabindex="0"
                                aria-checked="true">
                                <div class="van-checkbox__icon van-checkbox__icon--round van-checkbox__icon--checked"><i
                                        class="van-badge__wrapper van-icon van-icon-success"
                                        style="border-color: rgb(199, 160, 84); background-color: rgb(199, 160, 84);"></i>
                                </div><span class="van-checkbox__label">I have read and agree <span
                                        data-v-23d378a6="">【Privacy Agreement】</span></span>
                            </div>
                        </div>

                        <div data-v-23d378a6="" class="register__container-button" type="submit">
                            <div data-v-7ee4aaeb="" class="mian-btn m-t-40">
                                <button data-v-23d378a6="" data-v-7ee4aaeb="" id="submit"
                                    type="submit">Register</button>
                            </div>
                            <button data-v-23d378a6="" class="login" id="logb" onclick="window.location='/login'">
                                <div data-v-23d378a6="" class="account" onclick="window.location='/login'">I have an
                                    account</div>
                                <div data-v-23d378a6="" class="loginin" onclick="window.location='/login'">Login</div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="customer" id="customerId"
            style="--7688837a: &#39;Roboto&#39;, &#39;Inter&#39;, sans-serif; --17a7a9f6: bahnschrift;"
            onclick="window.location='/keFuMenu'"><img class=""
                data-origin="https://www.bigdaddygame2.com/assets/png/icon_sevice-1ca64bcf.png"
                src="./index_files/icon_sevice-1ca64bcf.png">
        </div>
    </div>
    <div class="van-overlay" style="z-index: 2003; display: none;">
        <div data-v-7692a079="" data-v-42f27458="" class="Loading c-row c-row-middle-center"
            style="position: fixed;height: 100vh;width: 100vw;top: 0;left: 0;background: rgba(0,0,0,.6);z-index: 99999;">
            <div data-v-7692a079="" class="van-loading van-loading--circular">
                <span data-v-7692a079="" class="van-loading__spinner van-loading__spinner--circular"
                    style="width: auto; height: auto; -webkit-animation-duration: 1s; animation-duration: 1s;">
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                        viewBox="0 0 200 200" width="200" height="200" preserveAspectRatio="xMidYMid meet"
                        style="width: 100%;height: 100%;transform: translate3d(0px, 0px, 0px);content-visibility: visible;">
                        <g transform="matrix(-0.5984085202217102,0.8011911511421204,-0.8011911511421204,-0.5984085202217102,239.9599609375,79.72173309326172)"
                            style="display: block;">
                            <image width="200px" height="200px" preserveAspectRatio="xMidYMid slice"
                                xlink:href="/index_files/loadingspinner.png"></image>
                        </g>
                    </svg>
                </span>
                <img src="/index_files/h5setting_202401100608011fs2.png"
                  style="position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);width: 80%;height: 80%;margin-top: 10px;">
            </div>
        </div>
    </div>
    <script>
        $(document).ready(function () {
            // Select the overlay element
            $('.van-overlay').fadeIn(10);
        });
        $(window).on('load', function () {
            // Select the overlay element
            $('.van-overlay').fadeIn(10);
            setTimeout(function () {
                $('.van-overlay').fadeOut(300);
            }, 600);
        });
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script>
        let i = 1;
        $('.van-checkbox').click(function (e) {
            e.preventDefault();
            if (i == 1) {
                $('.van-checkbox__icon').removeClass('van-checkbox__icon--square van-checkbox__icon--checked');
                document.querySelector('.van-checkbox__icon i').style = '';
            } else {
                i = 0;
                $('.van-checkbox__icon').addClass('van-checkbox__icon--square van-checkbox__icon--checked');
                document.querySelector('.van-checkbox__icon i').style = 'border-color: rgb(244, 69, 62); background-color: rgb(244, 69, 62);';
            }
            i++;
        });

        function setCookie(cname, cvalue, exdays) {
            const d = new Date();
            d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
            let expires = "expires=" + d.toUTCString();
            document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
        }
        function getCookie(cname) {
            let name = cname + "=";
            let decodedCookie = decodeURIComponent(document.cookie);
            let ca = decodedCookie.split(';');
            for (let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) == ' ') {
                    c = c.substring(1);
                }
                if (c.indexOf(name) == 0) {
                    return c.substring(name.length, c.length);
                }
            }
            return "";
        }

        function unsetCookie() {
            setCookie('token', '', 0);
            setCookie('auth', '', 0);
        }
        // fetch('/api/webapi/GetUserInfo')
        //     .then(response => response.json())
        //     .then(data => {
        //         if (data.status === false) {
        //             unsetCookie();
        //             return false;
        //         };
        //         location.href = "/";
        //     });
    </script>
    <script>

        function setCookie(cname, cvalue, exdays) {
            const d = new Date();
            d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
            let expires = "expires=" + d.toUTCString();
            document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
        }

        function alertMess(text) {
            let length = $('.alert-toast .msg').length;
            if (length == 0) {
                $('.alert-toast').append(
                    `
                    <div data-v-1dcba851="" class="msg">
                        <div data-v-1dcba851="" class="msg-content" style=""> ${text} </div>
                    </div>
                    `
                );
                setTimeout(() => {
                    $('.msg').fadeOut();
                    setTimeout(() => {
                        $('.alert-toast .msg').remove();
                    }, 100);
                }, 1500);
            }
        }

        function validateForm(username, password, invite, checkbox, text) {
            if (!username || !invite || !checkbox || !password) {
                alertMess(text);
                return false;
            } else {
                return true;
            }
        }

        $('#logb').click(async (e) => {
            e.preventDefault();
            {
                $('.van-overlay').fadeIn(10);
                if (response.status === true) {
                    $('.van-overlay').fadeOut(300);
                    $('.Loading').fadeIn(10);
                    setTimeout(() => {
                        $('.Loading').fadeOut(10);
                        alertMess(response.message);
                    }, 100);
                    setTimeout(() => {
                        location.href = '/login';
                    }, 900);
                } else {
                    $('.van-overlay').fadeOut(300);
                    alertMess(response.message);
                }
            }
        });

        $('#submit').click(async (e) => {
            e.preventDefault();
            let username = $('#username').val().trim();
            // let otp = $('#otp').val().trim();
            let password = $('#password').val().trim();
            let invite = $('#invite').val().trim();
            //let checkbox = $('.van-checkbox__icon').hasClass('van-checkbox__icon--checked');
            let status = validateForm(username, password, invite, 'Please fill in the required section');
            if (status) {
                $('.van-overlay').fadeIn(10);
                $.ajax({
                    type: "POST",
                    url: "/api/webapi/register",
                    data: {
                        username: username,
                        pwd: password,
                        invitecode: invite,
                        // otp: otp,
                    },
                    dataType: "json",
                    success: function (response) {
                        if (response.status === true) {
                            $('.van-overlay').fadeOut(300);
                            $('.Loading').fadeIn(10);
                            setTimeout(() => {
                                $('.Loading').fadeOut(10);
                                alertMess(response.message);
                            }, 100);
                            setTimeout(() => {
                                location.href = '/login';
                            }, 900);
                        } else {
                            $('.van-overlay').fadeOut(300);
                            alertMess(response.message);
                        }
                    }
                });
            }

        });

        // $('.otp').click(function (e) { 
        //     e.preventDefault();
        //     let phone = $('input:eq(0)').val().trim();
        //     $(this).addClass('block-click');
        //     setTimeout(() => {
        //         $(this).removeClass('block-click');
        //     }, 1600);
        //     if (phone) {
        //         $.ajax({
        //             type: "POST",
        //             url: "/api/sent/otp/verify",
        //             data: {
        //                 phone: phone,
        //             },
        //             dataType: "json",
        //             success: function (response) {
        //                 alertMess(response.message);
        //             }
        //         });
        //     } else {
        //         alertMess('Please fill in the required section');
        //     }
        // });
    </script>
    <script>
        let getWBody = $('.mian').width();

        $('html').css('font-size', `${getWBody / 10}px`);
        $('.van-tabbar .van-tabbar-item').css({
            'transform': 'scale(0.9)',
        });
        $(window).resize(() => {
            let getWBody = $('.mian').width();
            $('html').css('font-size', `${getWBody / 10}px`);
            $('.van-tabbar .van-tabbar-item').css({
                'transform': 'scale(0.9)',
            });
        });
        var url = new URL(window.location.href);
        var c = url.searchParams.get("r_code");
        if (c) {
            $('#invite').val(c);
        }
        function showPass() {
            var x = document.getElementById("password");
            var y = document.getElementById("eyeicon");
            if (x.type === "password") {
                x.type = "text";
                y.src = "./index_files/eyeVisible-7bc1c956.png";
            } else {
                x.type = "password";
                y.src = "./index_files/eyeInvisible-2fa5c152.png";
            }
        }
    </script>
</body>

</html>