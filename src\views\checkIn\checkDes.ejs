<!DOCTYPE html>
<html translate="no" data-dpr="1" style="font-size: 48.24px;">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="robots" content="noindex,nofollow">
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport">
    <title>Check In</title>
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.1/css/all.css">
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.1/css/sharp-thin.css">
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.1/css/sharp-solid.css">
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.1/css/sharp-regular.css">
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.1/css/sharp-light.css">
    <link rel="stylesheet" href="./index_files/index-96409872.css">
    <link href="/css/checkIn/chunk-vendors.css" rel="stylesheet" />
    <link href="/css/checkIn/app.css" rel="stylesheet" />
    <link href="/css/checkIn/chunk.css" rel="stylesheet" />
    <link rel="shortcut icon" href="/images/bitbug_favicon.ico" type="image/x-icon">
    <style>
        html,
        body {
            height: 100%;
            width: 100%;
            background-color: #090909;
            padding: 0;
            margin: 0;
        }

        .mian[data-v-2973c506] {
            background-color: #292929;
            padding-bottom: 1.86667rem
        }

        .mian .audio[data-v-2973c506] {
            width: .66667rem;
            height: .66667rem
        }

        .mian .head-right[data-v-2973c506] {
            border-radius: .26667rem;
            padding: .13333rem
        }

        .mian .check-box .list1[data-v-2973c506] {
            padding: 0 .4rem
        }

        .mian .check-box .list1 .hd[data-v-2973c506] {
            border-top: .05333rem solid #f3c300;
            height: 1.06667rem;
            line-height: 1.01333rem
        }

        .mian .check-box .list1 .hd .item[data-v-2973c506] {
            height: 1.06667rem;
            line-height: 1.01333rem;
            flex: 1;
            text-align: center;
            font-size: .34667rem;
            background-color: #fae59f;
            color: #8f5206
        }

        .mian .check-box .list1 .bd .item[data-v-2973c506] {
            height: .93333rem;
            line-height: .93333rem;
            flex: 1;
            text-align: center;
            font-size: .34667rem;
            background-color: #292929;
            border: .02667rem solid #f3c300;
            margin-bottom: -.02667rem
        }

        .mian .check-box .details[data-v-2973c506] {
            margin-top: .8rem;
            padding: 0 .4rem
        }

        .mian .check-box .details .txt[data-v-2973c506] {
            height: .8rem;
            line-height: .8rem;
            text-align: center;
            font-size: .45333rem;
            font-weight: 600
        }

        .mian .check-box .details .txt .tit[data-v-2973c506] {
            position: relative
        }

        .mian .check-box .details .txt .tit[data-v-2973c506]:after,
        .mian .check-box .details .txt .tit[data-v-2973c506]:before {
            content: "";
            display: block;
            height: .53333rem;
            position: absolute;
            width: 2.13333rem;
            top: 50%;
            transform: translateY(-50%)
        }

        .mian .check-box .details .txt .tit[data-v-2973c506]:after {
            right: -2.4rem;
            background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHEAAAAPCAYAAADTViBhAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAALvSURBVHgB7VjNThRBEK7+2V0gSNiTF0/8mAjPQNiDMcazL6DR6FssrwJe4Some0E9e10TRBI948GAwO5OV/lV98w6G/BIMof+YOn66aoe+uvq7lmi/+DJs+d9/VBG4+FuMyp5IrQDsbf2cIO+fxt+pIzG4gaJNQLJGEOQt1fXH8npyddMZEMxQ+Ljp3H77BtlbwZma3V9g05Php8oo3GYkpgqUGYIhC6qakFC3VpZ30RFDj9TRqMQSay20IrAisd/hMJLhqFtraxtUiayWTAlgbECRbmqO+OZGBGsNYzWKplkaA+1eUKpWAUdBWxLWbqsPTWUDbEw7JqZjThNZ4mJGf2N+rXShdULPUrGIj/C0N+rL4oM2UOAYtE3mBiHoVI8zNoGE8SKZQ5c+lOfgFwWZmOshBBga4lBq7mscxyKAsPasr/2KWILFxcF7NZIUQToyDTBCOg7MYU479hOnEzsRCzshQ+sPmud2PEYeou1tYuL4kYj2L2MRtfSbnf4yl2Lu4LeGbPzXvyflnh/IefnLZmfX+R2+7f86nSkfXYmy8vL/BM55rpduXd8LEe9HlO/H+ddecK/RlptJnGRCk9KNksbBleCVTel3XSVLpCRyFYCSdmK2y6plWNESujIkktbMlX+KcCAm9r1MYLRiGQLeELMoVjMeYgWp+OmpOqlop4rxGfGahHjfGkgTz4m81QUqY1RPoXoEnFYItrOLmArNEmyBYEdTIH3fvauMEpNBz/62+ZW8l/rnznoZc7LK7osQ5zzclGTF4qFWs4u+fstWVoa1WwPUjPE50sSd/S5a9eWKOFCg62U6u+EUvlKWacnKNfQdgcf9t9SRmMQ1yjOuCO8DyppvZqvOh+VRImqoXeDw/03lNEoTG+neKE/Wlnb0B21V11oym21vOXI3uD9wWvKaBxm3hOrigR1Pah6gMf7DljcHRwevKKMRuLGNza1itw26eqHM/DgJWU0Frd+dzo9I4V+gMAXlJGRcbf4C3iLtfBrDRsgAAAAAElFTkSuQmCC) no-repeat 0;
            background-size: 100% auto
        }

        .mian .check-box .details .txt .tit[data-v-2973c506]:before {
            left: -2.4rem;
            background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHEAAAAPCAYAAADTViBhAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAALcSURBVHgB7ZjPbtNAEMZnZl0noVQgRA9wodAUiQgJeIMeSNX22lwR7wG0dd6CGy/QU0ECCQ7lIVIObbkjDqhKgbSxd/lm/SdOC2cM8kjJ7nwzu17vL7O2QlRb5e3xam97Zb23/ae4odoqbRm8CJ/l9t37dHQw+Hg+p4ZYYeuubWwRcYQup4r7LcgaYkWtu9bbJEdbxCxUQFRTkB2A3C9A1hAraCurvRfAtgmACu8cRFJ5+fZihz4fpiBriBUzVOAzIHtOKbwcIKc8ifKW/DMyrcgyYY6iiPf29mQ4HPJodIfn52fl9PQ7n10f8dxwiAnn6WfrRObGlzmOxzw7O0ZeKEkSc9Jqcgvt2dkp/ISbzQYniWXbCDkejyW0Cdsw1HHSpCbFZizWWjaxCDWIktiIc5aDwGKcyIxzbAPDzqKFbkR8XH3jDFtJxAQBfGjIFWHR1iE25YvmC+NaIkbjAs1Acz7Pkc4pOjvmZNFc1Rme5hJ0oVTTuUhjkNDDWNaLQMP8iAp80i+LUWj9OjQ9W4t4jVXH8tAqIH+tVAOMNkY/cc6zchnEcqHlvNwErItkEo94MBgUUDsdolbri2s0vrqbdIPCMNSBdM1HvxFdJTo+Lv+GToqeMcb9QKsA1W/iM8pil7Bbo8LDKrFtCiYszeT7MxM/MHqj4q8f5JomjIuM8kIoyFzdmwAx3UD1kzjPiCkHaBSoyWfRfU00ExF2fp+tekm2dTL50dvibsnD9TeTbrL+rKbSbO6n280phOz+JZP0W66k605TeAKNafpI9fTy/tRZW9vft+5q7yUQPaXJcWqySvXVl6X5PpT+h3c7Uf1MrJjhZeVNe6lzC+X1kErPRLpwlFL//dudSIUaYgXt6HD/9eLSvQWwekAX3061AiMA7OdCDbGidnTwaRcVuYDuI5p6O/UV2C/n1hArbKjI3cU2KpJZj1bSZ2B+hNb2j1l3feMV/gSPqLb/134BlqU8FWVPVvMAAAAASUVORK5CYII=) no-repeat 0;
            background-size: 100% auto
        }

        .mian .check-box .details .con[data-v-2973c506] {
            line-height: .53333rem
        }

        .mian .check-box .details .con .yuan[data-v-2973c506] {
            vertical-align: .05333rem;
            margin-right: .13333rem;
            display: inline-block;
            width: .13333rem;
            height: .13333rem;
            border-radius: .13333rem;
            background: #f3c300
        }

        .mian .popup-box .title[data-v-2973c506] {
            width: 100%;
            height: 1.86667rem;
            padding: .53333rem .53333rem 0;
            color: #fff;
            font-size: .42667rem;
            background: #de0700 url(../img/head_banner.224e7b9d.png) no-repeat 0 0;
            background-size: 100% 100%;
            text-align: center
        }

        .mian .popup-box .title .txt[data-v-2973c506] {
            color: #fff;
            font-size: .45333rem;
            margin-bottom: .10667rem
        }

        .mian .popup-box .title .des[data-v-2973c506] {
            color: #fff;
            font-size: .32rem
        }

        .mian .popup-box .info[data-v-2973c506] {
            padding: .53333rem;
            font-size: .37333rem;
            line-height: .53333rem;
            text-align: center
        }

        .mian .popup-box .info .tit[data-v-2973c506] {
            font-size: .45333rem;
            margin-bottom: .4rem
        }

        .mian .popup-box .info .txt[data-v-2973c506] {
            background: #fff;
            box-shadow: 0 0 .37333rem 0 rgba(242, 65, 59, .09);
            border-radius: .21333rem;
            line-height: .53333rem;
            font-size: .4rem;
            color: #767575;
            text-align: center
        }

        .mian .popup-box .info .txt .des[data-v-2973c506] {
            text-align: center;
            display: block;
            color: #f2413b;
            line-height: .8rem;
            margin-top: .26667rem;
            font-size: .8rem;
            border-radius: .13333rem;
            margin-left: .13333rem
        }

        .mian .popup-box .info .btn[data-v-2973c506] {
            width: 50%;
            border-radius: .8rem;
            box-shadow: 0 0 .21333rem .02667rem rgba(242, 65, 59, .27)
        }
    </style>
</head>

<body style="font-size: 12px;">
    <div id="app">
        <div data-v-2973c506="" class="mian">
            <div data-v-106b99c8="" data-v-2973c506="" class="navbar">
                <div data-v-106b99c8="" class="navbar-left" onclick="location.href='/checkIn'">
                    <div data-v-106b99c8="" class="bank c-row c-row-middle-center">
                        <i class="fa-light fa-angle-left fa-fade fa-xl center" style="color: #FFF"></i>
                    </div>
                </div>
                <div data-v-106b99c8="" class="navbar-title"> Rule </div>
                <div data-v-106b99c8="" class="navbar-right" onclick="location.href='/keFuMenu'">
                    <div data-v-2973c506="" data-v-106b99c8="" class="c-row">
                        <i class="fa-fade fa center" style="color: #FFF">
                            <img data-v-11ffe290="" data-v-106b99c8="" src="/images/audio.webp" class="audio">
                        </i>
                    </div>
                </div>
            </div>
            <div data-v-2973c506="" class="check-box">
                <div data-v-2973c506="" class="list1 m-t-15">
                    <div data-v-2973c506="" class="hd c-row u-row-between">
                        <div data-v-2973c506="" class="item">Target</div>
                        <div data-v-2973c506="" class="item">Accumulated amount</div>
                        <div data-v-2973c506="" class="item">Attendance Rewards</div>
                    </div>
                    <div data-v-2973c506="" class="bd c-row u-row-between">
                        <div data-v-2973c506="" class="item"><span data-v-2973c506="" class="txt">Total charge</span>
                        </div>
                        <div data-v-2973c506="" class="item">300</div>
                        <div data-v-2973c506="" class="item">20</div>
                    </div>
                    <div data-v-2973c506="" class="bd c-row u-row-between">
                        <div data-v-2973c506="" class="item"><span data-v-2973c506="" class="txt">Total charge</span>
                        </div>
                        <div data-v-2973c506="" class="item">3000</div>
                        <div data-v-2973c506="" class="item">50</div>
                    </div>
                    <div data-v-2973c506="" class="bd c-row u-row-between">
                        <div data-v-2973c506="" class="item"><span data-v-2973c506="" class="txt">Total charge</span>
                        </div>
                        <div data-v-2973c506="" class="item">6000</div>
                        <div data-v-2973c506="" class="item">150</div>
                    </div>
                    <div data-v-2973c506="" class="bd c-row u-row-between">
                        <div data-v-2973c506="" class="item"><span data-v-2973c506="" class="txt">Total charge</span>
                        </div>
                        <div data-v-2973c506="" class="item">12000</div>
                        <div data-v-2973c506="" class="item">350</div>
                    </div>
                    <div data-v-2973c506="" class="bd c-row u-row-between">
                        <div data-v-2973c506="" class="item"><span data-v-2973c506="" class="txt">Total charge</span>
                        </div>
                        <div data-v-2973c506="" class="item">28000</div>
                        <div data-v-2973c506="" class="item">850</div>
                    </div>
                    <div data-v-2973c506="" class="bd c-row u-row-between">
                        <div data-v-2973c506="" class="item"><span data-v-2973c506="" class="txt">Total charge</span>
                        </div>
                        <div data-v-2973c506="" class="item">100000</div>
                        <div data-v-2973c506="" class="item">5000</div>
                    </div>
                    <div data-v-2973c506="" class="bd c-row u-row-between">
                        <div data-v-2973c506="" class="item"><span data-v-2973c506="" class="txt">Total charge</span>
                        </div>
                        <div data-v-2973c506="" class="item">2000000</div>
                        <div data-v-2973c506="" class="item">18050</div>
                    </div>
                </div>
                <div data-v-2973c506="" class="details">
                    <div data-v-2973c506="" class="con m-b-5">
                        <!-- <span data-v-2973c506="" class="yuan"></span>Continuous point
                        name , without interruption;</div> -->
                        <div data-v-2973c506="" class="con m-b-5"><span data-v-2973c506="" class="yuan"></span>During
                            the operation period, please check once a day.</div>
                        <div data-v-2973c506="" class="con m-b-5"><span data-v-2973c506="" class="yuan"></span>Players
                            with no deposit history will not be able to claim the bonus.</div>
                        <div data-v-2973c506="" class="con m-b-5"><span data-v-2973c506="" class="yuan"></span>From day
                            1
                            onwards, the deposit requirements must be met.</div>
                        <div data-v-2973c506="" class="con m-b-5"><span data-v-2973c506="" class="yuan"></span>The
                            platform has
                            Final rights to explain this operation. If you have any related questions, please contact
                            customer service.</div>
                    </div>
                </div>
                <div data-v-7692a079="" data-v-2973c506="" class="Loading c-row c-row-middle-center"
                    style="display: none;">
                    <div data-v-7692a079="" class="van-loading van-loading--circular">
                        <span data-v-7692a079="" class="van-loading__spinner van-loading__spinner--circular"
                            style="-webkit-animation-duration: 1s; animation-duration: 1s;">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                viewBox="0 0 200 200" width="200" height="200" preserveAspectRatio="xMidYMid meet"
                                style="width: 100%;height: 100%;transform: translate3d(0px, 0px, 0px);content-visibility: visible;">
                                <g transform="matrix(-0.5984085202217102,0.8011911511421204,-0.8011911511421204,-0.5984085202217102,239.9599609375,79.72173309326172)"
                                    style="display: block;">
                                    <image width="200px" height="200px" preserveAspectRatio="xMidYMid slice"
                                        xlink:href="/index_files/loadingspinner.png"></image>
                                </g>
                                <g transform="matrix(0.9408400058746338,0,0,0.9408400058746338,20.969436645507812,79.30152130126953)"
                                    style="display: block;">
                                    
                                </g>
                            </svg>
                        </span>
                        <img src="/index_files/h5setting_202401100608011fs2.png" style="position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);width: 80%;height: 80%;margin-top: 10px;">
                    </div>
                </div>
            </div>
        </div>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
        <script src="/js/client.js"></script>
</body>

</html>