<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Settings</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <link rel="stylesheet" href="/plugins/fontawesome-free/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="/dist/css/adminlte.min.css">
    <link rel="stylesheet" href="/css/admin.css">
    <link href="//cdn.jsdelivr.net/npm/@sweetalert2/theme-dark@4/dark.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.6.7/axios.min.js" integrity="sha512-NQfB/bDaB8kaSXF8E77JjhHG5PM6XVRxvHzkZiwl3ddWCEPBa23T76MuWSwAJdMGJnmQqM0VeY9kFszsrBEFrQ==" crossorigin="anonymous" referrerpolicy="no-referrer">
    </script>
    <style>
       .form-group {
            margin-top: 20px;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 5px #2ecc71;
        }
        
       .form-group button {
            margin-top: 30px;
        }
    </style>
</head>

<body class="hold-transition sidebar-mini">
    <div class="wrapper">
        <%- include('nav') %>
            <div class="content-wrapper">
                <section class="content-header">
                    <div class="container-fluid">
                        <div class="row mb-2">
                            <div class="col-sm-6">
                                <h1>Settings</h1>
                            </div>
                        </div>
                    </div>
                    <!-- /.container-fluid -->
                </section>

                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-12">
                            <div style="padding: 10px 20px;margin-bottom: 200px;">
                                <div class="form-group">
                                    <div class="text-center">
                                        <label for="quantity">Credit | Debit Wallet Balance</label>
                                    </div>
                                    <input type="text" class="form-control" oninput="value=value.replace(/\D/g,'')" id="buff-id" placeholder="Enter account ID"><br>
                                    <select class="form-select mb-4" id="buff-acc" aria-label="Default select example">
                                        <option selected value="">----------- Select Function ----------</option>
                                        <option value="1">Credit (+)</option>
                                        <option value="2">Debit (-)</option>
                                    </select>

                                    <input type="text" class="form-control" oninput="value=value.replace(/\D/g,'')" id="buff-money" placeholder="Enter Amount">
                                    <button type="submit" class="btn btn-primary" id="buff-username" style="width: 100%;">Submit</button>
                                </div>

                                <div class="form-group">
                                    <div class="text-center">
                                        <label for="telegram">Commission Setting</label>
                                    </div>
                                    <label for="inr_bonus" style="color: #3498db;">INR Deposit Self Bonus(%)</label>
                                    <input type="text" class="form-control" id="inr_bonus" placeholder="INR Deposit Self Bonus(%)" value=""><br>

                                    <label for="usdt_bonus" style="color: #3498db;">USDT Deposit Self Bonus(%)</label>
                                    <input type="text" class="form-control" id="usdt_bonus" placeholder="USDT Deposit Self Bonus(%)" value=""><br>


                                    <label for="referral_bonus" style="color: #3498db;">Referral Bonus(%)</label>
                                    <input type="text" class="form-control" id="referral_bonus" placeholder="Referral Bonus(%)" value=""><br>



                                    <br>

                                    <button type="submit" class="btn btn-primary Commission_setting" style="width: 100%;">Submit</button>
                                </div>

                                <div class="form-group">
                                    <div class="text-center">
                                        <label for="adminPrivateKey">Admin Wallet Private Key</label>
                                    </div>
                                    <input type="password" class="form-control" id="adminPrivateKey" placeholder="Enter admin wallet private key"><br>
                                    <button type="submit" class="btn btn-primary" id="save-admin-key" style="width: 100%;">Save Admin Private Key</button>
                                </div>

                                <div class="form-group">
                                    <div class="text-center">
                                        <label for="quantity">Change Deposit Information</label>
                                    </div>
                                    <label for="quantity" style="color: #3498db;">UPI ADDRESS</label>
                                    <input style="display:none;" type="text" class="form-control" placeholder="Enter Bank name" id="edit-banking-name_bank" value=""><br>
                                    <input style="display:none;" type="text" class="form-control" placeholder="Enter App Name" id="edit-banking-name" value=""><br>
                                    <input type="text" class="form-control" placeholder="Enter Vpa/upi Address" id="edit-banking-info" value=""><br>
                                    <button type="submit" class="btn btn-primary edit-banking" style="width: 100%;margin-bottom: 20px;">Submit</button>
                                    <!---------------------------------------------------------------->
                                    <label for="quantity" style="color: #a50064;">USDT BEP 20</label>
                                    <input style="display:none;" type="text" class="form-control" placeholder="WALLET NAME" id="edit-momo-name_bank" value=""><br>
                                    <input style="display:none;" type="text" class="form-control" placeholder="Wallet Owner Name" id="edit-momo-name" value=""><br>
                                    <input style="display:none;" type="text" class="form-control" placeholder="UPI ID" id="edit-momo-info" value=""><br>
                                    <input type="text" class="form-control" placeholder="USDT Wallet Address" id="edit-momo-qr" value=""><br>
                                    <button type="submit" class="btn btn-primary edit-momo" style="width: 100%;">Submit</button>
                                </div>



                                <div class="form-group">
                                    <div class="text-center">
                                        <label for="telegram">Betting Setting</label>
                                    </div>


                                    <label for="wingo_status" style="color: #3498db;">Wingo Status</label>
                                    <select class="form-control" id="wingo_status">
                                        <option value="Y">ON</option>
                                        <option value="N">OFF</option>
                                    </select>
                                    <br>




                                    <label for="block_second" style="color: #3498db;">TRX Block Second [40-59]</label>
                                    <input type="text" class="form-control" id="block_second" placeholder="TRX Block Set (Second)" value=""><br>

                                    <label for="trx_status" style="color: #3498db;">TRX Status</label>
                                    <select class="form-control" id="trx_status">
                                        <option value="Y">ON</option>
                                        <option value="N">OFF</option>
                                    </select>
                                    <br>

                                    <button type="submit" class="btn btn-primary block_second" style="width: 100%;">Submit</button>
                                </div>

                                <div class="form-group">
                                    <div class="text-center">
                                        <label for="telegram">Telegram</label>
                                    </div>
                                    <label for="telegram" style="color: #3498db;">Telegram</label>
                                    <input type="text" class="form-control" id="telegram" placeholder="Enter Telegram Link" value=""><br>
                                    <label for="cskh" style="color: #3498db;">Online Services</label>
                                    <input type="text" class="form-control" id="cskh" placeholder="Online Services" value=""><br>
                                    <label for="myapp_web" style="color: #3498db;">App Download Link</label>
                                    <input type="text" class="form-control" id="myapp_web" placeholder="Enter App Link" value=""><br>
                                    <button type="submit" class="btn btn-primary telegram" style="width: 100%;">Submit</button>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>
    <script src="/plugins/jquery/jquery.min.js"></script>
    <script src="/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="/dist/js/adminlte.min.js"></script>
    <script src="/js/admin/admin.js"></script>
    <script src="//cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.js"></script>
    <script>
        $.ajax({
            type: "POST",
            url: "/admin/manager/settings/get",
            data: "data",
            dataType: "json",
            success: function(response) {

                console.log(response);
                let bank = response.datas[0];
                let momo = response.datas[1];
                // let momo = response.momo;
                $('#edit-momo-name_bank').val(momo.bank_name);
                $('#edit-momo-name').val(momo.username);
                $('#edit-momo-info').val(momo.upi_id);
                $('#edit-momo-qr').val(momo.usdt_wallet_address);

                // $('#edit-banking-name_bank').val(bank.name_bank);
                // $('#edit-banking-name').val(bank.name_user);
                $('#edit-banking-info').val(bank.stk);
                $('#edit-momo-qr').val(momo.stk);

                $('#telegram').val(response.settings[0].telegram);
                $('#cskh').val(response.settings[0].cskh);
                $('#myapp_web').val(response.settings[0].app);
                $('#block_second').val(response.blockSecond);
                $('#trx_status').val(response.blockTrx);

                $('#inr_bonus').val(response.inr_bonus);
                $('#usdt_bonus').val(response.usdt_bonus);
                $('#referral_bonus').val(response.referral_bonus);


                $('#wingo_status').val(response.wingoStatus);





            }
        });
    </script>
    <script>
        function sendRequest(params1, params2, typer) {
            $.ajax({
                type: "POST",
                url: "/manage/admin/settings",
                data: {
                    params1: params1,
                    params2: params2,
                    typer: typer,
                },
                dataType: "json",
                success: function(response) {
                    if (response.message == 1) {
                        Swal.fire(
                            'Good job!',
                            'You clicked the button!',
                            'success'
                        )
                    } else if (response.message == 2) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Phone number does not exist',
                        })
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'An unknown error !',
                        })
                    }
                }
            });
        }

        function sendRequestBank(name_bank = '', name = '', info = '', qr = '', typer = '') {
            $.ajax({
                type: "POST",
                url: "/admin/manager/settings/bank",
                data: {
                    name_bank: name_bank,
                    name: name,
                    info: info,
                    qr: qr,
                    typer: typer
                },
                dataType: "json",
                success: function(response) {


                    Swal.fire(
                        'Good job!',
                        'Updated Successfully!',
                        'success'
                    )
                }
            });
        }
        $('.buff-money').click(function(e) {
            e.preventDefault();
            const phone = $('#buff-phone').val();
            const money = $('#buff-money').val();
            const checkMoney = $.isNumeric(money);
            if (phone && checkMoney) {
                sendRequest(phone, money, "buff");
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Empty or Wrong Details',
                })
            }
        });

        const alertMessage = (text) => {
            const msg = document.createElement('div');
            msg.setAttribute('data-v-1dcba851', '');
            msg.className = 'msg';

            const msgContent = document.createElement('div');
            msgContent.setAttribute('data-v-1dcba851', '');
            msgContent.className = 'msg-content v-enter-active v-enter-to';
            msgContent.style = '';
            msgContent.textContent = text;

            msg.appendChild(msgContent);
            document.body.appendChild(msg);

            setTimeout(() => {
                msgContent.classList.remove('v-enter-active', 'v-enter-to');
                msgContent.classList.add('v-leave-active', 'v-leave-to');

                setTimeout(() => {
                    document.body.removeChild(msg);
                }, 100);
            }, 1000);
        }

        $('.edit-momo').click(async(e) => {
            try {
                e.preventDefault();
                const bank_name = $('#edit-momo-name_bank').val();
                const username = $('#edit-momo-name').val();
                const upi_id = $('#edit-momo-info').val();
                const usdt_wallet_address = $('#edit-momo-qr').val();

                const response = await axios.post("/admin/manager/settings/bank", {
                    bank_name,
                    username,
                    upi_id,
                    usdt_wallet_address,
                    typer: 'momo'
                })

                Swal.fire(
                    'Good job!',
                    'Updated Successfully!',
                    'success'
                )
                if (response ?.data ?.status === true) {

                    alertMessage(response ?.data ?.message)
                }
            } catch (error) {
                console.log(error)
            }
        });
        $('.edit-banking').click(function(e) {
            e.preventDefault();
            const name_bank = $('#edit-banking-name_bank').val();
            const name = $('#edit-banking-name').val();
            const info = $('#edit-banking-info').val();



            sendRequestBank(name_bank, name, info, "", "bank");
        });
        $('.telegram').click(function(e) {
            e.preventDefault();
            const telegram = $('#telegram').val();
            const cskh = $('#cskh').val();
            const myapp_web = $('#myapp_web').val();
            $.ajax({
                type: "POST",
                url: "/admin/manager/settings/cskh",
                data: {
                    telegram: telegram,
                    cskh: cskh,
                    myapp_web: myapp_web,
                },
                dataType: "json",
                success: function(response) {
                    if (response.status == true) {
                        Swal.fire(
                            'Good job!',
                            'Your Links Updated Successfully!',
                            'success'
                        )
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Empty or Wrong',
                        })
                    }
                }
            });
        });
        $('.block_second').click(function(e) {
            e.preventDefault();
            const block_second = $('#block_second').val();
            const trx_status = $('#trx_status').val();
            const wingo_status = $('#wingo_status').val();
            $.ajax({
                type: "POST",
                url: "/admin/manager/settings/blockSecond",
                data: {
                    block_second: block_second,
                    block_trx: trx_status,
                    wingo_status: wingo_status
                },
                dataType: "json",
                success: function(response) {
                    if (response.status == true) {
                        Swal.fire(
                            'Good job!',
                            'Your Block Updated Successfully!',
                            'success'
                        )
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Empty or Wrong',
                        })
                    }
                }
            });
        });


        $('.Commission_setting').click(function(e) {
            e.preventDefault();
            const inr_bonus = $('#inr_bonus').val();
            const usdt_bonus = $('#usdt_bonus').val();
            const referral_bonus = $('#referral_bonus').val();
            $.ajax({
                type: "POST",
                url: "/admin/manager/settings/commissionSetting",
                data: {
                    inr_bonus: inr_bonus,
                    usdt_bonus: usdt_bonus,
                    referral_bonus: referral_bonus,
                },
                dataType: "json",
                success: function(response) {
                    console.log(response);
                    if (response.status == true) {
                        Swal.fire(
                            'Good job!',
                            'Your Block Updated Successfully!',
                            'success'
                        )
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Empty or Wrong',
                        })
                    }
                }
            });
        });











        $('#buff-username').click(function(e) {
            e.preventDefault();
            let id_user = $('#buff-id').val().trim();
            let buff_acc = $('#buff-acc').val().trim();
            let money_value = $('#buff-money').val().trim();
            if (id_user && buff_acc && money_value) {
                $.ajax({
                    type: "POST",
                    url: "/admin/manager/settings/buff",
                    data: {
                        id_user: id_user,
                        buff_acc: buff_acc,
                        money_value: money_value,
                    },
                    dataType: "json",
                    success: function(response) {
                        if (response.status == true) {
                            Swal.fire(
                                'Good job!',
                                'Data Added Successfully!',
                                'success'
                            )
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'User ID does not exist',
                            })
                        }
                    }
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Please enter complete information',
                })
            }
        });

        // Handle admin private key form submission
        $('#save-admin-key').click(function(e) {
            e.preventDefault();
            const adminPrivateKey = $('#adminPrivateKey').val().trim();

            if (!adminPrivateKey) {
                Swal.fire({
                    icon: 'error',
                    title: 'Please enter the admin private key',
                })
                return;
            }

            $.ajax({
                type: "POST",
                url: "/admin/manager/settings/adminPrivateKey",
                data: {
                    adminPrivateKey: adminPrivateKey
                },
                dataType: "json",
                success: function(response) {
                    if (response.status == true) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            text: 'Admin private key saved successfully!'
                        });
                        // Clear the input field for security
                        $('#adminPrivateKey').val('');
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: response.message || 'Failed to save admin private key'
                        });
                    }
                },
                error: function(error) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'An unexpected error occurred'
                    });
                }
            });
        });
    </script>
</body>

</html>