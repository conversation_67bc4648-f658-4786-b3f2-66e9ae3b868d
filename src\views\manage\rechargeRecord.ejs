<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Recharge Records</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <link rel="stylesheet" href="/plugins/fontawesome-free/css/all.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
  <link href="//cdn.jsdelivr.net/npm/@sweetalert2/theme-dark@4/dark.css" rel="stylesheet">
  <link rel="stylesheet" href="/dist/css/adminlte.min.css">
  <link rel="stylesheet" href="/css/admin.css">
</head>

<body class="hold-transition sidebar-mini">
  <div class="wrapper">
    <%- include('nav') %>
    <div class="content-wrapper">
      <section class="content-header">
        <div class="container-fluid">
          <div class="row mb-2">
            <div class="col-sm-6">
              <h1>Recharge List</h1>
            </div>
          </div>
          
          
          
        </div>
        
        
        
        <!-- /.container-fluid -->
      </section>

      <div class="form-group" style="text-align: center;">
        <input type="text" id="search" placeholder="Enter The Account You Want To Search">
      </div>

      <!-- Main content -->
      <section class="content">
      
      
      
      
      
      
      <div class="row info-box">
              <div class="col-12 col-sm-6 col-md-3" onclick="location.href='/admin/manager/rechargeRecord?type=upi'">
                <div class="info-box mb-3">
                  <span class="info-box-icon elevation-1 bg-success game" style="    font-size: 18px;">UPI</span>

                  <div class="info-box-content">
                    <span class="info-box-text" style="font-size: 18px;    color: rgb(40 167 69);    font-weight: 800;" id="UPISuccess">₹ Loading...</span>
                  </div>
                </div>
              </div>
              <div class="clearfix hidden-md-up"></div>

              <div class="col-12 col-sm-6 col-md-3" onclick="location.href='/admin/manager/rechargeRecord?type=mupi'">
                <div class="info-box mb-3">
                  <span class="info-box-icon elevation-1 bg-primary game" style="    font-size: 18px;">M-UPI</span>

                  <div class="info-box-content">
                    <span class="info-box-text" style="font-size: 18px;    color:rgb(0 123 255);    font-weight: 800;" id="mUPISuccess">₹ Loading...</span>
                  </div>
                </div>
              </div>
              <div class="col-12 col-sm-6 col-md-3" onclick="location.href='/admin/manager/rechargeRecord?type=usdt'">
                <div class="info-box mb-3">
                  <span class="info-box-icon elevation-1 bg-danger game" style="    font-size: 18px;">USDT</span>

                  <div class="info-box-content">
                    <span class="info-box-text" style="font-size: 18px;    color: rgb(220 53 69);    font-weight: 800;" id="mUSDTSuccess">₹ Loading...</span>
                  </div>
                </div>
              </div>
              <div class="col-12 col-sm-6 col-md-3"  >
                <div class="info-box">
                  <span class="info-box-icon elevation-1 bg-warning game" style="    font-size: 18px;">TOTAL</span>

                  <div class="info-box-content">
                    <span class="info-box-text" style="font-size: 18px;    color: rgb(255 193 7);    font-weight: 800;" id="TotalAmount">₹ Loading...</span>
                  </div>
                </div>
              </div>
            </div>
      
      
      
      
      
      
      
      
      
      
      
      
        <!-- Default box -->
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">Recharge List</h3>
            <div class="card-tools">
              <button type="button" class="btn btn-tool" data-card-widget="collapse" title="Collapse">
                <i class="fas fa-minus"></i>
              </button>
              <button type="button" class="btn btn-tool" data-card-widget="remove" title="Remove">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>

          <div class="card-body p-0" style="overflow-y: hidden;">
            <table class="table table-striped projects" id="table1">
              <thead>
                <tr>
                  <th class="text-center">#</th>
                  <th class="text-center">Account</th>
                  <th class="text-center">Type</th>
                  <th class="text-center">Amount</th>
                  <th class="text-center">Code</th>
                  <th class="text-center">UTR</th>
                  <th class="text-center">Time</th>
                  <th class="text-center">Status</th>
                  <!-- <th class="text-center"></th> -->
                </tr>
              </thead>
              <tbody>
                
              </tbody>
            </table>
          </div>
          <nav aria-label="Page navigation example" style="margin-top: 20px;display: flex;justify-content: center;">
            <ul class="pagination table1">
              <li class="page-item previous">
                <a class="page-link" href="#" tabindex="-1">Previous</a>
              </li>
              <div id="numbers" style="display: flex;"></div>
              <li class="page-item next">
                <a class="page-link" href="#">Next</a>
              </li>
            </ul>
          </nav>
        </div>

      </section>
    </div>
  <script src="/plugins/jquery/jquery.min.js"></script>
  <script src="/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="/dist/js/adminlte.min.js"></script>
  <script src="/js/admin/admin.js"></script>
  <script src="//cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.js"></script>
  <script src="/js/admin/tables.js"></script>
  <script>
    function formateT(params) {
    let result = (params < 10) ? "0" + params : params;
    return result;
    }
    
    function timerJoin(params = '', addHours = 0) {
        let date = '';
        if (params) {
            date = new Date(Number(params));
        } else {
            date = new Date();
        }
    
        date.setHours(date.getHours() + addHours);
    
        let years = formateT(date.getFullYear());
        let months = formateT(date.getMonth() + 1);
        let days = formateT(date.getDate());
    
        let hours = date.getHours() % 12;
        hours = hours === 0 ? 12 : hours;
        let ampm = date.getHours() < 12 ? "AM" : "PM";
    
        let minutes = formateT(date.getMinutes());
        let seconds = formateT(date.getSeconds());
    
        return years + '-' + months + '-' + days + ' ' + hours + ':' + minutes + ':' + seconds + ' ' + ampm;
    }
    function show(params) {
      if (params.length == 0) {
        $('tbody').html(`
            <tr class="text-center">
              <td colspan="7">no data...</td>
            </tr>
          `);
        return;
      }
      let html = '';
      params.map((data,index) => {
        console.log(data);
        let displayHTML;
  if(data.type == 'wow_pay') {
    displayHTML = '<b style="color: #3498db">UPI</b>';
  } else if(data.type == 'upi_manual') {
    displayHTML = '<b style="color: #a50064">M-UPI</b>';
  } else {
    displayHTML = '<b style="color: #a50064">USDT</b>';
  }
     let serialNumber = 0 + index + 1;   
        html += `<tr class="text-center">
                  <td id="${data.id}">
                    
                    ${serialNumber} 
                  </td>
                  <td>
                    <b>${data.phone}</b>
                  </td>
                 <td>
              ${displayHTML}
            </td>
                  <td>
                    <b>${(data.money)}</b>
                  </td>
                  <td style="min-width: 190px;">
                    <b>${data.id_order}</b>
                  </td>
                  <td style="min-width: 190px;">
                    <b>${data.utr}</b>
                  </td>
                  <td style="min-width: 190px;">
                    <b>${timerJoin(data.time)}</b>
                  </td>
                  <td class="project-state">
                    <span class="badge badge-${(data.status == 1) ? 'success' : 'danger'}">${(data.status == 1) ? 'Success' : 'Closed'}</span>
                  </td>
                  <!-- <td class="project-actions text-center" style="min-width: 160px;">
                    <a class="btn btn-success btn-sm confirm-btn" href="" data="${data.id}"><i class="fa fa-check"></i></a>
                    <!-- <a class="btn btn-info btn-sm" href="#"><i class="fas fa-pencil-alt"></i></a> -->
                    <a class="btn btn-danger btn-sm delete-btn" href="#" data="${data.id}"><i class="fas fa-trash"></i></a>
                  </td> -->
                </tr>`;
              })
          $('tbody').html(html);
          $('.btn-success').click(function (e) { 
            e.preventDefault();
            let id = $(this).attr('data');
            $.ajax({
              type: "POST",
              url: "/api/webapi/admin/rechargeDuyet",
              data: {
                id: id,
                type: 'confirm'
              },
              dataType: "json",
              success: function (response) {
                Swal.fire(
                  'Good job!',
                  'You clicked the button!',
                  'success'
                )
                setTimeout(() => {
                  location.reload();
                }, 100);
              }
            });
          });
          $('.btn-danger').click(function (e) { 
            e.preventDefault();
            let id = $(this).attr('data');
            $.ajax({
              type: "POST",
              url: "/api/webapi/admin/rechargeDuyet",
              data: {
                id: id,
                type: 'delete'
              },
              dataType: "json",
              success: function (response) {
                setTimeout(() => {
                  location.reload();
                }, 100);
                Swal.fire(
                  'Good job!',
                  'You clicked the button!',
                  'success'
                )
              }
            });
          });
    }
    
    function getUrlParameter(name) {
      name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
      var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
      var results = regex.exec(location.search);
      return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
    }

    // Get 'type' parameter from URL
    var type = getUrlParameter('type');



    
    
    $.ajax({
      type: "POST",
      url: "/api/webapi/admin/recharge",
      data: {
        type:type
      },
      dataType: "json",
      success: function (response) {
      
      
        show(response.datas2);
        $('#UPISuccess').text("₹" + response.UPISuccess.toFixed(2));
        $('#mUPISuccess').text("₹" + response.mUPISuccess.toFixed(2));
        $('#mUSDTSuccess').text("₹" + response.mUSDTSuccess.toFixed(2));
        $('#TotalAmount').text("₹" + response.TotalAmount.toFixed(2));
      }
    });
  </script>
</body>

</html>