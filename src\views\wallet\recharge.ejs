<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="noindex,nofollow">
    <title>Deposit</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.1/css/all.css">
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.1/css/sharp-thin.css">
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.1/css/sharp-solid.css">
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.1/css/sharp-regular.css">
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.1/css/sharp-light.css">
    <link rel="stylesheet" href="../index_files/index-96409872.css">
    <link href="/css/wallet/main.css" rel="stylesheet" />
    <link href="/css/wallet/chunk_2-1.css" rel="stylesheet" />
    <link href="/css/wallet/chunk_2-2.css" rel="stylesheet" />
    <link href="/css/wallet/chunk_2-3.css" rel="stylesheet" />
    <link href="/css/wallet/recharge_styles.css" rel="stylesheet" />
    <link rel="shortcut icon" href="/images/bitbug_favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.6.7/axios.min.js" integrity="sha512-NQfB/bDaB8kaSXF8E77JjhHG5PM6XVRxvHzkZiwl3ddWCEPBa23T76MuWSwAJdMGJnmQqM0VeY9kFszsrBEFrQ==" crossorigin="anonymous" referrerpolicy="no-referrer">
    </script>
</head>

<body>
    <style>
         :root {
            /* Primary Colors */
            --black: #000000;
            --dark-bg: #0c0c0e;
            --panel-bg: #14151a;
            --card-bg: #1c1d24;
            /* Gold Palette - More sophisticated */
            --gold-primary: #d4af37;
            --gold-secondary: #b8860b;
            --gold-light: #f5e7a3;
            --gold-dark: #85754d;
            /* Gradients */
            --gold-gradient: linear-gradient(135deg, var(--gold-dark), var(--gold-primary), var(--gold-light));
            --panel-gradient: linear-gradient(180deg, rgba(28, 29, 36, 0.95), rgba(20, 21, 26, 0.98));
            /* Effects */
            --gold-glow: 0 0 8px rgba(212, 175, 55, 0.4);
            --border-glow: 0 0 4px rgba(212, 175, 55, 0.25);
            --button-glow: 0 0 12px rgba(212, 175, 55, 0.5);
            /* Shadows */
            --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
            --button-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
            /* Spacing */
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --spacing-xl: 32px;
            /* Border Radius */
            --radius-sm: 4px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        html,
        body {
            height: 100%;
            width: 100%;
            overflow-x: hidden;
        }
        
        body {
            font-family: 'Rajdhani', sans-serif;
            background-color: var(--dark-bg);
            background-image: linear-gradient(125deg, rgba(20, 21, 26, 0.95) 0%, rgba(12, 12, 14, 0.98) 100%), url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1IiBoZWlnaHQ9IjUiPgo8cmVjdCB3aWR0aD0iNSIgaGVpZ2h0PSI1IiBmaWxsPSIjMDAwIj48L3JlY3Q+CjxwYXRoIGQ9Ik0wIDVMNSAwWk02IDRMNCA2Wk0tMSAxTDEgLTFaIiBzdHJva2U9IiMyMjIiIHN0cm9rZS13aWR0aD0iMSI+PC9wYXRoPgo8L3N2Zz4=');
            color: rgba(255, 255, 255, 0.95);
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            position: relative;
            font-size: 16px;
            line-height: 1.5;
            letter-spacing: 0.3px;
            font-weight: 400;
        }
        /* Subtle animated overlay */
        
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: radial-gradient(circle at 20% 30%, rgba(212, 175, 55, 0.03) 0%, transparent 15%), radial-gradient(circle at 80% 70%, rgba(212, 175, 55, 0.02) 0%, transparent 20%);
            animation: shimmer 30s infinite ease-in-out;
            z-index: -1;
            opacity: 0.7;
        }
        
        @keyframes shimmer {
            0% {
                background-position: 0% 0%;
            }
            100% {
                background-position: 100% 100%;
            }
        }
        
        .msg {
            position: fixed;
            top: 20px;
            left: 0;
            right: 0;
            display: flex;
            justify-content: center;
            z-index: 9999;
        }
        
        .msg-content {
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid var(--gold);
            color: var(--gold);
            padding: 12px 25px;
            border-radius: 5px;
            box-shadow: var(--gold-glow);
            font-family: 'Orbitron', sans-serif;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .v-enter-active,
        .v-leave-active {
            transition: opacity 0.5s, transform 0.5s;
        }
        
        .v-enter-to {
            opacity: 1;
            transform: translateY(0);
        }
        
        .v-leave-to {
            opacity: 0;
            transform: translateY(-20px);
        }
        
        main {
            display: flex;
            flex-direction: column;
            gap: 20px;
            max-width: 550px;
            margin: 0 auto;
            padding: 20px 15px;
            flex: 1;
        }
        
        header {
            text-align: center;
            padding: var(--spacing-lg) var(--spacing-md);
            position: relative;
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--panel-gradient);
            border-bottom: 1px solid rgba(212, 175, 55, 0.15);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }
        
        header::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60%;
            height: 1px;
            background: var(--gold-gradient);
            opacity: 0.5;
            box-shadow: var(--gold-glow);
        }
        
        header h1 {
            font-family: 'Orbitron', sans-serif;
            font-size: 24px;
            font-weight: 600;
            color: var(--gold-primary);
            text-transform: uppercase;
            letter-spacing: 2px;
            margin: 0;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            position: relative;
            padding-bottom: var(--spacing-xs);
        }
        
        header h1::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 2px;
            background: var(--gold-gradient);
            border-radius: 2px;
        }
        
        .back-button {
            position: absolute;
            left: var(--spacing-md);
            top: 50%;
            transform: translateY(-50%);
            background: rgba(20, 21, 26, 0.7);
            border: 1px solid var(--gold-dark);
            border-radius: var(--radius-sm);
            color: var(--gold-primary);
            font-family: 'Orbitron', sans-serif;
            font-size: 13px;
            font-weight: 500;
            padding: 6px 12px;
            cursor: pointer;
            transition: all 0.25s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            box-shadow: var(--button-shadow);
            backdrop-filter: blur(4px);
        }
        
        .back-button:hover {
            background: rgba(20, 21, 26, 0.9);
            border-color: var(--gold-primary);
            box-shadow: var(--gold-glow), var(--button-shadow);
            transform: translateY(-50%) translateX(2px);
        }
        
        .back-button:active {
            transform: translateY(-50%) scale(0.98);
        }
        
        .back-icon {
            width: 14px;
            height: 14px;
            filter: invert(76%) sepia(41%) saturate(757%) hue-rotate(359deg) brightness(89%) contrast(91%);
            transition: transform 0.2s ease;
        }
        
        .back-button:hover .back-icon {
            transform: translateX(-2px);
            filter: invert(83%) sepia(41%) saturate(757%) hue-rotate(359deg) brightness(95%) contrast(91%);
        }
        
        .crypto-panel {
            background-color: var(--panel-bg);
            background-image: linear-gradient(to bottom, rgba(28, 29, 36, 0.7), rgba(20, 21, 26, 0.9));
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            box-shadow: var(--card-shadow);
            position: relative;
            overflow: hidden;
            margin-bottom: var(--spacing-lg);
            backdrop-filter: blur(5px);
        }
        
        .crypto-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--gold-gradient);
            opacity: 0.8;
        }
        
        .crypto-panel::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at top right, rgba(212, 175, 55, 0.03), transparent 70%);
            pointer-events: none;
        }
        
        .crypto-panel h2 {
            font-family: 'Orbitron', sans-serif;
            color: var(--gold-primary);
            text-align: center;
            margin-bottom: var(--spacing-md);
            font-size: 18px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1.5px;
            position: relative;
            display: inline-block;
        }
        
        .corner {
            position: absolute;
            width: 12px;
            height: 12px;
            border: 1px solid var(--gold-dark);
            z-index: 2;
            opacity: 0.8;
            transition: all 0.5s ease;
        }
        
        .corner-top-left {
            top: -1px;
            left: -1px;
            border-right: none;
            border-bottom: none;
            border-top-left-radius: 2px;
        }
        
        .corner-top-right {
            top: -1px;
            right: -1px;
            border-left: none;
            border-bottom: none;
            border-top-right-radius: 2px;
        }
        
        .corner-bottom-left {
            bottom: -1px;
            left: -1px;
            border-right: none;
            border-top: none;
            border-bottom-left-radius: 2px;
        }
        
        .corner-bottom-right {
            bottom: -1px;
            right: -1px;
            border-left: none;
            border-top: none;
            border-bottom-right-radius: 2px;
        }
        
        .crypto-panel:hover .corner {
            border-color: var(--gold-primary);
            opacity: 1;
        }
        
        @keyframes cornerPulse {
            0% {
                box-shadow: 0 0 0 rgba(212, 175, 55, 0.2);
            }
            50% {
                box-shadow: 0 0 6px rgba(212, 175, 55, 0.4);
            }
            100% {
                box-shadow: 0 0 0 rgba(212, 175, 55, 0.2);
            }
        }
        
        .corner {
            animation: cornerPulse 4s infinite ease-in-out;
        }
        
        .corner-top-left {
            animation-delay: 0s;
        }
        
        .corner-top-right {
            animation-delay: 1s;
        }
        
        .corner-bottom-left {
            animation-delay: 2s;
        }
        
        .corner-bottom-right {
            animation-delay: 3s;
        }
        
        .balance-display {
            margin: var(--spacing-lg) 0;
            text-align: center;
            font-family: 'Orbitron', sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-md);
            background: rgba(0, 0, 0, 0.2);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            border: 1px solid rgba(212, 175, 55, 0.1);
            position: relative;
        }
        
        .balance-display::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(212, 175, 55, 0.05), transparent);
            background-size: 200% 200%;
            animation: shimmerBalance 3s infinite ease-in-out;
            pointer-events: none;
            border-radius: var(--radius-md);
        }
        
        @keyframes shimmerBalance {
            0% {
                background-position: 0% 0%;
            }
            50% {
                background-position: 100% 100%;
            }
            100% {
                background-position: 0% 0%;
            }
        }
        
        .balance-value {
            font-size: 24px;
            font-weight: 600;
            color: var(--gold-primary);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            position: relative;
            padding: 0 var(--spacing-sm);
        }
        
        .balance-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 500;
        }
        
        .reload-button {
            background: rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 6px;
            transition: all 0.25s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .reload-button:hover {
            background: rgba(0, 0, 0, 0.3);
            border-color: var(--gold-primary);
            box-shadow: var(--gold-glow), 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .reload-button:active {
            transform: scale(0.95);
        }
        
        .reload-button img {
            width: 16px;
            height: 16px;
            filter: invert(76%) sepia(41%) saturate(757%) hue-rotate(359deg) brightness(89%) contrast(91%);
            transition: transform 0.3s ease;
        }
        
        .reload-button:hover img {
            transform: rotate(180deg);
            filter: invert(83%) sepia(41%) saturate(757%) hue-rotate(359deg) brightness(95%) contrast(91%);
        }
        
        .payment-methods {
            margin-top: var(--spacing-lg);
        }
        
        .payment-methods-title {
            font-family: 'Orbitron', sans-serif;
            color: var(--gold-primary);
            font-size: 15px;
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-weight: 500;
            letter-spacing: 0.5px;
            position: relative;
            padding-bottom: var(--spacing-xs);
        }
        
        .payment-methods-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 40px;
            height: 1px;
            background: var(--gold-gradient);
            opacity: 0.7;
        }
        
        .payment-methods-title img {
            width: 18px;
            height: 18px;
            filter: invert(76%) sepia(41%) saturate(757%) hue-rotate(359deg) brightness(89%) contrast(91%);
        }
        
        .payment-methods-options {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-md);
            margin-top: var(--spacing-md);
        }
        
        .payment-method-item {
            background-color: rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(212, 175, 55, 0.15);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            flex: 1;
            min-width: 150px;
            cursor: pointer;
            transition: all 0.25s ease;
            position: relative;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }
        
        .payment-method-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, transparent, rgba(212, 175, 55, 0.03), transparent);
            pointer-events: none;
            border-radius: var(--radius-md);
        }
        
        .payment-method-item:hover {
            border-color: var(--gold-dark);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transform: translateY(-2px);
        }
        
        .payment-method-item.action {
            border-color: var(--gold-primary);
            box-shadow: var(--gold-glow), 0 4px 8px rgba(0, 0, 0, 0.2);
            background-color: rgba(212, 175, 55, 0.05);
        }
        
        .payment-method-item img,
        .payment-method-item svg {
            width: 22px;
            height: 22px;
            transition: transform 0.2s ease;
        }
        
        .payment-method-item:hover img,
        .payment-method-item:hover svg {
            transform: scale(1.1);
        }
        
        .payment-method-item .name {
            font-family: 'Rajdhani', sans-serif;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
            font-size: 15px;
            letter-spacing: 0.3px;
        }
        
        .payment-method-item.action .name {
            color: var(--gold-primary);
            font-weight: 600;
        }
        
        .payment-method-item .icon {
            position: absolute;
            right: var(--spacing-md);
            top: 50%;
            transform: translateY(-50%);
            opacity: 0.7;
            transition: opacity 0.2s ease;
        }
        
        .payment-method-item:hover .icon {
            opacity: 1;
        }
        
        .payment-method-item.action .icon i {
            color: var(--gold-primary) !important;
        }
        
        .amount-input-container {
            margin-top: var(--spacing-xl);
            position: relative;
        }
        
        .amount-input-container::before {
            content: '';
            position: absolute;
            top: -15px;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, rgba(212, 175, 55, 0.1), transparent);
        }
        
        .deposit-info-title {
            font-family: 'Orbitron', sans-serif;
            color: var(--gold-primary);
            font-size: 16px;
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-weight: 500;
            letter-spacing: 0.8px;
            position: relative;
            padding-bottom: var(--spacing-sm);
            border-bottom: 1px solid rgba(212, 175, 55, 0.15);
        }
        
        .deposit-info-title::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 60px;
            height: 2px;
            background: var(--gold-gradient);
            opacity: 0.8;
        }
        
        .deposit-info-title img {
            width: 20px;
            height: 20px;
            filter: invert(76%) sepia(41%) saturate(757%) hue-rotate(359deg) brightness(89%) contrast(91%);
        }
        
        .notice-text {
            color: rgba(255, 255, 255, 0.7);
            font-size: 13px;
            line-height: 1.6;
            margin-bottom: var(--spacing-sm);
            padding-left: var(--spacing-sm);
            position: relative;
        }
        
        .notice-text:before {
            content: '•';
            position: absolute;
            left: 0;
            color: var(--gold-dark);
        }
        
        .notice-text:first-of-type {
            color: var(--gold-primary);
            font-weight: 500;
        }
        
        .notice-container {
            background: rgba(0, 0, 0, 0.15);
            border-radius: var(--radius-sm);
            padding: var(--spacing-md);
            margin: var(--spacing-md) 0;
            border-left: 2px solid var(--gold-dark);
        }
        
        .deposit-button {
            background: var(--gold-gradient);
            color: rgba(0, 0, 0, 0.9);
            border: none;
            border-radius: var(--radius-md);
            padding: 14px 20px;
            font-family: 'Orbitron', sans-serif;
            font-size: 15px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1.5px;
            cursor: pointer;
            transition: all 0.25s ease;
            box-shadow: var(--button-shadow);
            margin: var(--spacing-lg) 0 var(--spacing-md);
            width: 100%;
            position: relative;
            overflow: hidden;
        }
        
        .deposit-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--button-glow), var(--button-shadow);
        }
        
        .deposit-button:active {
            transform: translateY(1px);
        }
        
        .deposit-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }
        
        .deposit-button:hover::before {
            transform: translateX(100%);
        }
        
        .previous-payment-popup {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.85);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .previous-payment-popup.show {
            opacity: 1;
            display: flex;
        }
        
        .popup-content {
            background-color: var(--panel-bg);
            background-image: linear-gradient(to bottom, rgba(28, 29, 36, 0.95), rgba(20, 21, 26, 0.98));
            border: 1px solid var(--gold-dark);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            max-width: 90%;
            width: 400px;
            text-align: center;
            position: relative;
            box-shadow: var(--card-shadow), 0 0 30px rgba(0, 0, 0, 0.5);
            transform: translateY(20px);
            transition: transform 0.3s ease;
            overflow: hidden;
        }
        
        .previous-payment-popup.show .popup-content {
            transform: translateY(0);
        }
        
        .popup-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--gold-gradient);
            opacity: 0.8;
        }
        
        .popup-content h3 {
            font-family: 'Orbitron', sans-serif;
            color: var(--gold-primary);
            margin-bottom: var(--spacing-lg);
            font-size: 16px;
            font-weight: 500;
            line-height: 1.5;
            letter-spacing: 0.5px;
        }
        
        .popup-button {
            background: var(--gold-gradient);
            color: rgba(0, 0, 0, 0.9);
            border: none;
            border-radius: var(--radius-md);
            padding: 12px 20px;
            font-family: 'Orbitron', sans-serif;
            font-size: 13px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            cursor: pointer;
            transition: all 0.25s ease;
            margin: var(--spacing-md) 0;
            width: 100%;
            box-shadow: var(--button-shadow);
            position: relative;
            overflow: hidden;
        }
        
        .popup-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }
        
        .popup-button:hover::before {
            transform: translateX(100%);
        }
        
        .popup-button.secondary {
            background: transparent;
            border: 1px solid var(--gold-dark);
            color: var(--gold-primary);
            margin-top: var(--spacing-xs);
        }
        
        .popup-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--button-glow), var(--button-shadow);
        }
        
        .popup-button.secondary:hover {
            background-color: rgba(212, 175, 55, 0.05);
            border-color: var(--gold-primary);
        }
        
        @media (max-width: 768px) {
            main {
                padding: var(--spacing-md) var(--spacing-sm);
            }
            .quick-amount-option {
                min-width: calc(50% - var(--spacing-sm));
            }
            .payment-method-item {
                min-width: 100%;
            }
        }
        
        @media (max-width: 480px) {
            header {
                padding: var(--spacing-md) var(--spacing-sm);
            }
            header h1 {
                font-size: 20px;
                letter-spacing: 1.5px;
            }
            .back-button {
                font-size: 12px;
                padding: 5px 8px;
            }
            .balance-display {
                flex-direction: column;
                gap: var(--spacing-xs);
            }
            .balance-value {
                font-size: 22px;
            }
            .balance-label {
                font-size: 13px;
            }
            .amount-input-title,
            .payment-methods-title {
                font-size: 14px;
            }
            .amount-input-box {
                padding: var(--spacing-sm);
            }
            .quick-amount-option {
                padding: 8px 10px;
            }
            .deposit-button {
                padding: 12px 16px;
                font-size: 14px;
            }
        }
    </style>

    <div id="app">
        <main>
            <header>
                <button class="back-button" onclick="location.href='/wallet'">
                    <img class="back-icon" src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0ibHVjaWRlIGx1Y2lkZS1hcnJvdy1sZWZ0Ij48cGF0aCBkPSJtMTIgMTktNy03IDctNyIvPjxwYXRoIGQ9Ik0xOSAxMkg1Ii8+PC9zdmc+">
                    BACK
                </button>
                <h1>DEPOSIT</h1>
            </header>

            <div class="crypto-panel">
                <div class="corner corner-top-left"></div>
                <div class="corner corner-top-right"></div>
                <div class="corner corner-bottom-left"></div>
                <div class="corner corner-bottom-right"></div>

                <div class="balance-display">
                    <span class="balance-label">TOTAL BALANCE:</span>
                    <span class="balance-value">₹ <span id="money_amount_display">Loading...</span></span>
                    <button class="reload-button" id="reload_money_amount_button">
                        <img src="/images/reload.png" alt="Reload">
                    </button>
                </div>

                <div class="payment-methods">
                    <div class="payment-methods-title">
                        <img src="/images/htmls2.png" alt="Payment Method"> PAYMENT METHOD
                    </div>

                    <div class="payment-methods-options" id="payment_methods_options">
                        <div class="payment-method-item" type="Wow_pay" style="display:none;">
                            <img width="20px" height="20px" src="/images/recharge_TRANSFER_red.webp" alt="UPI Auto Pay">
                            <div class="name">UPI Auto Pay</div>
                            <div class="icon">
                                <i class="van-icon van-icon-success" style="color: rgb(255, 255, 255); font-size: 14px;"></i>
                            </div>
                        </div>

                        <div class="payment-method-item action" type="usdt_manual">
                            <svg width="24px" height="24px" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g fill="none"> <circle cx="16" cy="16" r="16" fill="#F3BA2F"></circle> <path fill="#FFF" d="M12.116 14.404L16 10.52l3.886 3.886 2.26-2.26L16 6l-6.144 6.144 2.26 2.26zM6 16l2.26-2.26L10.52 16l-2.26 2.26L6 16zm6.116 1.596L16 21.48l3.886-3.886 2.26 2.259L16 26l-6.144-6.144-.003-.003 2.263-2.257zM21.48 16l2.26-2.26L26 16l-2.26 2.26L21.48 16zm-3.188-.002h.002V16L16 18.294l-2.291-2.29-.004-.004.004-.003.401-.402.195-.195L16 13.706l2.293 2.293z"></path> </g> </g></svg>
                            <div class="name">USDT BEP20</div>
                            <div class="icon">
                                <i class="van-icon van-icon-success" style="color: rgb(255, 255, 255); font-size: 14px;"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="amount-input-container">
                    <div class="deposit-info-title">
                        <img src="/images/htmls2.png" alt="Information">
                        <span>DEPOSIT INFORMATION</span>
                    </div>

                    <div class="notice-container">
                        <div class="notice-text">
                            * Every recharge you get 5% additional bonus
                        </div>
                        <div class="notice-text">
                            You can deposit any amount you wish. The system will automatically credit your account.
                        </div>
                        <div class="notice-text">
                            * Deposits are typically processed within minutes after confirmation.
                        </div>
                        <div class="notice-text">
                            * For any issues with your deposit, please contact customer support.
                        </div>
                    </div>

                    <button id="deposit_fund_btn" class="deposit-button">
                        DEPOSIT FUND
                    </button>
                </div>
            </div>


    </div>
    </div>
    </div>

    <div class="previous-payment-popup" id="previous_payment_popup">
        <div class="popup-content">
            <h3>You have a pending payment left what do you want to do with that?</h3>
            <button class="popup-button" id="pay_now">PAY NOW</button>
            <button class="popup-button secondary" id="cancel_payment">CANCEL AND CREATE NEW DEPOSIT</button>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="/js/client.js"></script>

    <script>
        let MoneyAmountDisplay = document.querySelector("#money_amount_display")
        let ReloadMoneyAmountButton = document.querySelector("#reload_money_amount_button")
        let PaymentMethodOptions = document.querySelector("#payment_methods_options")
        let MoneyAmountInput = document.querySelector("#money_amount_input")
        let QuickMoneyOptions = document.querySelector("#quick_money_options")
        let DepositFundButton = document.querySelector("#deposit_fund_btn")

        const PaymentMethodsMap = {

            MANUAL_PAY: "manual_pay",
            USDT_MANUAL: "usdt_manual",
            WOW_PAY: "Wow_pay",
        }

        const QuickMoneyForRegular = `
            <div data-v-67caa467="" data-money="100" class="li action">
                <span data-v-67caa467="" class="number">100</span>
            </div>
            <div data-v-67caa467="" data-money="500" class="li">
                <span data-v-67caa467="" class="number">500</span>
            </div>
            <div data-v-67caa467="" data-money="1000" class="li">
                <span data-v-67caa467="" class="number">1000</span>
            </div>
            <div data-v-67caa467="" data-money="10000" class="li">
                <span data-v-67caa467="" class="number">10000</span>
            </div>
            <div data-v-67caa467="" data-money="50000" class="li">
                <span data-v-67caa467="" class="number">50000</span>
            </div>
            <div data-v-67caa467="" data-money="100000" class="li">
                <span data-v-67caa467="" class="number">100000</span>
            </div>
        `

        const QuickMoneyForUSDT = `
            <div data-v-67caa467="" data-money="10" class="li action">
                <span data-v-67caa467="" class="number">$10 </span>
            </div>
            <div data-v-67caa467="" data-money="50" class="li">
                <span data-v-67caa467="" class="number">$50</span>
            </div>
            <div data-v-67caa467="" data-money="100" class="li">
                <span data-v-67caa467="" class="number">$100</span>
            </div>
            <div data-v-67caa467="" data-money="500" class="li">
                <span data-v-67caa467="" class="number">$500</span>
            </div>
            <div data-v-67caa467="" data-money="1000" class="li">
                <span data-v-67caa467="" class="number">$1000</span>
            </div>
            <div data-v-67caa467="" data-money="5000" class="li">
                <span data-v-67caa467="" class="number">$5000</span>
            </div>
        `

        let selectedPaymentMethod = ''
        let inputMoney = 0

        const alertMessage = (text) => {
            const msg = document.createElement('div');
            msg.setAttribute('data-v-1dcba851', '');
            msg.className = 'msg';

            const msgContent = document.createElement('div');
            msgContent.setAttribute('data-v-1dcba851', '');
            msgContent.className = 'msg-content v-enter-active v-enter-to';
            msgContent.style = '';
            msgContent.textContent = text;

            msg.appendChild(msgContent);
            document.body.appendChild(msg);

            setTimeout(() => {
                msgContent.classList.remove('v-enter-active', 'v-enter-to');
                msgContent.classList.add('v-leave-active', 'v-leave-to');

                setTimeout(() => {
                    document.body.removeChild(msg);
                }, 100);
            }, 1000);
        }


        const handleSetTotalBalance = async() => {
            MoneyAmountDisplay.textContent = "Loading..."

            try {
                const response = await axios.get('/api/webapi/GetUserInfo');
                const user = response && response.data && response.data.data

                if (user === null || user === undefined || !user.money_user) {
                    throw Error("Something went wrong while fetching the user wallet balance!")
                }

                MoneyAmountDisplay.textContent = user.money_user.toFixed(2)
            } catch (error) {
                console.log(error)
            }

        }

        
        const handleSelectPaymentGateway = (event) => {
            Array.from(PaymentMethodOptions.children).forEach((child) => {
                child.classList.remove("action")
                if (event.currentTarget.getAttribute("type") === child.getAttribute("type")) {
                    child.classList.add("action")
                }
            })

            
        }

        const handleNumericInput = (event) => {
            const key = event.keyCode;
            // Only allow numbers to be entered
            if (key < 48 || key > 57) {
                event.preventDefault();
            }
        }

       

        const handleQuickMoney = (item) => (event) => {
            const amount = parseInt(item.getAttribute("data-money"))

            inputMoney = amount
            MoneyAmountInput.value = amount

            Array.from(QuickMoneyOptions.children).forEach((child) => {
                child.classList.remove("action")
            })

            item.classList.add("action")
        }

        const handleDepositFund = (event) => {
            window.location.href = `/wallet/paynow/manual_usdt`
        }

        const handleUPIGatewayPaymentRequest = async(moneyAmount) => {

            const response = await axios.post("/wallet/paynow/upi", {
                money: moneyAmount
            })

            const data = response && response.data

            if (data === undefined || data === null) {
                alertMessage("Something went wrong!")
                return
            }

            if (data.status === false) {
                alertMessage(data.message || "Unknown error")
                return
            }

            window.location.href = data.urls && data.urls.web_url
        }

        const handleWowPayPaymentRequest = async(moneyAmount) => {
            try {

                const response = await axios.post(`/wallet/paynow/wowpay?money=${moneyAmount}`)

                const data = response && response.data

                if (data === undefined || data === null) {
                    alertMessage("Something went wrong!")
                    return
                }


                if (data.status === false) {
                    alertMessage(data.message || "Unknown error")
                    return
                }

                window.location.href = data.payment_url
            } catch (error) {
                console.log(error)
            }
        }

        const handleManualUSDTPaymentRequest = async(moneyAmount) => {
            window.location.href = `/wallet/paynow/manual_usdt?am=${moneyAmount}`
                // The rest of this function is commented out since we're redirecting directly

            /* Commented out code for future reference
            const response = await axios.post("/wallet/paynow/coinpayment", {
                money: moneyAmount
            })

            const data = response && response.data

            if (data === undefined || data === null) {
                alertMessage("Something went wrong!")
                return
            }

            if (data.status === false) {
                alertMessage(data.message || "Unknown error")
                return
            }
            alert(data.message)
            window.location.href = data.urls && data.urls.web_url
            */
        }

        const handleManualUPIPaymentRequest = async(moneyAmount) => {
            window.location.href = `/wallet/paynow/manual_upi?am=${moneyAmount}`
        }

        const handleUSDTPaymentRequest = async(moneyAmount) => {

        }

        // Below code will run on Page Load
        handleSetTotalBalance()
        
            // --------------------------------

        // Even Listeners
        ReloadMoneyAmountButton.addEventListener("click", handleSetTotalBalance)

        Array.from(PaymentMethodOptions.children).forEach((child) => {
            if (Array.from(child.classList).includes("disabled")) return

            child.addEventListener("click", handleSelectPaymentGateway)
        })



        DepositFundButton.addEventListener("click", handleDepositFund);

        /* Some Event Listeners added in handleCheckAndSelectPaymentMethod function */
        // --------------
    </script>
</body>

</html>