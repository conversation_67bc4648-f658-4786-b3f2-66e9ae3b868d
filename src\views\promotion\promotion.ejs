<!DOCTYPE html>
<html translate="no" data-dpr="1" style="font-size: 48.24px;">

<head>

    <head>
        <meta charset="UTF-8">
        <link rel="icon" type="image/svg+xml" href="/logo.png">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>DeltInWin</title>
        <!-- Foontawsome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer">
        <!-- Bootstrap CSS -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">

        <link rel="stylesheet" crossorigin="" href="/index_files/index-BUBUniRp.css">
        <style id="_goober">
            @keyframes go2264125279 {
                from {
                    transform: scale(0) rotate(45deg);
                    opacity: 0;
                }
                to {
                    transform: scale(1) rotate(45deg);
                    opacity: 1;
                }
            }
            
            @keyframes go3020080000 {
                from {
                    transform: scale(0);
                    opacity: 0;
                }
                to {
                    transform: scale(1);
                    opacity: 1;
                }
            }
            
            @keyframes go463499852 {
                from {
                    transform: scale(0) rotate(90deg);
                    opacity: 0;
                }
                to {
                    transform: scale(1) rotate(90deg);
                    opacity: 1;
                }
            }
            
            @keyframes go1268368563 {
                from {
                    transform: rotate(0deg);
                }
                to {
                    transform: rotate(360deg);
                }
            }
            
            @keyframes go1310225428 {
                from {
                    transform: scale(0) rotate(45deg);
                    opacity: 0;
                }
                to {
                    transform: scale(1) rotate(45deg);
                    opacity: 1;
                }
            }
            
            @keyframes go651618207 {
                0% {
                    height: 0;
                    width: 0;
                    opacity: 0;
                }
                40% {
                    height: 0;
                    width: 6px;
                    opacity: 1;
                }
                100% {
                    opacity: 1;
                    height: 10px;
                }
            }
            
            @keyframes go901347462 {
                from {
                    transform: scale(0.6);
                    opacity: 0.4;
                }
                to {
                    transform: scale(1);
                    opacity: 1;
                }
            }
            
            .go4109123758 {
                z-index: 9999;
            }
            
            .go4109123758>* {
                pointer-events: auto;
            }
        </style>
    </head>
</head>

<body style="font-size: 12px;">
    <div id="root">
        <div class="">
            <a href="/customerSupport">
                <div class="iconrob" style="position: fixed; right: 0px; bottom: 0px; width: 40px; height: 40px;"><img src="/images/icon-BMOz9JeZ.png" alt="Draggable Icon" style="position: absolute; left: -50px; top: -76px; cursor: move;"></div>
            </a>
        </div>
        <div class="mainApp">
            <div style="position: fixed; z-index: 9999; inset: 16px; pointer-events: none;"></div>
            <div class="container-fluid permotionpage" style="
    padding: 4px 12px;
">
                <div class="row align-items-center" style="
">
                    <div class="col-5"><a class="col-5 text-center" href="/MLMDashboard"><button class="safemode btn bg-theme1 text-white rounded-pill hedbtn text-end shadow-none me-auto" style="
    font-size: 15px;
    width: 105px;
    padding: 3px 9px;
    margin: 0;
">Safe Mode</button></a></div>
                    <div class="col-3">
                        <div class="headertext text-start">Agency</div>
                    </div>
                    <div class="col-4 text-end"><a class=" " href="/newSubordinate"><button class="btn hedbtn text-end shadow-none me-auto" style="
    padding: 0;
"><span style="
"><i class="fa-solid fa-arrow-down-short-wide text-end text-theme1" style="
    font-size: 27px;
"></i></span></button></a></div>
                </div>
            </div>
            <div class="container-fluid permotionpage bgimagepermotion">
                <div class="row">
                    <div class="col-12 text-center">
                        <div class="cummiosionpoint text-white text-center my-1 fw-bold" id="yesterday-commission">0.00</div><button class="btn shadow-none rounded-pill px-2 py-1 text-theme1 bg-white d-flex mx-auto mb-2">Yesterday Total Commission</button>
                        <p class="text-white text-center" style="
    margin-bottom: 17px;
    font-size: 15px;
    margin-top: -14px;
">Upgrade the level to increase commission income</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 mb-3">
                        <div class="card bg-white border-0 rounded3 topcard">
                            <div class="row mx-0">
                                <div class="col-6 border-end px-0">
                                    <div class="card-header py-1 text-center p-0 text-white bg-theme1"> Direct subordinates</div>
                                    <div class="card-number fs-14 text-center text-theme1 my-2" id="direct-register-count">0</div>
                                    <div class="card-text text-center mb-2 fs-14">number of register</div>
                                    <div class="card-number fs-14 text-center text-warning mb-2" id="direct-register-today">0</div>
                                    <div class="card-text text-center mb-2 fs-14">Today register</div>
                                    <div class="card-number fs-14 text-center mb-2 text-success" id="direct-deposit-amount">0</div>
                                    <div class="card-text text-center mb-2 fs-14">Deposit amount</div>
                                    <div class="card-number fs-14 text-center mb-2 text-success" id="direct-bet-amount">0</div>
                                    <div class="card-text text-center mb-2 fs-14">Direct bet amount</div>
                                    <div class="card-number fs-14 text-center mb-2 text-theme1" id="direct-first-deposit">0</div>
                                    <div class="card-text text-center mb-2 fs-14"> Number of people making first deposit</div>
                                </div>
                                <div class="col-6 px-0">
                                    <div class="card-header2 py-1 text-center p-0 text-white bg-theme1" style="
    border-top-left-radius: 11px;
"> Team subordinates</div>
                                    <div class="card-number fs-14 text-center text-theme1 my-2" id="team-register-count">0</div>
                                    <div class="card-text text-center mb-2 fs-14">number of register</div>
                                    <div class="card-number fs-14 text-center text-warning mb-2" id="team-register-today">0</div>
                                    <div class="card-text text-center mb-2 fs-14">Today register</div>
                                    <div class="card-number fs-14 text-center mb-2 text-success" id="team-deposit-amount">0</div>
                                    <div class="card-text text-center mb-2 fs-14">Deposit amount</div>
                                    <div class="card-number fs-14 text-center mb-2 text-success" id="team-bet-amount">0</div>
                                    <div class="card-text text-center mb-2 fs-14">Team bet amount</div>
                                    <div class="card-number fs-14 text-center mb-2 text-theme1" id="team-first-deposit">0</div>
                                    <div class="card-text text-center mb-2 fs-14"> Number of people making first deposit</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 mb-3"><button class="btn  fw-bold rounded-pill text-white d-flex mx-auto w-75 py-1 px-3 justify-content-center text-uppercase shadow-none border-0 maingridentcolor1">invitation link</button></div>
                </div>
            </div>
            <div class="container-fluid permotionpage mainheight pb-5" style="
    padding: 0px 10px;
">
                <div class="row g-2 pb-2">
                    <div class="col-12">
                        <a class="list text-decoration-none d-flex align-items-center justify-content-between bg-white rounded3 px-1 py-2" href="javascript:void(0)" onclick="copyInvitationCode()">
                            <div class="headinglist text-black fs-16 d-flex align-items-center gap-2 text-black "><span><i class="fa-solid fa-globe text-theme1"></i></span>Copy invitation code</div>
                            <div class="arrowicon text-muted d-flex gap-2 align-items-center fs-14 text-muted" id="invitation-code-display">Loading...<i class="fa-regular fa-copy text-muted"></i></div>
                        </a>
                    </div>
                    <div class="col-12" style="
    margin-top: 5px;
">
                        <a class="list text-decoration-none d-flex align-items-center justify-content-between bg-white rounded3 px-1 py-2" href="/Subordinate">
                            <div class="headinglist text-black fs-16 d-flex align-items-center gap-2 text-black"><span><i class="fa-solid fa-money-bill-transfer text-theme1"></i></span>Subordinate Data</div>
                            <div class="arrowicon"><i class="fa-solid fa-angle-right text-black"></i></div>
                        </a>
                    </div>
                    <div class="col-12" style="margin-top: 5px;">
                        <a class="list text-decoration-none d-flex align-items-center justify-content-between bg-white rounded3 px-1 py-2" href="/commission">
                            <div class="headinglist text-black fs-16 d-flex align-items-center gap-2 text-black"><span><i class="fa-solid fa-money-bill-transfer text-theme1"></i></span>Commission Detail</div>
                            <div class="arrowicon"><i class="fa-solid fa-angle-right text-black"></i></div>
                        </a>
                    </div>
                    <div class="col-12" style="
    margin-top: 5px;
">
                        <a class="list text-decoration-none d-flex align-items-center justify-content-between bg-white rounded3 px-1 py-2" href="/releaseSignUpBonus">
                            <div class="headinglist text-black fs-16 d-flex align-items-center gap-2 text-black"><span><i class="fa-solid fa-money-bill-transfer text-theme1"></i></span>Daily Extra Invite Bonus</div>
                            <div class="arrowicon"><i class="fa-solid fa-angle-right text-black"></i></div>
                        </a>
                    </div>
                    <div class="col-12" style="
    margin-top: 5px;
">
                        <a class="list text-decoration-none d-flex align-items-center justify-content-between bg-white rounded3 px-1 py-2" href="/directdeposite">
                            <div class="headinglist text-black fs-16 d-flex align-items-center gap-2 text-black"><span><i class="fa-solid fa-money-bill-transfer text-theme1"></i></span>Referral Bonus</div>
                            <div class="arrowicon"><i class="fa-solid fa-angle-right text-black"></i></div>
                        </a>
                    </div>
                    <div class="col-12" style="
    margin-top: 5px;
">
                        <a class="list text-decoration-none d-flex align-items-center justify-content-between bg-white rounded3 px-1 py-2" href="/selffirstd">
                            <div class="headinglist text-black fs-16 d-flex align-items-center gap-2 text-black"><span><i class="fa-solid fa-square-poll-vertical text-theme1"></i></span>Deposit &amp; Extra Bonus</div>
                            <div class="arrowicon"><i class="fa-solid fa-angle-right text-black"></i></div>
                        </a>
                    </div>
                    <div class="col-12" style="
    margin-top: 5px;
">
                        <a class="list text-decoration-none d-flex align-items-center justify-content-between bg-white rounded3 px-1 py-2" href="/LevelUpBonus ">
                            <div class="headinglist text-black fs-16 d-flex align-items-center gap-2 text-black"><span><i class="fa-solid fa-square-poll-vertical text-theme1"></i></span>Trade Level Bonus</div>
                            <div class="arrowicon"><i class="fa-solid fa-angle-right text-black"></i></div>
                        </a>
                    </div>
                    <div class="col-12" style="
    margin-top: 5px;
">
                        <a class="list text-decoration-none d-flex align-items-center justify-content-between bg-white rounded3 px-1 py-2" href="/DailySalaryIncome">
                            <div class="headinglist text-black fs-16 d-flex align-items-center gap-2 text-black"><span><i class="fa-solid fa-square-poll-vertical text-theme1"></i></span>Daily Salary Income</div>
                            <div class="arrowicon"><i class="fa-solid fa-angle-right text-black"></i></div>
                        </a>
                    </div>
                    <div class="col-12" style="
    margin-top: 5px;
">
                        <a class="list text-decoration-none d-flex align-items-center justify-content-between bg-white rounded3 px-1 py-2" href="/WeeklySalaryIncome">
                            <div class="headinglist text-black fs-16 d-flex align-items-center gap-2 text-black"><span><i class="fa-solid fa-square-poll-vertical text-theme1"></i></span>Self Trade Rebate Income</div>
                            <div class="arrowicon"><i class="fa-solid fa-angle-right text-black"></i></div>
                        </a>
                    </div>
                    <div class="col-12" style="
    margin-top: 5px;
">
                        <a class="list text-decoration-none d-flex align-items-center justify-content-between bg-white rounded3 px-1 py-2" href="/betincome">
                            <div class="headinglist text-black fs-16 d-flex align-items-center gap-2 text-black"><span><i class="fa-solid fa-square-poll-vertical text-theme1"></i></span>Daily Attendence Bonus</div>
                            <div class="arrowicon"><i class="fa-solid fa-angle-right text-black"></i></div>
                        </a>
                    </div>
                    <div class="col-12" style="
    margin-top: 5px;
">
                        <a class="list text-decoration-none d-flex align-items-center justify-content-between bg-white rounded3 px-1 py-2" href="/teamrechargeincome ">
                            <div class="headinglist text-black fs-16 d-flex align-items-center gap-2 text-black"><span><i class="fa-solid fa-square-poll-vertical text-theme1"></i></span>Level UP Reward</div>
                            <div class="arrowicon"><i class="fa-solid fa-angle-right text-black"></i></div>
                        </a>
                    </div>
                    <div class="col-12" style="
    margin-top: 5px;
">
                        <a class="list text-decoration-none d-flex align-items-center justify-content-between bg-white rounded3 px-1 py-2" href="/MonthlyReward ">
                            <div class="headinglist text-black fs-16 d-flex align-items-center gap-2 text-black"><span><i class="fa-solid fa-square-poll-vertical text-theme1"></i></span>Monthly Reward</div>
                            <div class="arrowicon"><i class="fa-solid fa-angle-right text-black"></i></div>
                        </a>
                    </div>
                    <div class="col-12" style="
    margin-top: 5px;
">
                        <a class="list text-decoration-none d-flex align-items-center justify-content-between bg-white rounded3 px-1 py-2" href="/jackpotincome">
                            <div class="headinglist text-black fs-16 d-flex align-items-center gap-2 text-black"><span><i class="fa-solid fa-square-poll-vertical text-theme1"></i></span>Community Bonus</div>
                            <div class="arrowicon"><i class="fa-solid fa-angle-right text-black"></i></div>
                        </a>
                    </div>
                    <div class="col-12" style="
    margin-top: 5px;
">
                        <a class="list text-decoration-none d-flex align-items-center justify-content-between bg-white rounded3 px-1 py-2" href="/myRefferral">
                            <div class="headinglist text-black fs-16 d-flex align-items-center gap-2 text-black"><span><i class="fa-solid fa-people-group text-theme1"></i></span>My Referral</div>
                            <div class="arrowicon"><i class="fa-solid fa-angle-right text-black"></i></div>
                        </a>
                    </div>
                    <div class="col-12" style="
    margin-top: 5px;
">
                        <a class="list text-decoration-none d-flex align-items-center justify-content-between bg-white rounded3 px-1 py-2" href="/myDownline">
                            <div class="headinglist text-black fs-16 d-flex align-items-center gap-2 text-black"><span><i class="fa-solid fa-people-group text-theme1"></i></span>My Downline</div>
                            <div class="arrowicon"><i class="fa-solid fa-angle-right text-black"></i></div>
                        </a>
                    </div>
                    <div class="col-12" style="
    margin-top: 5px;
">
                        <a class="list text-decoration-none d-flex align-items-center justify-content-between bg-white rounded3 px-1 py-2" href="/levelDetails">
                            <div class="headinglist text-black fs-16 d-flex align-items-center gap-2 text-black"><span><i class="fa-solid fa-people-group text-theme1"></i></span>Level Details</div>
                            <div class="arrowicon"><i class="fa-solid fa-angle-right text-black"></i></div>
                        </a>
                    </div>
                </div>
                <div class="row pb-2">
                    <div class="col-12">
                        <div class="card rounded3 border-0 shadow-none p-1">
                            <div class="card-heading main fw-bold text-theme1 fs-16 d-flex align-items-center gap-2"><span><i class="fa-solid fa-people-group-arrows text-theme1"></i></span>Promotion Data</div>
                            <div class="row mx-0 mb-1 align-items-center">
                                <div class="col-6 px-1 border-end">
                                    <div class="numbertext fs-12 text-theme1 text-center mb-1">55.386</div>
                                    <div class="text-center fs-14 text-muted">This Week</div>
                                </div>
                                <div class="col-6 px-1">
                                    <div class="numbertext fs-12 text-theme1 text-center mb-1">3827.425</div>
                                    <div class="text-center fs-14 text-muted">Total commission</div>
                                </div>
                            </div>
                            <div class="row mx-0">
                                <div class="col-6 px-1 border-end">
                                    <div class="numbertext fs-12 text-theme1 text-center mb-1">2</div>
                                    <div class="text-center fs-14 text-muted">Direct Subordinate</div>
                                </div>
                                <div class="col-6 px-1">
                                    <div class="numbertext fs-12 text-theme1 text-center mb-1">41</div>
                                    <div class="text-center fs-14 text-muted">Total Subordinates Team</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <footer class="container-fluid footermain mainApp px-1">
                <div class="row w-100">
                    <div class="col-12 px-1 w-100">
                        <div class="footerlist" style="
    zoom: 0.35 !important;
">
                            <a class="tabbar__container-item text-decoration-none active" href="/">
                                <div class="icon"><img class="w-100 h-100" src="data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewBox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20opacity='0.3'%20d='M27%2046C36.9411%2046%2045%2037.9411%2045%2028C45%2018.0589%2036.9411%2010%2027%2010C17.0589%2010%209%2018.0589%209%2028C9%2037.9411%2017.0589%2046%2027%2046Z'%20fill='black'/%3e%3cpath%20d='M23.6599%205.27799L5.76039%2017.3574V42.8644H15.8404V32.4244C15.8404%2029.6409%2018.0969%2027.3844%2020.8804%2027.3844H27.2404C30.0239%2027.3844%2032.2804%2029.6409%2032.2804%2032.4244V36.8044H28.9204V32.4244C28.9204%2031.4965%2028.1682%2030.7444%2027.2404%2030.7444H20.8804C19.9526%2030.7444%2019.2004%2031.4965%2019.2004%2032.4244V43.5844C19.2004%2045.0424%2018.0184%2046.2244%2016.5604%2046.2244H5.04039C3.58236%2046.2244%202.40039%2045.0424%202.40039%2043.5844V16.9747C2.40039%2016.0973%202.83631%2015.2772%203.56361%2014.7863L22.4377%202.04925C23.1552%201.56501%2024.0926%201.55594%2024.8194%202.02622L44.5146%2014.7702C45.2664%2015.2567%2045.7204%2016.0911%2045.7204%2016.9866V43.5844C45.7204%2045.0424%2044.5384%2046.2244%2043.0804%2046.2244H30.6004V42.8644H42.3604V17.3783L23.6599%205.27799Z'%20fill='black'/%3e%3cpath%20d='M32.4%2044.5443C32.4%2045.4722%2031.6478%2046.2243%2030.72%2046.2243C29.7922%2046.2243%2029.04%2045.4722%2029.04%2044.5443C29.04%2043.6165%2029.7922%2042.8643%2030.72%2042.8643C31.6478%2042.8643%2032.4%2043.6165%2032.4%2044.5443Z'%20fill='black'/%3e%3cpath%20d='M32.2799%2036.7445C32.2799%2037.6724%2031.5277%2038.4245%2030.5999%2038.4245C29.6721%2038.4245%2028.9199%2037.6724%2028.9199%2036.7445C28.9199%2035.8167%2029.6721%2035.0645%2030.5999%2035.0645C31.5277%2035.0645%2032.2799%2035.8167%2032.2799%2036.7445Z'%20fill='black'/%3e%3c/svg%3e"
                                        alt=""></div><span>Home</span></a>
                            <a class="tabbar__container-item text-decoration-none" href="/checkIn">
                                <div class="icon"><img class="w-100 h-100" src="data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewBox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20opacity='0.3'%20d='M27%2042C36.9411%2042%2045%2033.9411%2045%2024C45%2014.0589%2036.9411%206%2027%206C17.0589%206%209%2014.0589%209%2024C9%2033.9411%2017.0589%2042%2027%2042Z'%20fill='black'/%3e%3cpath%20d='M17.4489%2016.6808C17.4491%2016.6697%2017.4492%2016.6586%2017.4492%2016.6475C17.4492%2015.681%2016.6833%2014.8975%2015.7168%2014.8975C14.7529%2014.8975%2013.9263%2015.6767%2013.9219%2016.6396C13.9222%2016.6432%2013.9215%2016.6469%2013.9219%2016.6505C13.9221%2016.7723%2013.9355%2016.8911%2013.9606%2017.0059C14.5925%2021.9877%2018.8462%2025.8397%2023.9996%2025.8397C29.1037%2025.8397%2033.3252%2022.0611%2034.0195%2017.1487C34.0666%2016.9905%2034.0918%2016.8229%2034.0918%2016.6494C34.0918%2015.6829%2033.3083%2014.8994%2032.3418%2014.8994C31.3753%2014.8994%2030.5469%2015.6829%2030.5469%2016.6494C30.5469%2016.6662%2030.5471%2016.6829%2030.5476%2016.6996C30.0741%2019.8911%2027.3228%2022.3397%2023.9996%2022.3397C20.67%2022.3397%2017.9144%2019.8815%2017.4489%2016.6808Z'%20fill='black'/%3e%3cpath%20d='M10.5119%205.2H37.487C40.0599%205.2%2042.1753%207.22874%2042.2828%209.79945L42.793%2022H42.7995C42.7995%2022.8836%2043.5158%2023.6005%2044.3995%2023.6005C45.2832%2023.6005%2045.9995%2022.8841%2045.9995%2022.0005C45.9995%2021.9341%2045.9955%2021.8686%2045.9876%2021.8044L45.48%209.66575C45.3008%205.38123%2041.7752%202%2037.487%202H10.5119C6.22361%202%202.69803%205.38123%202.51886%209.66575L1.34795%2037.6657C1.15787%2042.2112%204.79154%2046%209.34096%2046H38.6579C43.2073%2046%2046.841%2042.2112%2046.6509%2037.6657L46.3303%2030C46.3069%2029.1368%2045.5993%2028.4442%2044.7304%2028.4442C43.8615%2028.4442%2043.1544%2029.1368%2043.131%2030H43.1275L43.1307%2030.0763C43.1312%2030.1004%2043.1322%2030.1243%2043.1337%2030.148L43.4537%2037.7994C43.5677%2040.5267%2041.3875%2042.8%2038.6579%2042.8H9.34096C6.61131%2042.8%204.43111%2040.5267%204.54516%2037.7994L5.71607%209.79945C5.82357%207.22874%207.93892%205.2%2010.5119%205.2Z'%20fill='black'/%3e%3c/svg%3e"
                                        alt=""></div><span>Activity</span></a>
                            <a class="tabbar__container-item text-decoration-none " href="/promotion">
                                <div class="icon diamondimage" style="
    margin-top: 53px;
    margin-left: 18px;
">
                                    <div class="image"><svg id="icon-promotion" viewBox="0 0 57 49" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.93876 1.50122C9.69785 0.55236 10.8471 0 12.0622 0H44.2172C45.4324 0 46.5816 0.552359 47.3407 1.50122L55.0792 11.1744C55.5056 11.7073 55.828 12.2943 56.0469 12.9092H0.232598C0.451468 12.2943 0.773925 11.7073 1.20023 11.1744L8.93876 1.50122ZM0 16.091H56.2795C56.0896 17.0496 55.664 17.9709 55.0034 18.7637L31.2126 47.3125C29.6134 49.2316 26.666 49.2316 25.0669 47.3125L1.27612 18.7637C0.615521 17.9709 0.189841 17.0496 0 16.091ZM20.5563 22.0266L27.7513 32.1286C27.9512 32.4093 28.3685 32.4083 28.5671 32.1267L35.6853 22.0338C36.1425 21.3856 36.8863 21 37.6795 21C39.0272 21 40.1198 22.0925 40.1198 23.4403V23.6393H39.8972C39.5712 23.6393 39.1148 23.8877 38.5931 24.5708C38.0874 25.2331 32.1271 33.2938 28.9417 37.6047C28.7578 37.8535 28.467 38 28.1577 38C27.8515 38 27.5632 37.8562 27.379 37.6117L17.3204 24.2603C17.3204 24.2603 16.9258 23.6393 16.2608 23.6393H16.1198V23.445C16.1198 22.0947 17.2144 21 18.5648 21C19.3556 21 20.0975 21.3825 20.5563 22.0266Z" fill="white"></path></svg></div>
                                </div><span class="permotiontext">Promotion</span></a>
                            <a class="tabbar__container-item text-decoration-none" href="/wallet">
                                <div class="icon"><img src="data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewBox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20opacity='0.3'%20d='M28%2042C37.9411%2042%2046%2033.9411%2046%2024C46%2014.0589%2037.9411%206%2028%206C18.0589%206%2010%2014.0589%2010%2024C10%2033.9411%2018.0589%2042%2028%2042Z'%20fill='black'/%3e%3cpath%20d='M3%2023C3%2017.016%206.526%2012.836%2012.0085%2012.132C12.5675%2012.044%2013.148%2012%2013.75%2012H35.25C35.809%2012%2036.3465%2012.022%2036.8625%2012.11C42.4095%2012.77%2046%2016.972%2046%2023V34C46%2040.6%2041.7%2045%2035.25%2045H13.75C7.3%2045%203%2040.6%203%2034V31.822'%20stroke='black'/%3e%3cpath%20d='M46%2023.7241H39.548C37.1822%2023.7241%2035.2466%2025.5862%2035.2466%2027.8621C35.2466%2030.1379%2037.1822%2032%2039.548%2032H46M37%2012C36.4838%2011.9172%2035.8058%2012%2035.2466%2012H14C13.3978%2012%2012.5592%2011.9172%2012%2012C12%2012%2012.7312%2011.3517%2013.2474%2010.8551L20.2371%204.11027C21.6566%202.75836%2023.5733%202%2025.5708%202C27.5682%202%2029.4849%202.75836%2030.9044%204.11027L34.6681%207.77235C36.0445%209.0758%2039.548%2012%2037%2012Z'%20stroke='black'/%3e%3c/svg%3e"
                                        alt="" class="w-100 h-100"></div><span>Wallet</span></a>
                            <a class="tabbar__container-item text-decoration-none" href="/mian">
                                <div class="icon"><img src="data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewBox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20opacity='0.3'%20d='M28%2042C37.9411%2042%2046%2033.9411%2046%2024C46%2014.0589%2037.9411%206%2028%206C18.0589%206%2010%2014.0589%2010%2024C10%2033.9411%2018.0589%2042%2028%2042Z'%20fill='black'/%3e%3cpath%20d='M24.08%205.27992C13.7412%205.27992%205.36%2013.6612%205.36%2023.9999C5.36%2034.3387%2013.7412%2042.7199%2024.08%2042.7199C34.4188%2042.7199%2042.8%2034.3387%2042.8%2023.9999V15.2399H46.16V23.9999C46.16%2036.1944%2036.2744%2046.0799%2024.08%2046.0799C11.8856%2046.0799%202%2036.1944%202%2023.9999C2%2011.8055%2011.8856%201.91992%2024.08%201.91992H44.36V5.27992H24.08Z'%20fill='black'/%3e%3cpath%20d='M46.1598%203.59992C46.1598%204.52776%2045.4076%205.27992%2044.4798%205.27992C43.552%205.27992%2042.7998%204.52776%2042.7998%203.59992C42.7998%202.67208%2043.552%201.91992%2044.4798%201.91992C45.4076%201.91992%2046.1598%202.67208%2046.1598%203.59992Z'%20fill='black'/%3e%3cpath%20d='M46.1598%2015.1195C46.1598%2016.0474%2045.4076%2016.7995%2044.4798%2016.7995C43.552%2016.7995%2042.7998%2016.0474%2042.7998%2015.1195C42.7998%2014.1917%2043.552%2013.4395%2044.4798%2013.4395C45.4076%2013.4395%2046.1598%2014.1917%2046.1598%2015.1195Z'%20fill='black'/%3e%3cpath%20d='M15.8064%2029.5825C16.501%2028.9674%2017.5627%2029.0317%2018.1779%2029.7263C19.3275%2031.0242%2020.9265%2032.5202%2023.6403%2032.5202C26.5117%2032.5202%2028.4971%2031.0925%2029.4448%2029.9868C30.0486%2029.2824%2031.1092%2029.2008%2031.8136%2029.8046C32.5181%2030.4085%2032.5997%2031.469%2031.9959%2032.1735C30.5435%2033.8679%2027.6809%2035.8802%2023.6403%2035.8802C19.4421%2035.8802%2016.9931%2033.4562%2015.6627%2031.9541C15.0475%2031.2595%2015.1118%2030.1977%2015.8064%2029.5825Z'%20fill='black'/%3e%3c/svg%3e"
                                        alt="" class="w-100 h-100"></div><span>Account</span></a>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM" crossorigin="anonymous"></script>
    <script src="/js/qr.js"></script>
    <script src="/js/client.js"></script>
    <script src="/js/promotion.js"></script>
    <script>
        $(document).ready(function() {
            function alertMess(text, sic) {
                $('body').append(
                    `
					<div data-v-1dcba851="" class="msg">
						<div data-v-1dcba851="" class="msg-content v-enter-active v-enter-to" style=""> ${text} </div>
					</div>
					`
                );
                setTimeout(() => {
                    $('.msg .msg-content').removeClass('v-enter-active v-enter-to');
                    $('.msg .msg-content').addClass('v-leave-active v-leave-to');
                    setTimeout(() => {
                        $('.msg').remove();
                    }, 100);
                    sic.removeClass('block-click');
                }, 1000);
            }
            $('.btn-box .btn').click(function(e) {
                e.preventDefault();
                $(this).addClass('block-click');
                alertMess('Copy successful', $(this));
                navigator.clipboard.writeText($(this).attr('data-clipboard-text'));
            });
        });
    </script>

    <!-- User info loading is now handled by promotion.js -->
    <script>
        // RosesRender function for level data display
        function RosesRender(datas) {
            let html = datas.map((e) => {
                return `<div data-v-7c8bbbf4="" class="bd van-row">
                        <div data-v-7c8bbbf4="" class="c-tc van-ellipsis van-col van-col--6">Lvl ${e.level}</div>
                        <div data-v-7c8bbbf4="" class="c-tc van-col van-col--4">${e.f1}%</div>
                        <div data-v-7c8bbbf4="" class="c-tc van-col van-col--4">${e.f2}%</div>
                        <div data-v-7c8bbbf4="" class="c-tc van-col van-col--5">${e.f3}%</div>
                        <div data-v-7c8bbbf4="" class="c-tc van-col van-col--5">${e.f4}%</div>
                    </div>`;
            });
            $('.table:eq(1) .box').html(html.join(''));
        }

        // Copy invitation code function
        function copyInvitationCode() {
            if (window.promotionManager) {
                window.promotionManager.copyInvitationCode();
            }
        }
        // All promotion functionality is now handled by promotion.js
        // This keeps the page clean and organized
    </script>
    <script>
        /**
         * Promotion Page JavaScript
         * Handles all promotion-related functionality including data fetching and UI updates
         */

        class PromotionManager {
            constructor() {
                this.isLoading = false;
                this.refreshInterval = null;
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.loadAllData();
                this.startAutoRefresh();
            }

            setupEventListeners() {
                // Copy invitation code click handler
                $(document).on('click', '[data-copy-invitation]', (e) => {
                    e.preventDefault();
                    this.copyInvitationCode();
                });

                // Copy buttons for other elements
                $('.btn-box .btn').on('click', (e) => {
                    e.preventDefault();
                    const button = $(e.currentTarget);
                    button.addClass('block-click');
                    const textToCopy = button.attr('data-clipboard-text');
                    if (textToCopy) {
                        this.copyToClipboard(textToCopy, button);
                    }
                });
            }

            async loadAllData() {
                if (this.isLoading) return;

                this.isLoading = true;
                this.showLoading();

                try {
                    await Promise.all([
                        this.loadPromotionData(),
                        this.loadTeamData(),
                        this.loadUserInfo()
                    ]);
                } catch (error) {
                    console.error('Error loading promotion data:', error);
                    this.showError('Failed to load promotion data');
                } finally {
                    this.isLoading = false;
                    this.hideLoading();
                }
            }

            async loadPromotionData() {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: "POST",
                        url: "/api/webapi/promotion",
                        data: {},
                        dataType: "json",
                        success: (response) => {
                            console.log('Promotion API Response:', response);

                            if (response.status === false) {
                                window.location.href = '/home';
                                return;
                            }

                            this.updatePromotionUI(response);
                            resolve(response);
                        },
                        error: (xhr, status, error) => {
                            console.error('Error fetching promotion data:', error);
                            reject(error);
                        }
                    });
                });
            }

            async loadTeamData() {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: "GET",
                        url: "/api/webapi/myTeam",
                        dataType: "json",
                        success: (response) => {
                            console.log('Team API Response:', response);

                            if (response.status === true) {
                                this.updateTeamUI(response);
                            }
                            resolve(response);
                        },
                        error: (xhr, status, error) => {
                            console.error('Error fetching team data:', error);
                            reject(error);
                        }
                    });
                });
            }

            async loadUserInfo() {
                return new Promise((resolve, reject) => {
                    fetch('/api/webapi/GetUserInfo')
                        .then(response => response.json())
                        .then(data => {
                            if (data.status === false) {
                                unsetCookie();
                                return;
                            }
                            this.updateUserInfo(data);
                            resolve(data);
                        })
                        .catch(error => {
                            console.error('Error fetching user info:', error);
                            reject(error);
                        });
                });
            }

            updatePromotionUI(response) {
                // Update commission data
                $('#yesterday-commission').text(this.formatNumber(response.invite.roses_today || 0, 2));

                // Update direct subordinates data
                $('#direct-register-count').text(response.invite.f1 || 0);
                $('#direct-register-today').text(response.invite.f1_today || 0);

                // Update team subordinates data  
                $('#team-register-count').text(response.invite.total_f || 0);
                $('#team-register-today').text(response.invite.f_all_today || 0);

                // Update invitation code
                if (response.info && response.info[0] && response.info[0].code) {
                    this.updateInvitationCode(response.info[0].code);
                }

                // Update other elements
                this.updateOtherElements(response);

                // Render level data if available
                if (response.level && typeof RosesRender === 'function') {
                    RosesRender(response.level);
                }
            }

            updateTeamUI(response) {
                if (!response.f1) return;

                // Calculate team statistics
                let totalDeposit = 0;
                let totalBetAmount = 0;
                let firstDepositCount = 0;

                response.f1.forEach(member => {
                    totalDeposit += parseFloat(member.total_money || 0);
                    totalBetAmount += parseFloat(member.total_turn_over || 0);
                    // Count members with deposits as first deposit (you can modify this logic)
                    if (parseFloat(member.total_money || 0) > 0) {
                        firstDepositCount++;
                    }
                });

                // Add user's own turnover if available
                const myTurnover = parseFloat(response.myTunrOver || response.myTurnOver || 0);
                totalBetAmount += myTurnover;

                // Update team statistics
                $('#team-deposit-amount').text(this.formatNumber(totalDeposit, 0));
                $('#team-bet-amount').text(this.formatNumber(totalBetAmount, 0));
                $('#team-first-deposit').text(firstDepositCount);

                // Update direct statistics
                if (response.f1_direct) {
                    let directDeposit = 0;
                    let directBetAmount = 0;
                    let directFirstDeposit = 0;

                    response.f1_direct.forEach(member => {
                        directDeposit += parseFloat(member.total_money || 0);
                        directBetAmount += parseFloat(member.total_turn_over || 0);
                        if (parseFloat(member.total_money || 0) > 0) {
                            directFirstDeposit++;
                        }
                    });

                    $('#direct-deposit-amount').text(this.formatNumber(directDeposit, 0));
                    $('#direct-bet-amount').text(this.formatNumber(directBetAmount, 0));
                    $('#direct-first-deposit').text(directFirstDeposit);
                }

                // Update total member display if element exists
                if ($('#total-mem').length) {
                    const totalRecharge = totalDeposit;
                    const totalTurnover = totalBetAmount;
                    $('#total-mem').text(`Total Recharge: ${this.formatNumber(totalRecharge, 0)} | Total Turnover: ${this.formatNumber(totalTurnover, 0)}`);
                }
            }

            updateUserInfo(data) {
                const userData = data.data;

                // Update user elements if they exist
                $('.name').text(userData.name_user || 'Member');
                $('.uid').html(`UID | ${userData.id_user || 'N/A'} &nbsp;<span><i class="fa-regular fa-copy text-white"></i></span>`);
                $('.uid').attr('data-clipboard-text', userData.id_user || '');

                // Update phone number display
                if (userData.phone_user) {
                    const phone = userData.phone_user.toString();
                    const maskedPhone = `+91 ${phone.slice(0, 2)}****${phone.slice(-4)}`;
                    $('.logindate').html(`<span>Phone: </span><span>${maskedPhone}</span>`);
                }

                // Store user data globally
                window.currentUserData = userData;
                window.fullApiResponse = data;
            }

            updateInvitationCode(code) {
                const inviteLink = window.location.hostname + `/register?r_code=${code}`;

                $('#invitation-code-display').html(`${code}<i class="fa-regular fa-copy text-muted"></i>`);
                $('#invite_code').text(inviteLink);

                // Set clipboard data
                $('.btn-box .btn:eq(0)').attr('data-clipboard-text', code);
                $('.btn-box .btn:eq(1)').attr('data-clipboard-text', inviteLink);

                // Generate QR code
                this.generateQRCode(inviteLink);
            }

            updateOtherElements(response) {
                $('#f1').text(response.invite.f1 || 0);
                $('#f1_today').text(response.invite.f1_today || 0);
                $('#f_all_today').text(response.invite.f_all_today || 0);
                $('#f_total').text(response.invite.total_f || 0);
                $('#roses_all').text(this.formatNumber(response.invite.roses_all || 0, 2));
                $('#roses_f1').text("Ref. Rec. Com.(4%) : " + this.formatNumber(response.invite.referralCommission || 0, 2));
                $('#roses_today').text(this.formatNumber(response.invite.roses_today || 0, 2));
            }

            generateQRCode(link) {
                try {
                    if (typeof QRCode !== 'undefined' && $('#qrcode').length) {
                        $('#qrcode').empty(); // Clear existing QR code
                        const qrcode = new QRCode(document.getElementById("qrcode"));
                        $('#qrcode').attr('title', link);
                        qrcode.makeCode(link);
                    }
                } catch (error) {
                    console.error('Error generating QR code:', error);
                }
            }

            copyInvitationCode() {
                const codeElement = $('#invitation-code-display');
                const codeText = codeElement.text().replace(/\s*$/, '');
                const actualCode = codeText.split(' ')[0];

                if (actualCode && actualCode !== 'Loading...') {
                    this.copyToClipboard(actualCode, codeElement);
                }
            }

            copyToClipboard(text, element) {
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(text).then(() => {
                        this.showSuccessMessage('Copied successfully!', element);
                    }).catch((err) => {
                        console.error('Could not copy text: ', err);
                        this.fallbackCopy(text, element);
                    });
                } else {
                    this.fallbackCopy(text, element);
                }
            }

            fallbackCopy(text, element) {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                this.showSuccessMessage('Copied successfully!', element);
            }

            showSuccessMessage(text, element) {
                $('body').append(`
            <div data-v-1dcba851="" class="msg">
                <div data-v-1dcba851="" class="msg-content v-enter-active v-enter-to">${text}</div>
            </div>
        `);

                setTimeout(() => {
                    $('.msg .msg-content').removeClass('v-enter-active v-enter-to');
                    $('.msg .msg-content').addClass('v-leave-active v-leave-to');
                    setTimeout(() => {
                        $('.msg').remove();
                    }, 100);
                    if (element) {
                        element.removeClass('block-click');
                    }
                }, 1000);
            }

            showError(message) {
                console.error(message);
                this.showSuccessMessage(`Error: ${message}`, null);

                // Set default values on error
                $('#yesterday-commission').text('0.00');
                $('#direct-register-count').text('0');
                $('#direct-register-today').text('0');
                $('#team-register-count').text('0');
                $('#team-register-today').text('0');
                $('#invitation-code-display').html('Error loading<i class="fa-regular fa-copy text-muted"></i>');
            }

            showLoading() {
                $('.van-overlay').fadeIn(10);

                // Show loading states
                $('#yesterday-commission').text('...');
                $('#direct-register-count').text('...');
                $('#direct-register-today').text('...');
                $('#team-register-count').text('...');
                $('#team-register-today').text('...');
                $('#invitation-code-display').html('Loading...<i class="fa-regular fa-copy text-muted"></i>');
            }

            hideLoading() {
                $('.van-overlay').fadeOut(10);
            }

            formatNumber(num, decimals = 2) {
                return parseFloat(num || 0).toFixed(decimals);
            }

            startAutoRefresh() {
                // Refresh data every 30 seconds
                this.refreshInterval = setInterval(() => {
                    this.loadAllData();
                }, 30000);
            }

            stopAutoRefresh() {
                if (this.refreshInterval) {
                    clearInterval(this.refreshInterval);
                    this.refreshInterval = null;
                }
            }

            destroy() {
                this.stopAutoRefresh();
                $(document).off('click', '[data-copy-invitation]');
                $('.btn-box .btn').off('click');
            }
        }

        // Initialize promotion manager when document is ready
        $(document).ready(function() {
            window.promotionManager = new PromotionManager();
        });
    </script>
    <!-- <script>
        var limit = 15;
        var start = 0;
        var action = 'inactive';
        function load_country_data(limit, start) {
            $.ajax({
                url: "/api/webapi/promotion",
                method: "POST",
                data: {
                    limit: limit,
                    start: start
                },
                cache: false,
                success: function(data) {
                    if (giai_doan == '') {
                        $('.van-list__finished-text').text("No more data");
                        action = 'active';
                    } else {
                        $('.van-list__finished-text').html('<div data-v-7d40872f="" class="order-content"><div role="feed" class="van-list" aria-busy="true"><div class="van-list__loading"><div class="van-loading van-loading--circular"><span class="van-loading__spinner van-loading__spinner--circular" style="width: 16px; height: 16px;"><svg viewBox="25 25 50 50" class="van-loading__circular"><circle cx="50" cy="50" r="20" fill="none"></circle></svg></span><span class="van-loading__text">Loading...</span></div></div><div class="van-list__placeholder"></div></div></div>');
                        action = "inactive";
                    }
                }
            });
        }
        if (action == 'inactive') {
            action = 'active';
            load_country_data(limit, start);
        }
        $(window).scroll(function() {
            if ($(window).scrollTop() + $(window).height() > $(".main").height() && action == 'inactive') {
                action = 'active';
                start = start + limit;
                setTimeout(function() {
                    load_country_data(limit, start);
                }, 1200);
            }
        });
    </script> -->
</body>

</html>