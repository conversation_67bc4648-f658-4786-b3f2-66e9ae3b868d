<div data-v-03b808c2="" class="van-popup van-popup--round van-popup--bottom pop-total"
    style="max-width: 10rem; left: auto; box-shadow: rgba(63, 69, 83, 0.3) 0px -10px 20px 0px; z-index: 2004;transform: translateY(400px);transition: transform .5s,-webkit-transform .5s;"
    >
    <div data-v-03b808c2="" class="betting-mark">
        <div data-v-03b808c2="" class="info">
            <div data-v-03b808c2="" class="Bet-box">
                <ul data-v-03b808c2="" class="c-row c-flex-warp c-row-middle list-join-ao" style="color: #fff">
                    <span data-v-03b808c2="" game="1">Sum：</span>
                    <span data-v-03b808c2="" game="2_1" class="d-none">Choose 2 suitable numbers：</span>
                    <span data-v-03b808c2="" game="3" class="d-none">Choose 3 unique numbers：</span>
                    <span data-v-03b808c2="" game="4" class="d-none">Choose 3 different numbers：</span>
                </ul>
                <ul data-v-03b808c2="" class="c-row c-flex-warp c-row-middle d-none" game="2_2">
                    <!----->
                </ul>
                <ul data-v-03b808c2="" class="c-row c-flex-warp c-row-middle d-none" game="4">
                    <span data-v-03b808c2="">Choose 2 different numbers：</span>
                </ul>
                <div data-v-03b808c2="" class="actionBtn d-none">Choose 3 identical numbers</div>
            </div>
            <div data-v-03b808c2="" class="item c-row c-row-between">
                <div data-v-03b808c2="" class="tit">Amount</div>
                <div data-v-03b808c2="" class="c-row amount-box">
                    <div data-v-03b808c2="" value="1" class="li action">1</div>
                    <div data-v-03b808c2="" value="10" class="li">10</div>
                    <div data-v-03b808c2="" value="100" class="li">100</div>
                    <div data-v-03b808c2="" value="1000" class="li">1000</div>
                </div>
            </div> 
            <div data-v-03b808c2="" class="item c-row c-row-between">
                <div data-v-03b808c2="" class="tit">Quantity</div>
                <div data-v-03b808c2="" class="c-row c-row-between stepper-box minus-plus">
                    <div data-v-03b808c2="" class="li minus">-</div>
                    <div data-v-03b808c2="" class="digit-box van-cell van-field">
                        <div class="van-cell__value van-cell__value--alone van-field__value">
                            <div class="van-field__body">
                                <input type="tel" value="1" oninput="value=value.replace(/\D/g,'')" inputmode="numeric" class="van-field__control xvalue">
                            </div>
                        </div>
                    </div>
                    <div data-v-03b808c2="" class="li plus c-row c-row-middle-center action">+</div>
                </div>
            </div>
            <div data-v-03b808c2="" class="item c-row c-flew-end">
                <div data-v-03b808c2="" class="c-row multiple-box">
                    <div data-v-03b808c2="" value="1" class="li action">X1</div>
                    <div data-v-03b808c2="" value="5" class="li">X5</div>
                    <div data-v-03b808c2="" value="10" class="li">X10</div>
                    <div data-v-03b808c2="" value="20" class="li">X20</div>
                    <div data-v-03b808c2="" value="50" class="li">X50</div>
                    <div data-v-03b808c2="" value="100" class="li">X100</div>
                </div>
            </div>
            <div data-v-03b808c2="" class="item c-row c-row-middle">
                <div data-v-03b808c2="" role="checkbox" tabindex="0" aria-checked="true" class="van-checkbox">
                    <div class="van-checkbox__icon van-checkbox__icon--square van-checkbox__icon--checked">
                        <i
                            class="van-icon van-icon-success"
                            style="border-color: rgb(217, 172, 79); background-color: rgb(217, 172, 79);">
                        </i>
                    </div>
                    <span class="van-checkbox__label">
                        <div data-v-03b808c2="" class="agree p-r-15">I agree</div>
                    </span>
                </div><span data-v-03b808c2="" class="txt txt-qu-ytac">Pre-Selling Rules</span>
            </div>
        </div> 
        <div data-v-03b808c2="" class="foot c-row">
            <div data-v-03b808c2="" class="left canned"> Cancel </div>
            <div data-v-03b808c2="" class="right c-row c-row-middle-center confirm">
                <span data-v-03b808c2="" class="p-r-5">Total Amount</span>
                <span data-v-03b808c2="" value="" class="result">1</span>
                <span class="info-bet" xvalue="1" money="1"></span>
            </div>
        </div>
    </div>
</div>