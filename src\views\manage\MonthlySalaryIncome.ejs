<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Monthly Salary Income</title>
  <link rel="stylesheet"
    href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />
  <link rel="stylesheet" href="/plugins/fontawesome-free/css/all.min.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" />
  <link href="//cdn.jsdelivr.net/npm/@sweetalert2/theme-dark@4/dark.css" rel="stylesheet" />
  <link rel="stylesheet" href="/dist/css/adminlte.min.css" />
  <link rel="stylesheet" href="/css/admin.css" />
  <style>
    .block-click {
      pointer-events: none;
    }
  </style>
</head>

<body class="hold-transition sidebar-mini">
  <div class="wrapper">
    <%- include('nav') %>
      <div class="content-wrapper">
        <section class="content-header">
          <div class="container-fluid">
            <div class="row mb-2">
              <div class="col-sm-6">
                <h1>Monthly Salary Income</h1>
              </div>
            </div>
          </div>
          <!-- /.container-fluid -->
        </section>

        <div class="form-group" style="text-align: center">
          <input type="text" id="search" placeholder="Enter the member you are looking for" />
        </div>

        <!-- Main content -->
        <section class="content">
          <!-- Default box -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">Monthly Salary Income</h3>
              <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse" title="Collapse">
                  <i class="fas fa-minus"></i>
                </button>
                <button type="button" class="btn btn-tool" data-card-widget="remove" title="Remove">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
            <div class="card-body p-0" style="overflow-y: hidden">
              <table class="table table-striped projects" id="table1">
                <thead>
                  <tr>
                    <th class="text-center">#</th>
                    <th class="text-center">Id Number</th>
                   <th class="text-center">Phone Number</th>
                    <th class="text-center">Amount</th>
                   
                    <th class="text-center">Date</th>
                  </tr>
                </thead>
                <tbody>
                  <!-- #region -->
                </tbody>
              </table>
            </div>
            <nav aria-label="Page navigation example" style="margin-top: 20px; display: flex; justify-content: center">
                <ul class="pagination table1">
                    <li class="page-item previous" id="previous">
                        <a class="page-link" href="#" tabindex="-1">Previous</a>
                    </li>
                    <div id="numbers" style="display: flex">
                        <li class="page-item">
                            <a class="page-link active text-white" id="text-page"></a>
                        </li>
                    </div>
                    <li class="page-item next" id="next">
                        <a class="page-link" href="#">Next</a>
                    </li>
                </ul>
            </nav>
          </div>
        </section>
      </div>
  </div> 
  <script src="/plugins/jquery/jquery.min.js"></script>
  <script src="/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="/dist/js/adminlte.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.js"></script>
  <script src="/js/admin/admin.js"></script> 
  <script>
    $("#search").on("keyup", function() {
        var value = $(this).val().toLowerCase();
        $("tbody tr").filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });
  </script>
  <script>
    const Render = (datas) => {
     
      let html = '';
      datas.map((data,index) => {
       let serialNumber = pageno + index + 1;

      
      
        html += `
        <tr class="text-center" style="">
         <td>${serialNumber}</td>
          <td>${data.userId}</td>
          <td>
            <b style="color: #2003db">${data.phone}</b>
          </td>
          
          <td>
            <b>${data.amount}</b>
          </td>
          <td>
            <b>${data.time}</b>
          </td>
        </tr>`;
        $("tbody").html(html);
      });
      $('.btn-danger').click(function (e) { 
        e.preventDefault();
        let check = confirm("Are you sure you want to lock this account ?");
        let id = $(this).attr('id');
        if (check) {
          $.ajax({
            type: "POST",
            url: "/api/webapi/admin/banned",
            data: {
              id: id,
              type: 'close'
            },
            dataType: "json",
            success: function (response) {
              alert(response.message);
              location.reload();
            }
          });
        }
      });
      
      $('.btn-info1').click(function (e) { 
        e.preventDefault();
        let check = confirm("Are you sure you want to unlock this account ?");
        let id = $(this).attr('id');
        if (check) {
          $.ajax({
            type: "POST",
            url: "/api/webapi/admin/banned",
            data: {
              id: id,
              type: 'open'
            },
            dataType: "json",
            success: function (response) {
              alert(response.message);
              location.reload();
            }
          });
        }
      });

    }

    let pageno = 0;
    let limit = 30;
    let page = 1;
    $.ajax({
      type: "POST",
      url: "/api/webapi/admin/listDailySalaryIncome",
      data: {
        typeid: "1",
        type: "monthly",
        pageno: pageno,
        limit: limit,
        language: "vi",
      },
      dataType: "json",
      success: function (response) {
        $('#text-page').text(page + ' / ' + response.page_total);
        if (response.status === true) return Render(response.datas,pageno,limit);
      },
    });

    $('#search').keypress(function(event){
      var keycode = (event.keyCode ? event.keyCode : event.which);
      var value = $('#search').val().trim();
      if(keycode == '13'){
        $.ajax({
          type: "POST",
          url: "/api/webapi/search",
          data: {
            phone: value,
          },
          dataType: "json",
          success: function (response) {
            if (response.status === true) return Render(response.datas,pageno,limit);
          }
        });
      }

    });

    $('#next').click(function (e) {
      pageno += limit;
      e.preventDefault();
      $.ajax({
        type: "POST",
        url: "/api/webapi/admin/listDailySalaryIncome",
        data: {
          typeid: "1",
          type: "monthly",
          pageno: pageno,
          limit: limit,
          language: "vi",
        },
        dataType: "json",
        success: function (response) {
          if(response.datas.length == 0) {
            $('#next').addClass('block-click');
            return pageno -= limit
          };
          $('#text-page').text(++page + ' / ' + response.page_total);
          if (response.status === true) return Render(response.datas,pageno,limit);
        }
      });
    });
    
    $('#previous').click(function (e) { 
      e.preventDefault();
      $('#next').removeClass('block-click');
      pageno -= limit;
      if(pageno < 0) return pageno = 0;
      $.ajax({
        type: "POST",
        url: "/api/webapi/admin/listDailySalaryIncome",
        data: {
          typeid: "1",
          type: "monthly",
          pageno: pageno,
          limit: limit,
          language: "vi",
        },
        dataType: "json",
        success: function (response) {
          $('#text-page').text(--page + ' / ' + response.page_total);
          if (response.status === true) return Render(response.datas,pageno,limit);
        }
      });
    });
  </script>
</body>

</html>