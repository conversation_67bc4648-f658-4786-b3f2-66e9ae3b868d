<!DOCTYPE html>
<html translate="no" data-dpr="1" style="font-size: 48.24px;">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="robots" content="noindex,nofollow">
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport">
    <title>Check In</title>
    <link rel="stylesheet" href="./index_files/index-96409872.css">
    <link href="/css/checkIn/chunk-vendors.css" rel="stylesheet" />
    <link href="/css/checkIn/app.css" rel="stylesheet" />
    <link href="/css/checkIn/chunk.css" rel="stylesheet" />
    <link rel="shortcut icon" href="/images/bitbug_favicon.ico" type="image/x-icon">
    <style>
        html,
		body {
			height: 100%;
			width: 100%;
			background-color: #090909;
			padding: 0;
			margin: 0;
		}
        .block-click {
            pointer-events: none;
        }
    </style>
</head>

<body style="font-size: 12px;">
    <div id="app">
        <div data-v-11ffe290="" class="mian">
            <div data-v-106b99c8="" data-v-11ffe290="" class="navbar action">
                <div data-v-106b99c8="" class="navbar-left" onclick="location.href='/home'">
                    <div data-v-a9660e98="" data-v-106b99c8="" class="c-row c-row-middle-center">
                        <i class="fa-light fa-angle-left fa-fade fa-xl center" style="color: #FFF"></i>
                    </div>
                </div>
                <div data-v-106b99c8="" class="navbar-title"> Rewards </div>
                <div data-v-106b99c8="" class="navbar-right" onclick="location.href='/keFuMenu'">
                    <div data-v-11ffe290="" data-v-106b99c8="" class="c-row">
                        <i class="fa-fade fa center" style="color: #FFF">
                            <img data-v-11ffe290="" data-v-106b99c8="" src="/images/audio.webp" class="audio">
                        </i>
                    </div>
                </div>
            </div>
            <div data-v-11ffe290="" class="check-box">
                <div data-v-11ffe290="" class="check-header c-tc">
                    <h4 data-v-11ffe290="" class="tit m-b-5">7 Days Recharge Rewards</h4>
                    <!-- <div data-v-11ffe290="" class="tit c-tc c-row c-row-middle-center">
                        <div data-v-11ffe290="" class="frequency c-row c-row-middle"> Receive: <p data-v-11ffe290=""
                                class="c-row">Day<span data-v-11ffe290="" class="num">0</span></p>
                        </div>
                    </div> -->
                    <div data-v-11ffe290="" class="c-row c-row-middle-center p-l-20 p-r-20">
                        <div data-v-11ffe290="">
                            <p data-v-11ffe290="" class="txt" onclick="location.href='/checkDes'">Rule</p>
                             <p data-v-11ffe290="" class="txt" onclick="location.href='/checkRecord'">Registration Profile</p>
                        </div>
                    </div>
                </div>
                <div data-v-11ffe290="" class="list c-row c-flex-warp">
                    <div data-v-11ffe290="" data-dpr="1" class="item action">
                        <!---->
                        <div data-v-11ffe290="" class="num c-row c-row-middle c-row-center">₹&nbsp;<span
                                data-v-11ffe290="" class="des">20</span></div><img data-v-11ffe290=""
                            src="/images/tu.webp" class="img"><span data-v-11ffe290="" class="txt">Get it now</span>
                        <!---->
                    </div>
                    <div data-v-11ffe290="" data-dpr="2" class="item action">
                        <!---->
                        <div data-v-11ffe290="" class="num c-row c-row-middle c-row-center">₹&nbsp; <span
                                data-v-11ffe290="" class="des">48</span></div><img data-v-11ffe290=""
                            src="/images/tu.webp" class="img"><span data-v-11ffe290="" class="txt">Get it now</span>
                        <!---->
                    </div>
                    <div data-v-11ffe290="" data-dpr="3" class="item action">
                        <!---->
                        <div data-v-11ffe290="" class="num c-row c-row-middle c-row-center"> ₹&nbsp;<span
                                data-v-11ffe290="" class="des">100</span></div><img data-v-11ffe290=""
                            src="/images/tu.webp" class="img"><span data-v-11ffe290="" class="txt">Get it now</span>
                        <!---->
                    </div>
                    <div data-v-11ffe290="" data-dpr="4" class="item action">
                        <!---->
                        <div data-v-11ffe290="" class="num c-row c-row-middle c-row-center"> ₹&nbsp;<span
                                data-v-11ffe290="" class="des">180</span></div><img data-v-11ffe290=""
                            src="/images/tu.webp" class="img"><span data-v-11ffe290="" class="txt">Get it now</span>
                        <!---->
                    </div>
                    <div data-v-11ffe290="" data-dpr="5" class="item action">
                        <!---->
                        <div data-v-11ffe290="" class="num c-row c-row-middle c-row-center"> ₹&nbsp;<span
                                data-v-11ffe290="" class="des">500</span></div><img data-v-11ffe290=""
                            src="/images/tu.webp" class="img"><span data-v-11ffe290="" class="txt">Get it now</span>
                        <!---->
                    </div>
                    <div data-v-11ffe290="" data-dpr="6" class="item action">
                        <!---->
                        <div data-v-11ffe290="" class="num c-row c-row-middle c-row-center"> ₹&nbsp;<span
                                data-v-11ffe290="" class="des">1100</span></div><img data-v-11ffe290=""
                            src="/images/tu.webp" class="img"><span data-v-11ffe290="" class="txt">Get it now</span>
                        <!---->
                    </div>
                    <div data-v-11ffe290="" data-dpr="7" class="item action">
                        <!---->
                        <div data-v-11ffe290="" class="num c-row c-row-middle c-row-center"> ₹&nbsp;<span
                                data-v-11ffe290="" class="des">1300</span></div><img data-v-11ffe290=""
                            src="/images/tu.webp" class="img"><span data-v-11ffe290="" class="txt">Get it now</span>
                        <!---->
                    </div>
                    <div data-v-11ffe290="" data-dpr="8" class="item action">
                        <!---->
                        <div data-v-11ffe290="" class="num c-row c-row-middle c-row-center"> ₹&nbsp;<span
                                data-v-11ffe290="" class="des">6000</span></div><img data-v-11ffe290=""
                            src="/images/tu.webp" class="img"><span data-v-11ffe290="" class="txt">Get it now</span>
                        <!---->
                    </div>
                </div>
            </div>
            <div data-v-7692a079="" data-v-432e6ed0="" class="Loading c-row c-row-middle-center">
				<div data-v-7692a079="" class="van-loading van-loading--circular">
					<span data-v-7692a079="" class="van-loading__spinner van-loading__spinner--circular" style="width: auto; height: auto; -webkit-animation-duration: 1s; animation-duration: 1s;">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 200 200" width="200" height="200" preserveAspectRatio="xMidYMid meet" style="width: 100%;height: 100%;transform: translate3d(0px, 0px, 0px);content-visibility: visible;">
                            <g transform="matrix(-0.5984085202217102,0.8011911511421204,-0.8011911511421204,-0.5984085202217102,239.9599609375,79.72173309326172)" style="display: block;">
                                <image width="200px" height="200px" preserveAspectRatio="xMidYMid slice" xlink:href="/index_files/loadingspinner.png"></image>
                            </g>
                            <g transform="matrix(0.9408400058746338,0,0,0.9408400058746338,20.969436645507812,79.30152130126953)" style="display: block;">
                                
                            </g>
                        </svg>
                    </span>
                    <img src="/index_files/h5setting_202401100608011fs2.png" style="position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);width: 80%;height: 80%;margin-top: 10px;">
				</div>
			</div>
            <%- include('../nav') -%>
        </div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="/js/client.js"></script>
    <script>
        function alertMess(text, sic) {
            $('body').append(
                `
                <div data-v-1dcba851="" class="msg">
                    <div data-v-1dcba851="" class="msg-content v-enter-active v-enter-to" style=""> ${text} </div>
                </div>
                `
            );
            setTimeout(() => {
                $('.msg .msg-content').removeClass('v-enter-active v-enter-to');
                $('.msg .msg-content').addClass('v-leave-active v-leave-to');
                setTimeout(() => {
                    $('.msg').remove();
                }, 100);
                sic.removeClass('block-click');
            }, 1000);
        }
        $('.list .item').click(function (e) { 
            e.preventDefault();
            $(this).addClass('block-click');
            let data = $(this).attr('data-dpr');
            let wd = $(this);
            $.ajax({
                type: "POST",
                url: "/api/webapi/checkIn",
                data: {
                    data: data,
                },
                dataType: "json",
                success: function (response) {
                    alertMess(response.message, wd);
                    if(response.status === false) return;
                    wd.removeClass('action');
                    wd.find('.txt').text('Received');
                }
            });
        });
        $.ajax({
            type: "POST",
            url: "/api/webapi/checkIn",
            data: {

            },
            dataType: "json",
            success: function (response) {
                let data = response.datas[0];
                if(data.total1 == 0) {
                    $('.list .item:eq(0)').removeClass('action');
                    $('.list .item:eq(0)').addClass('block-click');
                    $('.list .item:eq(0) .txt').text('Received');
                }
                if(data.total2 == 0) {
                    $('.list .item:eq(1)').removeClass('action');
                    $('.list .item:eq(1)').addClass('block-click');
                    $('.list .item:eq(1) .txt').text('Received');
                }
                if(data.total3 == 0) {
                    $('.list .item:eq(2)').removeClass('action');
                    $('.list .item:eq(2)').addClass('block-click');
                    $('.list .item:eq(2) .txt').text('Received');
                }
                if(data.total4 == 0) {
                    $('.list .item:eq(3)').removeClass('action');
                    $('.list .item:eq(3)').addClass('block-click');
                    $('.list .item:eq(3) .txt').text('Received');
                }
                if(data.total5 == 0) {
                    $('.list .item:eq(4)').removeClass('action');
                    $('.list .item:eq(4)').addClass('block-click');
                    $('.list .item:eq(4) .txt').text('Received');
                }
                if(data.total6 == 0) {
                    $('.list .item:eq(5)').removeClass('action');
                    $('.list .item:eq(5)').addClass('block-click');
                    $('.list .item:eq(5) .txt').text('Received');
                }
                if(data.total7 == 0) {
                    $('.list .item:eq(6)').removeClass('action');
                    $('.list .item:eq(6)').addClass('block-click');
                    $('.list .item:eq(6) .txt').text('Received');
                }
                
            }
        });
    </script>
    <script>
        $(window).on('load', function() {
			$('.Loading').fadeOut(0);
		});
    </script>
</body>

</html>