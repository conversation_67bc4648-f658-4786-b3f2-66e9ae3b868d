<!DOCTYPE html>
<html translate="no" data-dpr="1" style="font-size: 37.52px;">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="robots" content="noindex,nofollow">
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport">
    <title>Profile</title>
    <link rel="stylesheet" href="./index_files/index-96409872.css">
    <link href="/css/home/<USER>" rel="stylesheet" />
    <link href="/css/home/<USER>" rel="stylesheet" />
    <link rel="shortcut icon" href="/images/bitbug_favicon.ico" type="image/x-icon" />
    <style>
        html,
        body {
            height: 100%;
            width: 100%;
            background-color: #090909;
            padding: 0;
            margin: 0;
        }

        .mian[data-v-9f3a9836] {
            background-color: #fbfcfd
        }

        .mian .profile[data-v-9f3a9836] {
            padding: .4rem;
            font-size: .37333rem
        }

        .mian .profile .item[data-v-9f3a9836] {
            border-bottom: .02667rem solid #dadada;
            padding: 0 0 .4rem;
            margin-bottom: .53333rem
        }

        .mian .profile .item .img .userImg[data-v-9f3a9836],
        .mian .profile .item .img[data-v-9f3a9836] {
            height: 1.06667rem;
            width: 1.06667rem;
            border-radius: 1.06667rem
        }

        .mian .profile .item .lab[data-v-9f3a9836] {
            color: #000
        }

        .mian .profile .item .txt[data-v-9f3a9836] {
            color: #959595
        }

        .mian .popup-box .title[data-v-9f3a9836] {
            height: 1.6rem;
            width: 100%;
            background: url(/images/myProfilebg.298e3612.png) no-repeat 0 0;
            background-size: 100% 100%
        }

        .mian .popup-box .con[data-v-9f3a9836] {
            padding: .26667rem 0;
            border-bottom: .02667rem solid #dadada
        }

        .mian .popup-box .con .input[data-v-9f3a9836] {
            width: 100%;
            height: .93333rem;
            line-height: .93333rem;
            border: none;
            padding: 0 .4rem
        }

        .mian .popup-box .con .lab[data-v-9f3a9836] {
            padding: 0 .26667rem;
            margin-bottom: .26667rem
        }

        .mian .popup-box .food[data-v-9f3a9836] {
            height: 1.33333rem;
            line-height: 1.33333rem
        }

        .mian .popup-box .food .item[data-v-9f3a9836] {
            flex: 1;
            text-align: center;
            font-weight: 600;
            font-size: .45333rem
        }

        .mian .popup-box .food .item.sure[data-v-9f3a9836] {
            color: #6abe57
        }
    </style>
</head>

<body style="font-size: 12px;">
    <div id="app">
        <div data-v-9f3a9836="" class="mian">
            <div data-v-106b99c8="" data-v-9f3a9836="" class="navbar">
                <div data-v-106b99c8="" class="navbar-left" onclick="location.href='/mian'">
                    <div data-v-106b99c8="" class="bank c-row c-row-middle-center">
                        <img data-v-106b99c8="" src="/images/back.c3244ab0.png" class="navbar-back">
                    </div>
                </div>
                <div data-v-106b99c8="" class="navbar-title">Profile</div>
                <div data-v-106b99c8="" class="navbar-right"></div>
            </div>
            <div data-v-9f3a9836="" class="profile">
                <div data-v-9f3a9836="" class="item c-row c-row-between c-row-middle-center">
                    <div data-v-9f3a9836="" class="lab">Avatar</div>
                    <div data-v-9f3a9836="" class="img">
                        <img data-v-9f3a9836="" width="40px" height="40px" src="/images/avatar.svg" class="userImg">
                    </div>
                </div>
                <div data-v-9f3a9836="" class="item c-row c-row-between">
                    <div data-v-9f3a9836="" class="lab">ID</div>
                    <div data-v-9f3a9836="" class="txt id"> </div>
                </div>
                <div data-v-9f3a9836="" class="item c-row c-row-between">
                    <div data-v-9f3a9836="" class="lab">Username</div>
                    <div data-v-9f3a9836="" class="txt c-row c-row-middle-center">
                        <span class="name"> </span>
                        <i data-v-9f3a9836="" class="m-l-10 van-icon van-icon-arrow"
                            style="color: rgb(149, 149, 149); font-size: 16px;">
                            <!---->
                        </i>
                    </div>
                </div>
                <div data-v-9f3a9836="" class="item c-row c-row-between">
                    <div data-v-9f3a9836="" class="lab">Mobile Number</div>
                    <div data-v-9f3a9836="" class="number"> </div>
                </div>
            </div>
            <!---->
            <div class="van-overlay" style="z-index: 2001;display: none;"></div>
            <div data-v-9f3a9836="" class="van-popup van-popup--center"
                style="width: 80%; border-radius: 15px; max-width: 340px; z-index: 2002;display: none;">
                <div data-v-9f3a9836="" class="popup-box">
                    <div data-v-9f3a9836="" class="title"></div>
                    <div data-v-9f3a9836="" class="con">
                        <div data-v-9f3a9836="" class="lab">Change Username</div>
                        <div data-v-9f3a9836="">
                            <input data-v-9f3a9836="" type="text" maxlength="16" placeholder="Username" class="input">
                        </div>
                    </div>
                    <div data-v-9f3a9836="" class="food c-row c-row-middle-center">
                        <div data-v-9f3a9836="" class="item">Cancel</div>
                        <div data-v-9f3a9836="" class="item sure">Confirm</div>
                    </div>
                </div>
            </div>
            <!---->
            <div data-v-7692a079="" data-v-9f3a9836="" class="Loading c-row c-row-middle-center">
                <div data-v-7692a079="" class="van-loading van-loading--circular">
                    <span data-v-7692a079="" class="van-loading__spinner van-loading__spinner--circular"
                        style="width: auto; height: auto; -webkit-animation-duration: 1s; animation-duration: 1s;">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                            viewBox="0 0 200 200" width="200" height="200" preserveAspectRatio="xMidYMid meet"
                            style="width: 100%;height: 100%;transform: translate3d(0px, 0px, 0px);content-visibility: visible;">
                            <g transform="matrix(-0.5984085202217102,0.8011911511421204,-0.8011911511421204,-0.5984085202217102,239.9599609375,79.72173309326172)"
                                style="display: block;">
                                <image width="200px" height="200px" preserveAspectRatio="xMidYMid slice"
                                    xlink:href="/index_files/loadingspinner.png"></image>
                            </g>
                            <g transform="matrix(0.9408400058746338,0,0,0.9408400058746338,20.969436645507812,79.30152130126953)"
                                style="display: block;">
                              
                            </g>
                        </svg>
                    </span>
                    <img src="/index_files/h5setting_202401100608011fs2.png" style="position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);width: 80%;height: 80%;margin-top: 10px;">
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="/js/client.js"></script>
    <script>
        function setCookie(cname, cvalue, exdays) {
            const d = new Date();
            d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
            let expires = "expires=" + d.toUTCString();
            document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
        }
        function getCookie(cname) {
            let name = cname + "=";
            let decodedCookie = decodeURIComponent(document.cookie);
            let ca = decodedCookie.split(';');
            for (let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) == ' ') {
                    c = c.substring(1);
                }
                if (c.indexOf(name) == 0) {
                    return c.substring(name.length, c.length);
                }
            }
            return "";
        }

        function unsetCookie() {
            setCookie('token', '', 0);
            setCookie('auth', '', 0);
        }
        fetch('/api/webapi/GetUserInfo')
            .then(response => response.json())
            .then(data => {
                $('.Loading').fadeOut(100);
                if (data.status === false) {
                    unsetCookie();
                    return false;
                };
                $('.profile .name').text(data.data.name_user);
                $('.profile .id').text(data.data.id_user);
                $('.profile .number').text("+91 " + data.data.phone_user);
            });
    </script>
    <!---->
    <script>
        function alertMess(text) {
            $('body').append(
                `
                <div data-v-1dcba851="" class="msg">
                    <div data-v-1dcba851="" class="msg-content v-enter-active v-enter-to" style=""> ${text} </div>
                </div>
                `
            );
            setTimeout(() => {
                $('.msg .msg-content').removeClass('v-enter-active v-enter-to');
                $('.msg .msg-content').addClass('v-leave-active v-leave-to');
                setTimeout(() => {
                    $('.msg').remove();
                }, 100);
            }, 1000);
        }

        $('.food .item:eq(0)').click(function (e) {
            e.preventDefault();
            $('.van-popup, .van-overlay').fadeOut(100);
        });
        $('.profile .item:eq(2)').click(function (e) {
            e.preventDefault();
            $('.van-popup, .van-overlay').fadeIn(100);
        });
        $('.sure').click(function (e) {
            e.preventDefault();
            let name = $('input').val().trim();
            if (!name) {
                let check = $('body .msg').length;
                if (check == 0) {
                    alertMess('Please enter your name');
                }
                return;
            }
            $.ajax({
                type: "PUT",
                url: "/api/webapi/change/userInfo",
                data: {
                    name: name,
                    type: 'editname',
                },
                dataType: "json",
                success: function (response) {
                    alertMess(response.message);
                    $('.van-popup, .van-overlay').fadeOut(100);
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                }
            });
        });
    </script>
</body>

</html>