<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <link rel="icon" type="image/svg+xml" href="/logo.png">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Salary Income - DeltInWin</title>
    <!-- Foontawsome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
    <link rel="stylesheet" crossorigin="" href="/index_files/index-BUBUniRp.css">
</head>

<body style="font-size: 12px;">
    <div id="root">
        <div class="mainApp">
            <div class="container-fluid" style="padding: 4px 12px;">
                <!-- Header -->
                <div class="row align-items-center mb-3">
                    <div class="col-2">
                        <a href="/promotion" class="text-decoration-none">
                            <i class="fa-solid fa-arrow-left text-theme1" style="font-size: 20px;"></i>
                        </a>
                    </div>
                    <div class="col-8 text-center">
                        <h5 class="mb-0 text-theme1 fw-bold">Daily Salary Income</h5>
                    </div>
                    <div class="col-2"></div>
                </div>

                <!-- Income Summary -->
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="card bg-gradient-primary text-white border-0 rounded-3">
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-4">
                                        <h6 class="card-title mb-1">Today</h6>
                                        <h4 class="mb-0" id="today-income">₹0.00</h4>
                                    </div>
                                    <div class="col-4">
                                        <h6 class="card-title mb-1">This Month</h6>
                                        <h4 class="mb-0" id="month-income">₹0.00</h4>
                                    </div>
                                    <div class="col-4">
                                        <h6 class="card-title mb-1">Total</h6>
                                        <h4 class="mb-0" id="total-income">₹0.00</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Income Details -->
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="card border-0 rounded-3 bg-light">
                            <div class="card-body">
                                <h6 class="card-title text-theme1 mb-3">
                                    <i class="fa-solid fa-info-circle me-2"></i>How Daily Salary Works
                                </h6>
                                <ul class="list-unstyled mb-0">
                                    <li class="mb-2"><i class="fa-solid fa-check text-success me-2"></i>Earn daily salary based on your team's performance</li>
                                    <li class="mb-2"><i class="fa-solid fa-check text-success me-2"></i>Minimum team deposit required: ₹1000</li>
                                    <li class="mb-2"><i class="fa-solid fa-check text-success me-2"></i>Salary rate: 0.5% of team deposits</li>
                                    <li class="mb-0"><i class="fa-solid fa-check text-success me-2"></i>Paid automatically every 24 hours</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Income History -->
                <div class="row">
                    <div class="col-12">
                        <div class="card border-0 rounded-3">
                            <div class="card-header bg-theme1 text-white">
                                <h6 class="mb-0">Income History</h6>
                            </div>
                            <div class="card-body p-0">
                                <div id="income-list" class="list-group list-group-flush">
                                    <!-- Loading state -->
                                    <div class="list-group-item text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2 mb-0">Loading income data...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Qualification Status -->
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="card border-0 rounded-3 bg-light">
                            <div class="card-body">
                                <h6 class="card-title text-theme1">Qualification Status</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">Team Deposits</small>
                                        <div class="fw-bold" id="team-deposits">₹0.00</div>
                                        <div class="progress mt-1" style="height: 5px;">
                                            <div class="progress-bar" role="progressbar" style="width: 0%" id="deposit-progress"></div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Status</small>
                                        <div class="fw-bold" id="qualification-status">
                                            <span class="badge bg-warning">Not Qualified</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loading Overlay -->
            <div class="van-overlay" style="z-index: 2000; display: none;">
                <div class="Loading">
                    <div class="van-loading van-loading--spinner van-loading--vertical">
                        <span class="van-loading__spinner van-loading__spinner--spinner" style="width: 30px; height: 30px;">
                            <svg viewBox="25 25 50 50" class="circular">
                                <circle cx="50" cy="50" r="20" fill="none" class="path"></circle>
                            </svg>
                        </span>
                        <span class="van-loading__text">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/client.js"></script>

    <script>
        $(document).ready(function() {
            loadIncomeData();
        });

        function loadIncomeData() {
            $('.van-overlay').fadeIn(10);
            
            // Fetch team data to calculate salary
            $.ajax({
                type: "GET",
                url: "/api/webapi/myTeam",
                dataType: "json",
                success: function(response) {
                    console.log('Team Data for Salary:', response);
                    
                    if (response.status === true) {
                        updateIncomeUI(response);
                    } else {
                        showNoData();
                    }
                    $('.van-overlay').fadeOut(10);
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching income data:', error);
                    showError('Failed to load income data');
                    $('.van-overlay').fadeOut(10);
                }
            });
        }

        function updateIncomeUI(data) {
            const team = data.f1 || [];
            
            // Calculate team deposits
            let totalTeamDeposits = 0;
            team.forEach(member => {
                totalTeamDeposits += parseFloat(member.total_money || 0);
            });
            
            // Calculate daily salary (0.5% of team deposits)
            const dailySalary = totalTeamDeposits * 0.005;
            const monthlySalary = dailySalary * 30;
            
            // Update UI
            $('#today-income').text(`₹${dailySalary.toFixed(2)}`);
            $('#month-income').text(`₹${monthlySalary.toFixed(2)}`);
            $('#total-income').text(`₹${(monthlySalary * 12).toFixed(2)}`);
            
            $('#team-deposits').text(`₹${totalTeamDeposits.toFixed(2)}`);
            
            // Update qualification status
            const minRequired = 1000;
            const progress = Math.min((totalTeamDeposits / minRequired) * 100, 100);
            $('#deposit-progress').css('width', `${progress}%`);
            
            if (totalTeamDeposits >= minRequired) {
                $('#qualification-status').html('<span class="badge bg-success">Qualified</span>');
            } else {
                $('#qualification-status').html('<span class="badge bg-warning">Not Qualified</span>');
            }
            
            // Render income history (mock data for now)
            renderIncomeHistory([]);
        }

        function renderIncomeHistory(history) {
            const listContainer = $('#income-list');
            listContainer.empty();
            
            if (history.length === 0) {
                listContainer.html(`
                    <div class="list-group-item text-center py-4">
                        <i class="fa-solid fa-chart-line text-muted" style="font-size: 48px;"></i>
                        <p class="mt-2 mb-0 text-muted">No income history found</p>
                        <small class="text-muted">Income will appear here once you qualify</small>
                    </div>
                `);
                return;
            }
            
            // Render actual history items here
        }

        function showNoData() {
            $('#income-list').html(`
                <div class="list-group-item text-center py-4">
                    <i class="fa-solid fa-chart-line text-muted" style="font-size: 48px;"></i>
                    <p class="mt-2 mb-0 text-muted">No income data available</p>
                </div>
            `);
        }

        function showError(message) {
            $('#income-list').html(`
                <div class="list-group-item text-center py-4">
                    <i class="fa-solid fa-exclamation-triangle text-danger" style="font-size: 48px;"></i>
                    <p class="mt-2 mb-0 text-danger">${message}</p>
                    <button class="btn btn-primary btn-sm mt-2" onclick="loadIncomeData()">Retry</button>
                </div>
            `);
        }
    </script>
</body>

</html>
