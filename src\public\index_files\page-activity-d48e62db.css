.van-toast[data-v-52d6b6c6] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-52d6b6c6] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-52d6b6c6] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-52d6b6c6] {
        height: 80%
}

.progress[data-v-52d6b6c6] {
        width: 100%;
        position: relative;
        overflow: hidden
}

.progress .step[data-v-52d6b6c6] {
        position: absolute;
        top: 50%;
        left: 50%;
        -webkit-transform: translateX(-50%) translateY(-50%);
        transform: translate(-50%) translateY(-50%);
        color: #fff
}

.van-toast[data-v-427e61df] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-427e61df] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-427e61df] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-427e61df] {
        height: 80%
}

.first_list-item[data-v-427e61df] {
        padding: .26667rem;
        border-radius: .26667rem;
        background: #3F3F3F
}

.first_list-item .head[data-v-427e61df] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        margin-bottom: .18667rem
}

.first_list-item .head .title[data-v-427e61df] {
        font-size: .37333rem;
        color: #fff
}

.first_list-item .head .title>span[data-v-427e61df] {
        color: #d9ac4f
}

.first_list-item .head .orange[data-v-427e61df] {
        font-size: .34667rem;
        color: #fae59f
}

.first_list-item .description[data-v-427e61df] {
        font-size: .29333rem;
        color: #a6a9ae;
        margin-bottom: .26667rem
}

.first_list-item .foot[data-v-427e61df] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.first_list-item .foot>div[data-v-427e61df] {
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        flex: 1
}

.first_list-item .foot .btn[data-v-427e61df] {
        width: 2.13333rem;
        min-height: .66667rem;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        -webkit-box-flex: 0;
        -webkit-flex: none;
        flex: none;
        font-size: .32rem;
        margin-left: .56rem;
        border-radius: .13333rem;
        padding: 0 .13333rem;
        color: #fff
}

.first_list-item .foot .btn.n1[data-v-427e61df] {
        box-shadow: 0 .05333rem .21333rem 0 #d0d0ed5c 0 -.05333rem .13333rem 0 #fff6f4 inset;
        background: -webkit-linear-gradient(top,#FAE59F,#C4933F);
        background: linear-gradient(180deg,#FAE59F,#C4933F);
        color: #8f5206
}

.first_list-item .foot .btn.n1.rule[data-v-427e61df] {
        opacity: 0;
        pointer-events: none
}

.first_list-item .foot .btn.n2[data-v-427e61df] {
        border: .01333rem solid #FAE59F;
        color: #fae59f
}

.first_list-item .foot .btn.n2.rule[data-v-427e61df] {
        opacity: 0;
        pointer-events: none
}

.first_list-item .foot .btn.n3[data-v-427e61df] {
        box-shadow: 0 .05333rem .21333rem #d0d0ed5c,0 -.05333rem .13333rem #e8ebff inset;
        background: -webkit-linear-gradient(top,#A6ACD0 0%,#C2CAF4 100%);
        background: linear-gradient(180deg,#A6ACD0 0%,#C2CAF4 100%)
}

.first_list-item+.first_list-item[data-v-427e61df] {
        margin-top: .26667rem
}

.dialog-wrapper[data-v-427e61df] {
        padding: .26667rem 0;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.dialog-wrapper img[data-v-427e61df] {
        width: 3.73333rem;
        height: 2.46667rem;
        position: relative;
        margin-top: -.74667rem
}

.dialog-wrapper .dialog-title[data-v-427e61df] {
        color: #fff;
        margin: .4rem 0 .26667rem;
        font-size: .48rem;
        font-weight: 700
}

.dialog-wrapper .dialog-tips[data-v-427e61df] {
        color: #666;
        font-size: .32rem;
        margin: 0 0 .32rem
}

.dialog-wrapper .dialog-para[data-v-427e61df] {
        color: #666;
        font-size: .32rem
}

.dialog-wrapper .dialog-btn[data-v-427e61df] {
        width: 5.6rem;
        height: 1.06667rem;
        text-align: center;
        line-height: 1.06667rem;
        background: -webkit-linear-gradient(top,#FAE59F,#C4933F);
        background: linear-gradient(180deg,#FAE59F,#C4933F);
        border-radius: 1.06667rem;
        font-weight: 700;
        color: #8f5206;
        font-size: .42667rem;
        margin-top: .53333rem
}

.dialog-wrapper .dialog-content[data-v-427e61df] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        color: #ff7d06
}

.dialog-wrapper .dialog-content img[data-v-427e61df] {
        width: .4rem;
        height: .4rem;
        margin: 0 .13333rem 0 0
}

.dialog-wrapper .dialog-footer[data-v-427e61df] {
        position: relative;
        bottom: -1.46667rem
}

.dialog-wrapper .dialog-footer img[data-v-427e61df] {
        width: .8rem;
        height: .8rem
}

.van-toast[data-v-91f2fefc] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-91f2fefc] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-91f2fefc] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-91f2fefc] {
        height: 80%
}

.empty__container[data-v-91f2fefc] {
        width: 100%;
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.empty__container img[data-v-91f2fefc] {
        width: 4rem;
        height: 3.71427rem;
        margin-bottom: .24667rem
}

.empty__container p[data-v-91f2fefc] {
        color: #acafc2;
        font-size: .34667rem
}

.van-toast[data-v-306b6ffc] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-306b6ffc] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-306b6ffc] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-306b6ffc] {
        height: 80%
}

.item[data-v-306b6ffc] {
        min-height: 3.72rem;
        border-radius: .26667rem;
        position: relative;
        margin-bottom: .26667rem;
        overflow: hidden
}

.item[data-v-306b6ffc] .right {
        position: absolute;
        right: .26667rem;
        top: 50%;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        min-width: 3.73333rem
}

.item[data-v-306b6ffc] .right .head {
        margin-bottom: .13333rem
}

.item[data-v-306b6ffc] .right .head h1,.item[data-v-306b6ffc] .right .foot h1 {
        background: -webkit-linear-gradient(left,#C4933F,#FAE59F);
        background: linear-gradient(90deg,#C4933F,#FAE59F);
        border-radius: .26667rem .26667rem 0 0;
        color: #8f5206;
        font-size: .29333rem;
        padding: .13333rem .26667rem
}

.item[data-v-306b6ffc] .right .head>div,.item[data-v-306b6ffc] .right .foot>div {
        border-radius: 0 0 .26667rem .26667rem;
        padding: 0 .26667rem;
        min-height: .66667rem;
        line-height: .66667rem
}

.item[data-v-306b6ffc] .right .head>div.time,.item[data-v-306b6ffc] .right .foot>div.time {
        background: rgba(143,82,6,.36);
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        color: #fae59f
}

.item[data-v-306b6ffc] .right .head>div.time span,.item[data-v-306b6ffc] .right .foot>div.time span {
        font-size: .48rem;
        font-weight: 700
}

.item[data-v-306b6ffc] .right .head>div.amount,.item[data-v-306b6ffc] .right .foot>div.amount {
        background: rgba(143,82,6,.36);
        color: #fae59f;
        font-weight: 700;
        text-align: center
}

.item[data-v-306b6ffc] .right .head.type2 h1 {
        background: -webkit-linear-gradient(right,#6F7381 0%,#A9AAB5 100%);
        background: linear-gradient(270deg,#6F7381 0%,#A9AAB5 100%);
        color: #fff
}

.item[data-v-306b6ffc] .right .head.type2 .time {
        background: rgba(0,0,0,.36);
        color: #fff
}

.van-toast[data-v-f7cf7e37] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-f7cf7e37] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-f7cf7e37] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-f7cf7e37] {
        height: 80%
}

.activity-wrapper .activitySection__container[data-v-f7cf7e37] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        gap: .34667rem;
        width: 100%;
        padding-left: .32rem;
        padding-right: .32rem
}

.activity-wrapper .activitySection__container>.box[data-v-f7cf7e37] {
        width: 100%;
        border-radius: .26667rem;
        background: #3F3F3F;
        overflow: hidden;
        color: #d9ac4f
}

.activity-wrapper .activitySection__container>.box:last-of-type img[data-v-f7cf7e37] {
        background: #3F3F3F
}

.activity-wrapper .activitySection__container>.box>.box-content[data-v-f7cf7e37] {
        padding: .24rem .32rem;
        line-height: .32rem
}

.activity-wrapper .activitySection__container>.box>.box-content .box-title[data-v-f7cf7e37] {
        font-size: .4rem;
        font-weight: 600;
        margin-bottom: .13333rem
}

.activity-wrapper .activitySection__container>.box>.box-content p[data-v-f7cf7e37] {
        font-size: .32rem;
        color: #fff
}

.activity-wrapper .activitySection__container>.box img[data-v-f7cf7e37] {
        width: 100%;
        height: 3.46667rem
}

.activity-wrapper .activity-banner[data-v-f7cf7e37] {
        color: #fff;
        font-size: .32rem;
        font-style: normal;
        min-height: 2rem;
        background: #3F3F3F;
        padding: .32rem;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.activity-wrapper .activity-banner .banner-title[data-v-f7cf7e37] {
        font-size: .48rem;
        margin-bottom: .26667rem;
        font-weight: 700
}

.activity-wrapper .activity-banner .banner-para[data-v-f7cf7e37] {
        margin-bottom: .06667rem
}

.activity-wrapper .activity-panel[data-v-f7cf7e37] {
        padding: .32rem
}

.activity-wrapper .activity-panel-header[data-v-f7cf7e37] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: self-start;
        -webkit-align-items: self-start;
        align-items: self-start;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between
}

.activity-wrapper .activity-panel-header.lg3[data-v-f7cf7e37] {
        padding: 0 .58667rem
}

.activity-wrapper .activity-panel-header.lg2[data-v-f7cf7e37] {
        padding: 0 1.69333rem
}

.activity-wrapper .activity-panel-header.lg1[data-v-f7cf7e37] {
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center
}

.activity-wrapper .activity-panel-header.lg5[data-v-f7cf7e37] {
        -webkit-flex-wrap: wrap;
        flex-wrap: wrap
}

.activity-wrapper .activity-panel-header.lg5 .header-item[data-v-f7cf7e37] {
        width: 2.13333rem;
        margin-bottom: .26667rem
}

.activity-wrapper .activity-panel-header .header-item[data-v-f7cf7e37] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        color: #d9ac4f;
        width: 1.33333rem;
        font-weight: 500;
        font-size: .32rem;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        text-align: center
}

.activity-wrapper .activity-panel-header .header-item .bgcontainer[data-v-f7cf7e37] {
        width: 1.06667rem;
        height: 1.06667rem;
        border-radius: .26667rem;
        padding: .13333rem;
        margin-bottom: .29333rem
}

.activity-wrapper .activity-panel-header .header-item .bgcontainer[data-v-f7cf7e37]:after {
        content: "";
        display: block;
        width: .8rem;
        height: .8rem;
        background-repeat: no-repeat;
        background-position: center;
        background-size: .8rem
}

.activity-wrapper .activity-panel-header .header-item .bgcontainer.a1[data-v-f7cf7e37]:after {
        background-image: url(/assets/png/activityIcon1-48e9cfdc.png)
}

.activity-wrapper .activity-panel-header .header-item .bgcontainer.a2[data-v-f7cf7e37]:after {
        background-image: url(/assets/png/activityIcon2-0523d754.png)
}

.activity-wrapper .activity-panel-header .header-item .bgcontainer.a3[data-v-f7cf7e37]:after {
        background-image: url(/assets/png/activityIcon3-778b1534.png)
}

.activity-wrapper .activity-panel-header .header-item .bgcontainer.a4[data-v-f7cf7e37]:after {
        background-image: url(/assets/png/activityIcon4-d526ab4c.png)
}

.activity-wrapper .activity-panel-header .header-item .bgcontainer.a5[data-v-f7cf7e37]:after {
        background-image: url(/assets/png/activityIcon5-c41cae54.png)
}

.activity-wrapper .activity-panel-content[data-v-f7cf7e37] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        -webkit-flex-wrap: wrap;
        flex-wrap: wrap;
        margin-top: .26667rem
}

.activity-wrapper .activity-panel-content .content-title[data-v-f7cf7e37] {
        width: calc((100% - .24rem)/2);
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-pack: start;
        -webkit-justify-content: flex-start;
        justify-content: flex-start;
        background: #3F3F3F;
        border-radius: .13333rem;
        padding-bottom: .26667rem;
        box-shadow: drop-shadow(0 .05333rem .05333rem rgba(208,208,237,.36))
}

.activity-wrapper .activity-panel-content .content-title .content-para[data-v-f7cf7e37] {
        color: #d9ac4f;
        font-weight: 700;
        padding: .13333rem .26667rem;
        font-size: .37333rem
}

.activity-wrapper .activity-panel-content .content-title p[data-v-f7cf7e37] {
        padding: .06667rem .26667rem;
        color: #a6a9ae;
        font-size: .29333rem;
        font-weight: 400;
        line-height: .32rem
}

.activity-wrapper .dialog-wrapper[data-v-f7cf7e37] {
        padding: .26667rem 0;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.activity-wrapper .dialog-wrapper img[data-v-f7cf7e37] {
        width: 4.6rem;
        height: 4.6rem;
        position: relative;
        margin-top: -2.13333rem
}

.activity-wrapper .dialog-wrapper .dialog-title[data-v-f7cf7e37] {
        color: #fff;
        margin-bottom: .13333rem;
        font-size: .4rem;
        padding: 0 .16rem
}

.activity-wrapper .dialog-wrapper .dialog-title span[data-v-f7cf7e37] {
        color: #d9ac4f;
        font-weight: 700;
        font-size: .48rem
}

.activity-wrapper .dialog-wrapper .dialog-para[data-v-f7cf7e37] {
        color: #fff;
        font-size: .32rem;
        padding: 0 .26667rem
}

.activity-wrapper .dialog-wrapper .dialog-btn[data-v-f7cf7e37] {
        width: 5.6rem;
        height: 1.06667rem;
        text-align: center;
        line-height: 1.06667rem;
        background: -webkit-linear-gradient(top,#FAE59F,#C4933F);
        background: linear-gradient(180deg,#FAE59F,#C4933F);
        border-radius: 1.06667rem;
        font-weight: 700;
        color: #8f5206;
        font-size: .42667rem;
        margin-top: 1.06667rem
}

.activity-wrapper .dialog-wrapper .dialog-footer[data-v-f7cf7e37] {
        position: relative;
        bottom: -1.46667rem
}

.activity-wrapper .dialog-wrapper .dialog-footer img[data-v-f7cf7e37] {
        width: .8rem;
        height: .8rem
}

.activity-wrapper .cardBox[data-v-f7cf7e37] {
        padding: 0 .26667rem
}

[data-v-f7cf7e37] .van-dialog.MsgRadius {
        border-radius: .13333rem
}

[data-v-f7cf7e37] .van-dialog.noOverHidden {
        overflow: inherit
}

.nowidth[data-v-f7cf7e37] {
        width: 0;
        -webkit-box-flex: 0;
        -webkit-flex: none;
        flex: none
}

.van-toast[data-v-10abffca] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-10abffca] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-10abffca] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-10abffca] {
        height: 80%
}

.navbar[data-v-10abffca] {
        background: #f7f8ff
}

.navbar__content-center img[data-v-10abffca] {
        width: 2.9048rem;
        height: auto
}

.navbar__content-right img[data-v-10abffca] {
        width: .64rem;
        height: auto
}

.van-toast[data-v-b9e16d43] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-b9e16d43] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-b9e16d43] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-b9e16d43] {
        height: 80%
}

.dialog[data-v-b9e16d43] {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        background: rgba(0,0,0,.7);
        -webkit-transition: opacity .15s ease-in-out,visibility .15s ease-in-out;
        transition: opacity .15s ease-in-out,visibility .15s ease-in-out;
        z-index: 2004
}

.dialog.active[data-v-b9e16d43] {
        opacity: 1;
        visibility: visible
}

.dialog.active .dialog__container[data-v-b9e16d43] {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1
}

.dialog.inactive[data-v-b9e16d43] {
        opacity: 0;
        visibility: hidden
}

.dialog.inactive .dialog__container[data-v-b9e16d43] {
        -webkit-transform: scale(.3);
        transform: scale(.3);
        opacity: 0
}

.dialog__container[data-v-b9e16d43] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        width: 8rem;
        min-height: 6.66667rem;
        border-radius: .26667rem;
        background: #292929;
        -webkit-transition: opacity .3s,-webkit-transform .3s ease-in-out;
        transition: opacity .3s,-webkit-transform .3s ease-in-out;
        transition: opacity .3s,transform .3s ease-in-out;
        transition: opacity .3s,transform .3s ease-in-out,-webkit-transform .3s ease-in-out;
        z-index: 2003;
        padding: .53333rem .26667rem;
        border-radius: 20px
}

.dialog__container .close_img[data-v-b9e16d43] {
        position: absolute;
        bottom: -1.06667rem;
        width: .8rem;
        height: .8rem
}

.dialog__container-img[data-v-b9e16d43] {
        width: 2.13333rem;
        height: 2.13333rem
}

.dialog__container-img img[data-v-b9e16d43] {
        width: 100%;
        height: 100%
}

.dialog__container-title[data-v-b9e16d43] {
        margin-top: .34667rem;
        font-size: .48rem;
        font-weight: 700;
        color: #fff
}

.dialog__container-content[data-v-b9e16d43] {
        margin-top: .29333rem;
        color: #a6a9ae;
        font-size: .32rem;
        font-weight: 400
}

.dialog__container-footer[data-v-b9e16d43] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        gap: .28rem;
        width: 100%;
        height: 1.06667rem;
        margin-top: auto;
        padding: 0 .26667rem
}

.dialog__container-footer button[data-v-b9e16d43] {
        width: 3.12rem;
        height: 1.06667rem;
        color: #d9ac4f;
        font-size: .42667rem;
        text-align: center;
        border-radius: 9rem;
        border: .01333rem solid #D9AC4F;
        background: transparent
}

.dialog__container-footer button[data-v-b9e16d43]:last-of-type {
        width: 4.34667rem;
        color: #8f5206;
        font-weight: 700;
        background: -webkit-linear-gradient(top,#FAE59F 0%,#C4933F 100%);
        background: linear-gradient(180deg,#FAE59F 0%,#C4933F 100%)
}

.dialog__outside[data-v-b9e16d43] {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        cursor: default;
        z-index: 2002
}

.close_img[data-v-b9e16d43] {
        width: .8rem;
        height: .8rem;
        position: absolute;
        bottom: -1.06667rem;
        left: 50%;
        margin-left: -.4rem
}

.van-toast[data-v-6f85c91a] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-6f85c91a] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-6f85c91a] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-6f85c91a] {
        height: 80%
}

.dropdown[data-v-6f85c91a] {
        position: absolute;
        width: 2.48rem;
        height: 1.17333rem;
        color: #a6a9ae;
        text-align: center;
        line-height: 1.17333rem;
        background: #484848;
        outline: none;
        border-radius: .26667rem
}

.dropdown__value[data-v-6f85c91a] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        padding-left: .4rem;
        font-size: .4rem;
        cursor: pointer
}

.dropdown__value span[data-v-6f85c91a] {
        width: .90667rem;
        height: 1.17333rem
}

.dropdown__value i[data-v-6f85c91a] {
        margin-left: .13333rem;
        -webkit-transition: -webkit-transform .3s ease;
        transition: -webkit-transform .3s ease;
        transition: transform .3s ease;
        transition: transform .3s ease,-webkit-transform .3s ease
}

.dropdown__value .arrowActive[data-v-6f85c91a] {
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg)
}

.dropdown__list[data-v-6f85c91a] {
        position: absolute;
        top: 100%;
        left: 0;
        width: 8.61333rem;
        font-size: .37333rem;
        height: .26667rem;
        text-align: start;
        opacity: 0;
        border-radius: .13333rem;
        background-color: #484848;
        overflow: hidden;
        z-index: 1;
        overflow-y: scroll;
        -webkit-transition: opacity .2s ease-in-out,height .2s ease-in-out;
        transition: opacity .2s ease-in-out,height .2s ease-in-out
}

.dropdown__list[data-v-6f85c91a]::-webkit-scrollbar {
        width: 0
}

.dropdown__list-item[data-v-6f85c91a] {
        padding-left: .4rem;
        cursor: pointer
}

.dropdown__list-item span[data-v-6f85c91a] {
        display: inline-block;
        width: 1.33333rem
}

.dropdown__list-item[data-v-6f85c91a]:hover,.dropdown__list-item.active[data-v-6f85c91a] {
        background: #C4933F;
        color: #fff
}

.dropdown__list.active[data-v-6f85c91a] {
        height: 5.33333rem;
        opacity: 1;
        z-index: 10
}

.van-toast[data-v-25df175c] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-25df175c] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-25df175c] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-25df175c] {
        height: 80%
}

.active-container .banner[data-v-25df175c] {
        width: 100%;
        display: block;
        max-height: 4.8rem
}

.active-container .active-box[data-v-25df175c] {
        border-radius: .26667rem;
        padding: .26667rem .34667rem .34667rem;
        margin-top: -.2rem;
        position: relative;
        color: #fff;
        z-index: 2
}

.active-container .active-box .title[data-v-25df175c] {
        font-size: .37333rem;
        font-weight: 600;
        text-align: center;
        margin-bottom: .26667rem
}

.active-container .active-box[data-v-25df175c] img {
        display: block!important;
        width: 100%!important
}

.van-toast[data-v-0ef79ff1] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-0ef79ff1] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-0ef79ff1] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-0ef79ff1] {
        height: 80%
}

.infiniteScroll[data-v-0ef79ff1] {
        min-height: 2.66667rem;
        border-radius: .16rem
}

.infiniteScroll__loading[data-v-0ef79ff1] {
        width: 100%;
        min-height: .73333rem;
        margin-top: auto;
        margin-bottom: .4rem;
        color: #a6a9ae;
        font-size: .37333rem;
        text-align: center
}

.infiniteScroll__loading .van-loading[data-v-0ef79ff1] {
        text-align: center;
        z-index: 999
}

.van-toast[data-v-6c95b906] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-6c95b906] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-6c95b906] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-6c95b906] {
        height: 80%
}

.championship[data-v-6c95b906] {
        padding: .26667rem 0
}

.championship .tabs[data-v-6c95b906] {
        background: #3F3F3F;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex
}

.championship .tabs>div[data-v-6c95b906] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        text-align: center;
        min-height: 1.6rem;
        color: #a6a9ae;
        font-size: .4rem;
        width: 33.3333333333%
}

.championship .tabs>div.active[data-v-6c95b906] {
        font-weight: 900;
        position: relative;
        color: #d9ac4f
}

.championship .tabs>div.active[data-v-6c95b906]:after {
        position: absolute;
        bottom: 0;
        left: 50%;
        -webkit-transform: translateX(-50%);
        transform: translate(-50%);
        display: block;
        content: "";
        width: 0;
        height: 0;
        border-left: .2rem solid transparent;
        border-right: .2rem solid transparent;
        border-bottom: .24rem solid #292929
}

.championship .item[data-v-6c95b906] {
        margin: .26667rem
}

.championship .btn[data-v-6c95b906] {
        margin: .26667rem;
        font-size: .4rem;
        color: #fff;
        font-weight: 700;
        letter-spacing: .016rem;
        background: -webkit-linear-gradient(top,#CFD1DE 0%,#C7C9D9 100%);
        background: linear-gradient(180deg,#CFD1DE 0%,#C7C9D9 100%);
        text-align: center;
        border-radius: .66667rem;
        padding: .2rem 0
}

.championship .btn.active[data-v-6c95b906] {
        color: #8f5206;
        background: -webkit-linear-gradient(top,#FAE59F 0%,#C4933F 100%);
        background: linear-gradient(180deg,#FAE59F 0%,#C4933F 100%)
}

.van-toast[data-v-c4f3162c] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-c4f3162c] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-c4f3162c] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-c4f3162c] {
        height: 80%
}

.dailySignIn__container[data-v-c4f3162c] {
        font-family: Inter,sans-serif;
        padding-bottom: 1.28rem
}

.dailySignIn__container[data-v-c4f3162c] .navbar .navbar__content .navbar__content-left .van-icon,.dailySignIn__container[data-v-c4f3162c] .navbar .navbar__content .navbar__content-title {
        color: #fff
}

.dailySignIn__container .greyBtn[data-v-c4f3162c] {
        -webkit-filter: grayscale(1)!important;
        filter: grayscale(1)!important
}

.dailySignIn__container-hero[data-v-c4f3162c] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-pack: end;
        -webkit-justify-content: flex-end;
        justify-content: flex-end;
        height: 6.13333rem;
        margin-bottom: .42667rem;
        padding: 0 .4rem .26667rem;
        background: url(/assets/png/headerBg-ca048249.png) no-repeat center/cover;
        background-color: #3f3f3f;
        background-position: bottom
}

.dailySignIn__container-hero__header[data-v-c4f3162c] {
        color: #fff
}

.dailySignIn__container-hero__header h1[data-v-c4f3162c] {
        font-size: .53333rem
}

.dailySignIn__container-hero__header h1[data-v-c4f3162c]:last-of-type {
        margin-bottom: 0;
        font-size: .50667rem;
        font-weight: 700
}

.dailySignIn__container-hero__header p[data-v-c4f3162c] {
        font-size: .32rem;
        margin-bottom: .37333rem
}

.dailySignIn__container-hero__header p[data-v-c4f3162c]:first-of-type {
        width: 5.33333rem;
        word-wrap: break-word;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        overflow: hidden
}

.dailySignIn__container-hero__header p[data-v-c4f3162c]:last-of-type {
        margin-bottom: .08rem
}

.dailySignIn__container-hero__header div[data-v-c4f3162c] {
        display: inline-block;
        width: 4rem;
        margin-bottom: .21333rem;
        padding: .21333rem .41333rem .21333rem .14667rem;
        color: #8f5206;
        font-size: .37333rem;
        line-height: .32rem;
        background: #D9AC4F;
        -webkit-clip-path: polygon(100% 0,90% 50%,100% 100%,0 100%,0 0);
        clip-path: polygon(100% 0,90% 50%,100% 100%,0 100%,0 0);
        word-break: break-all
}

.dailySignIn__container-hero__header div span[data-v-c4f3162c] {
        margin-inline:.13333rem;font-size: .53333rem;
        font-weight: 600;
        line-height: 1
}

.dailySignIn__container-hero__footer[data-v-c4f3162c] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        margin-top: .53333rem
}

.dailySignIn__container-hero__footer button[data-v-c4f3162c] {
        width: 3.53333rem;
        height: .8rem;
        padding: .18667rem 0;
        color: #8f5206;
        font-size: .34667rem;
        line-height: .32rem;
        border: none;
        border-radius: 9rem;
        background: -webkit-linear-gradient(top,#FAE59F 0%,#C4933F 100%);
        background: linear-gradient(180deg,#FAE59F 0%,#C4933F 100%);
        cursor: pointer
}

.dailySignIn__container-hero__footer button[data-v-c4f3162c]:hover {
        outline: .01333rem solid #a8acd0
}

.dailySignIn__container-content[data-v-c4f3162c] {
        padding-inline:.32rem}

.dailySignIn__container-content__wrapper[data-v-c4f3162c] {
        display: grid;
        grid-template-columns: repeat(3,1fr);
        justify-items: center;
        gap: .2rem
}

.dailySignIn__container-content__wrapper-block[data-v-c4f3162c] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        width: 2.98667rem;
        height: 2.93333rem;
        background: url(/assets/png/Unsigned-63d76784.png) no-repeat;
        background-position: center;
        background-size: 96% 100%
}

.dailySignIn__container-content__wrapper-block__header[data-v-c4f3162c] {
        position: relative;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex
}

.dailySignIn__container-content__wrapper-block__header img[data-v-c4f3162c] {
        width: 2.98667rem;
        height: .82667rem;
        visibility: hidden
}

.dailySignIn__container-content__wrapper-block__header span[data-v-c4f3162c] {
        position: absolute;
        top: calc(50% - .02667rem);
        left: 50%;
        -webkit-transform: translate(-50%,-50%);
        transform: translate(-50%,-50%);
        color: #fff;
        font-size: .42667rem;
        line-height: 1
}

.dailySignIn__container-content__wrapper-block>img[data-v-c4f3162c] {
        width: 1.06667rem;
        height: 1.06667rem;
        margin-block:.18667rem .27773rem}

.dailySignIn__container-content__wrapper-block>span[data-v-c4f3162c] {
        color: #fff;
        font-size: .34667rem
}

.dailySignIn__container-content__wrapper-block img[data-v-c4f3162c] {
        max-width: 100%
}

.dailySignIn__container-content__wrapper-block[data-v-c4f3162c]:last-of-type {
        position: relative;
        width: 100%;
        grid-column: 1/-1;
        background: url(/assets/png/day7Bg-2dc29da0.png) no-repeat;
        background-position: center;
        background-size: 100% 100%
}

.dailySignIn__container-content__wrapper-block:last-of-type p[data-v-c4f3162c] {
        color: #fff;
        line-height: .66667rem;
        font-size: .4rem
}

.dailySignIn__container-content__wrapper-block:last-of-type>div[data-v-c4f3162c] {
        position: absolute;
        top: 50%;
        right: 1.8rem;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        gap: .36rem
}

.dailySignIn__container-content__wrapper-block:last-of-type>div>span[data-v-c4f3162c] {
        color: #fff;
        font-size: .4rem
}

.dailySignIn__container-content__wrapper-block.signed[data-v-c4f3162c] {
        background: url(/assets/png/Signed-5553686b.png) no-repeat;
        background-position: center;
        background-size: 96% 100%
}

.dailySignIn__container-content__wrapper-block.signed img[data-v-c4f3162c] {
        visibility: visible
}

.dailySignIn__container-content__wrapper-block.signed span[data-v-c4f3162c] {
        color: #fff!important
}

.dailySignIn__container-content__wrapper-block.signed[data-v-c4f3162c]:last-of-type {
        background: url(/assets/png/day7BgActive-6c1c1d3f.png) no-repeat;
        background-position: center;
        background-size: 100% 100%
}

.dailySignIn__container-content__footer[data-v-c4f3162c] {
        width: 100%;
        height: 1.06667rem;
        margin-top: 1.45333rem;
        padding: 0 1.13333rem;
        text-align: center
}

.dailySignIn__container-content__footer button[data-v-c4f3162c] {
        width: 100%;
        height: 100%;
        padding-block:.10667rem;color: #8f5206;
        font-size: .48rem;
        border: none;
        border-radius: 9rem;
        background: -webkit-linear-gradient(top,#FAE59F 0%,#C4933F 100%);
        background: linear-gradient(180deg,#FAE59F 0%,#C4933F 100%)
}

.dailySignIn__container-content__footer button[data-v-c4f3162c]:active {
        background: -webkit-linear-gradient(top,#FAE59F 0%,#C4933F 100%);
        background: linear-gradient(180deg,#FAE59F 0%,#C4933F 100%)
}

.van-toast[data-v-bbde2945] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-bbde2945] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-bbde2945] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-bbde2945] {
        height: 80%
}

.dailySignIn__container[data-v-bbde2945] {
        font-family: Inter,sans-serif;
        padding-bottom: 1.28rem
}

.dailySignIn__container[data-v-bbde2945] .navbar .navbar__content .navbar__content-left .van-icon,.dailySignIn__container[data-v-bbde2945] .navbar .navbar__content .navbar__content-title {
        color: #fff
}

.dailySignIn__container .task-banner[data-v-bbde2945] {
        padding: .13333rem .26667rem;
        margin-bottom: .4rem;
        background: #3F3F3F;
        color: #fff;
        height: -webkit-fit-content;
        height: fit-content;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: end;
        -webkit-align-items: flex-end;
        align-items: flex-end;
        position: relative
}

.dailySignIn__container .task-banner div[data-v-bbde2945] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.dailySignIn__container .task-banner div img[data-v-bbde2945] {
        width: 2.66667rem;
        height: 2.66667rem
}

.dailySignIn__container .task-banner .banner-title[data-v-bbde2945] {
        font-size: .48rem;
        color: #fff;
        margin-bottom: .26667rem
}

.dailySignIn__container .task-banner .banner-content[data-v-bbde2945] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: start;
        -webkit-align-items: flex-start;
        align-items: flex-start
}

.dailySignIn__container .task-panel[data-v-bbde2945] {
        padding: 0 .26667rem
}

.dailySignIn__container .task-panel .task-item[data-v-bbde2945] {
        width: 100%;
        background: #3F3F3F;
        border-radius: .26667rem;
        overflow: hidden;
        padding: 0 0 .13333rem;
        margin-bottom: .26667rem
}

.dailySignIn__container .task-panel .task-item img[data-v-bbde2945] {
        width: .53333rem;
        height: .53333rem
}

.dailySignIn__container .task-panel .task-item-header[data-v-bbde2945] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        margin-top: -.06667rem;
        border-bottom: .01333rem solid #292929;
        padding-right: .26667rem
}

.dailySignIn__container .task-panel .task-item-header img[data-v-bbde2945] {
        width: .53333rem;
        height: .53333rem
}

.dailySignIn__container .task-panel .task-item-header .hearder-status[data-v-bbde2945] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        min-width: 3.46667rem;
        padding: .2rem 0;
        color: #fff;
        border-bottom-right-radius: .33333rem;
        font-size: .37333rem;
        font-weight: 700
}

.dailySignIn__container .task-panel .task-item-header .hearder-status.new[data-v-bbde2945] {
        background: #FF7F22
}

.dailySignIn__container .task-panel .task-item-header .hearder-status.week[data-v-bbde2945] {
        background: #D9AC4F;
        color: #8f5206
}

.dailySignIn__container .task-panel .task-item-header .hearder-status.day[data-v-bbde2945] {
        background: #8F5206;
        color: #fae59f
}

.dailySignIn__container .task-panel .task-item-header .headerR[data-v-bbde2945] {
        color: #888;
        font-size: .37333rem
}

.dailySignIn__container .task-panel .task-item-header .headerR.new.status1[data-v-bbde2945],.dailySignIn__container .task-panel .task-item-header .headerR.new.status2[data-v-bbde2945],.dailySignIn__container .task-panel .task-item-header .headerR.other.status2[data-v-bbde2945],.dailySignIn__container .task-panel .task-item-header .headerR.other.status3[data-v-bbde2945] {
        color: #fff
}

.dailySignIn__container .task-panel .task-item-header .hearder-type[data-v-bbde2945] {
        min-width: 3.2rem;
        height: .66667rem;
        line-height: .66667rem;
        border-radius: .66667rem;
        text-align: center;
        padding: 0 .13333rem;
        color: #fff;
        font-size: .37333rem
}

.dailySignIn__container .task-panel .task-item-header .hearder-type.n1[data-v-bbde2945] {
        color: #a6a9ae
}

.dailySignIn__container .task-panel .task-item-header .hearder-type.n2[data-v-bbde2945] {
        background: -webkit-linear-gradient(left,#D9AC4F 0%,#D9AC4F 95%);
        background: linear-gradient(90deg,#D9AC4F 0%,#D9AC4F 95%)
}

.dailySignIn__container .task-panel .task-item-header .hearder-type.n3[data-v-bbde2945] {
        background: -webkit-linear-gradient(left,#A6ACD0 0%,#C2CAF4 95%);
        background: linear-gradient(90deg,#A6ACD0 0%,#C2CAF4 95%)
}

.dailySignIn__container .task-panel .task-item-header .uncomplete[data-v-bbde2945] {
        background: -webkit-linear-gradient(left,#FF7F22 0%,#FF7F22 95%)!important;
        background: linear-gradient(90deg,#FF7F22 0%,#FF7F22 95%)!important
}

.dailySignIn__container .task-panel .task-item-header .completed[data-v-bbde2945] {
        background: -webkit-linear-gradient(top,#8F5206 0%,#8F5206 100%);
        background: linear-gradient(180deg,#8F5206 0%,#8F5206 100%)
}

.dailySignIn__container .task-panel .task-item-header .gocomplete[data-v-bbde2945] {
        color: #f95959;
        border: .01333rem solid #F95959;
        background: #fff
}

.dailySignIn__container .task-panel .task-item-type[data-v-bbde2945] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        margin: .26667rem .26667rem 0
}

.dailySignIn__container .task-panel .task-item-type img[data-v-bbde2945] {
        width: .53333rem;
        height: .53333rem
}

.dailySignIn__container .task-panel .task-item-type .type-title[data-v-bbde2945] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        padding-left: .69333rem;
        height: .64rem;
        background-size: .64rem;
        background-repeat: no-repeat;
        background-position: left;
        color: #666;
        color: #fff
}

.dailySignIn__container .task-panel .task-item-type .type-title.weeklyType1[data-v-bbde2945] {
        background-image: url(/assets/svg/weeklyType1-105818fb.svg)
}

.dailySignIn__container .task-panel .task-item-type .type-title.weeklyType2[data-v-bbde2945] {
        background-image: url(/assets/svg/weeklyType2-63495183.svg)
}

.dailySignIn__container .task-panel .task-item-type .type-title.weeklyType3[data-v-bbde2945] {
        background-image: url(/assets/svg/weeklyType3-560185a5.svg)
}

.dailySignIn__container .task-panel .task-item-type .type-title.weeklyType4[data-v-bbde2945] {
        background-image: url(/assets/svg/weeklyType4-f5259147.svg)
}

.dailySignIn__container .task-panel .task-item-type .type-title.weeklyType5[data-v-bbde2945] {
        background-image: url(/assets/svg/weeklyType5-8b5a4d9b.svg)
}

.dailySignIn__container .task-panel .task-item-type .type-title.weeklyType6[data-v-bbde2945] {
        background-image: url(/assets/svg/weeklyType6-b4b8005a.svg)
}

.dailySignIn__container .task-panel .task-item-type .type-title.weeklyType7[data-v-bbde2945] {
        background-image: url(/assets/svg/weeklyType7-5ed28e7d.svg)
}

.dailySignIn__container .task-panel .task-item-type .type-title.weeklyType8[data-v-bbde2945] {
        background-image: url(/assets/svg/weeklyType8-6bb1ae2c.svg)
}

.dailySignIn__container .task-panel .task-item-type .type-title.weeklyType9[data-v-bbde2945] {
        background-image: url(/assets/svg/weeklyType9-864e3d73.svg)
}

.dailySignIn__container .task-panel .task-item-type .type-title.weeklyType10[data-v-bbde2945] {
        background-image: url(/assets/svg/weeklyType10-01d8a8bb.svg)
}

.dailySignIn__container .task-panel .task-item-type .type-title.weeklyType11[data-v-bbde2945] {
        background-image: url(/assets/svg/weeklyType11-837e1648.svg)
}

.dailySignIn__container .task-panel .task-item-type .type-title.weeklyType12[data-v-bbde2945] {
        background-image: url(/assets/svg/weeklyType12-7ffe96b0.svg)
}

.dailySignIn__container .task-panel .task-item-type .type-title.new[data-v-bbde2945] {
        background-image: url(/assets/png/new-b1787d33.png)
}

.dailySignIn__container .task-panel .task-item-type .type-tip[data-v-bbde2945] {
        color: #fae59f;
        margin-left: .26667rem
}

.dailySignIn__container .task-panel .task-item-description[data-v-bbde2945] {
        color: #a6a9ae;
        padding: .26667rem;
        background: #303030;
        border-radius: .13333rem;
        margin: .26667rem;
        font-size: .29333rem
}

.dailySignIn__container .task-panel .task-item-bottom[data-v-bbde2945] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        padding: .13333rem .26667rem;
        color: #fff
}

.dailySignIn__container .task-panel .task-item-bottom .bottom-title[data-v-bbde2945] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        color: #d9ac4f;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.dailySignIn__container .task-panel .task-item-bottom img[data-v-bbde2945] {
        margin-right: .13333rem
}

.dailySignIn__container .task-panel .task-item .btn[data-v-bbde2945] {
        color: #fff;
        font-size: .4rem;
        font-weight: 700;
        border-radius: .53333rem;
        text-align: center;
        padding: .16rem 0;
        margin: .26667rem .2rem .13333rem
}

.dailySignIn__container .task-panel .task-item .btnOther.status1[data-v-bbde2945] {
        color: #d9ac4f;
        border: .01333rem solid #D9AC4F
}

.dailySignIn__container .task-panel .task-item .btnOther.status2[data-v-bbde2945] {
        background: -webkit-linear-gradient(top,#FAE59F 0%,#C4933F 100%);
        background: linear-gradient(180deg,#FAE59F 0%,#C4933F 100%);
        color: #8f5206
}

.dailySignIn__container .task-panel .task-item .btnOther.status3[data-v-bbde2945] {
        background: -webkit-linear-gradient(top,#A9AAB5 0%,#6F7381 100%);
        background: linear-gradient(180deg,#A9AAB5 0%,#6F7381 100%)
}

.dailySignIn__container .task-panel .task-item .btnNew.status0[data-v-bbde2945] {
        color: #d9ac4f;
        border: .01333rem solid #D9AC4F
}

.dailySignIn__container .task-panel .task-item .btnNew.status1[data-v-bbde2945] {
        background: -webkit-linear-gradient(top,#FAE59F 0%,#C4933F 100%);
        background: linear-gradient(180deg,#FAE59F 0%,#C4933F 100%);
        color: #8f5206
}

.dailySignIn__container .task-panel .task-item .btnNew.status2[data-v-bbde2945],.dailySignIn__container .task-panel .task-item .btnNew.status3[data-v-bbde2945] {
        background: -webkit-linear-gradient(top,#A9AAB5 0%,#6F7381 100%);
        background: linear-gradient(180deg,#A9AAB5 0%,#6F7381 100%)
}

.dailySignIn__container .navi-record[data-v-bbde2945] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        color: #fff
}

.dailySignIn__container .navi-record img[data-v-bbde2945] {
        width: .58667rem;
        height: .58667rem;
        margin-right: .13333rem
}

.dailySignIn__container .dialog-wrapper[data-v-bbde2945] {
        padding: .26667rem 0;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.dailySignIn__container .dialog-wrapper img[data-v-bbde2945] {
        width: 3.73333rem;
        height: 2.46667rem;
        position: relative;
        margin-top: -.74667rem
}

.dailySignIn__container .dialog-wrapper .dialog-title[data-v-bbde2945] {
        color: #fff;
        margin: .4rem 0 .26667rem;
        font-size: .48rem;
        font-weight: 700;
        padding: 0 .13333rem
}

.dailySignIn__container .dialog-wrapper .dialog-tips[data-v-bbde2945] {
        color: #666;
        font-size: .32rem;
        margin: 0 0 .32rem
}

.dailySignIn__container .dialog-wrapper .dialog-para[data-v-bbde2945] {
        color: #666;
        font-size: .32rem;
        padding: 0 .26667rem
}

.dailySignIn__container .dialog-wrapper .dialog-btn[data-v-bbde2945] {
        width: 5.6rem;
        height: 1.06667rem;
        text-align: center;
        line-height: 1.06667rem;
        background: -webkit-linear-gradient(top,#FAE59F,#C4933F);
        background: linear-gradient(180deg,#FAE59F,#C4933F);
        border-radius: 1.06667rem;
        font-weight: 700;
        color: #8f5206;
        font-size: .42667rem;
        margin-top: .53333rem
}

.dailySignIn__container .dialog-wrapper .dialog-content[data-v-bbde2945] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        color: #d9ac4f
}

.dailySignIn__container .dialog-wrapper .dialog-content img[data-v-bbde2945] {
        width: .4rem;
        height: .4rem;
        margin: 0 .13333rem 0 0
}

.dailySignIn__container .dialog-wrapper .dialog-footer[data-v-bbde2945] {
        position: relative;
        bottom: -1.46667rem
}

.dailySignIn__container .dialog-wrapper .dialog-footer img[data-v-bbde2945] {
        width: .8rem;
        height: .8rem
}

[data-v-bbde2945] .van-dialog.noOverHidden {
        overflow: inherit
}

.van-toast[data-v-2ed7e395] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-2ed7e395] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-2ed7e395] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-2ed7e395] {
        height: 80%
}

.rule[data-v-2ed7e395] {
        width: calc(100% - .64rem);
        border: .05333rem solid #C4933F;
        border-top: none;
        margin: auto;
        padding: .32rem;
        background-color: #3f3f3f;
        border-radius: .26667rem
}

.rule .head[data-v-2ed7e395] {
        width: calc(100% + .74667rem);
        position: relative;
        left: -.37333rem;
        top: -.32rem;
        height: .88rem;
        line-height: .88rem;
        background-image: url(/assets/svg/rulehead-fa1d3b57.svg);
        background-repeat: no-repeat;
        background-size: contain;
        color: #8f5206;
        font-family: Inter;
        font-size: .42667rem;
        font-style: normal;
        font-weight: 700;
        text-align: center
}

.rule>div[data-v-2ed7e395]:not(.head) {
        color: #fff;
        font-size: .34667rem;
        line-height: .45333rem;
        margin-bottom: .4rem;
        padding-left: .34667rem;
        position: relative
}

.rule>div[data-v-2ed7e395]:not(.head):before {
        content: "";
        display: block;
        position: absolute;
        left: 0;
        top: .13333rem;
        height: .16rem;
        width: .16rem;
        -webkit-box-flex: 0;
        -webkit-flex: none;
        flex: none;
        border-radius: .02667rem;
        background: -webkit-linear-gradient(315deg,#D9AC4F,#D9AC4F);
        background: linear-gradient(135deg,#D9AC4F,#D9AC4F);
        -webkit-transform: rotate(45deg);
        transform: rotate(45deg)
}

.van-toast[data-v-bb39b164] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-bb39b164] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-bb39b164] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-bb39b164] {
        height: 80%
}

.first_list[data-v-bb39b164] {
        padding: .32rem
}

.van-toast[data-v-318225d9] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-318225d9] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-318225d9] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-318225d9] {
        height: 80%
}

.package-desc[data-v-318225d9] {
        border-radius: .53333rem;
        border: .01333rem solid #FFF;
        min-width: 2.4rem;
        max-width: 4rem;
        height: .66667rem;
        color: #fff;
        line-height: .66667rem;
        font-size: .32rem;
        text-align: center;
        padding: 0 .13333rem
}

.package-banner[data-v-318225d9] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        min-height: 3.86667rem;
        background: -webkit-linear-gradient(356deg,#8F5206 1.69%,#D9AC4F 95.43%);
        background: linear-gradient(94deg,#8F5206 1.69%,#D9AC4F 95.43%);
        margin-bottom: .26667rem;
        padding: .34667rem
}

.package-banner img[data-v-318225d9] {
        display: block;
        width: 2.69333rem;
        height: 3.2rem
}

.package-banner-text[data-v-318225d9] {
        color: #fff;
        margin-left: .45333rem;
        width: 100%
}

.package-banner-text>div[data-v-318225d9] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: end;
        -webkit-justify-content: end;
        justify-content: end
}

.package-banner-text h3[data-v-318225d9] {
        font-size: .48rem;
        font-style: normal;
        font-weight: 700;
        line-height: .48rem;
        margin-bottom: .4rem
}

.package-banner-text h4[data-v-318225d9] {
        font-size: .34667rem;
        font-style: normal;
        font-weight: 500;
        line-height: .34667rem;
        margin-bottom: .32rem
}

.package-banner-text p[data-v-318225d9] {
        font-size: .29333rem;
        font-style: normal;
        font-weight: 400;
        line-height: .32rem;
        margin-bottom: .18667rem
}

.package-banner-text span[data-v-318225d9] {
        width: .37333rem;
        height: .37333rem;
        display: inline-block;
        border-radius: 50%;
        background: #FAE59F;
        color: #8f5206;
        text-align: center;
        font-size: .29333rem;
        font-style: normal;
        font-weight: 500;
        line-height: .37333rem;
        margin-right: .13333rem
}

.package-time[data-v-318225d9] {
        color: #d9ac4f;
        font-size: .42667rem;
        font-style: normal;
        font-weight: 700;
        line-height: .42667rem;
        text-align: center
}

.package-rule[data-v-318225d9] {
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        flex: 1;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-pack: start;
        -webkit-justify-content: flex-start;
        justify-content: flex-start;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        margin: .26667rem .32rem 0;
        border-radius: .26667rem;
        overflow: hidden;
        background: #3F3F3F;
        color: #a6a9ae
}

.package-rule-titlebox[data-v-318225d9] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        flex: 1;
        width: 100%;
        background: -webkit-linear-gradient(bottom,#C4933F 0%,#FAE59F 100%);
        background: linear-gradient(0deg,#C4933F 0%,#FAE59F 100%)
}

.package-rule ul[data-v-318225d9] {
        width: 100%;
        height: 100%;
        padding: .13333rem 0
}

.package-rule ul li[data-v-318225d9] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        width: 100%
}

.package-rule ul li div[data-v-318225d9] {
        font-size: .37333rem;
        padding: .26667rem .13333rem;
        text-align: center;
        border-right: .01333rem solid #707070;
        min-height: 1.6rem;
        width: 3.93333rem
}

.package-rule ul li div[data-v-318225d9]:first-child {
        width: 2.66667rem
}

.package-rule ul li div[data-v-318225d9]:last-child {
        border-right: none;
        width: 2.66667rem
}

.package-rule ul li span[data-v-318225d9] {
        color: #d9ac4f;
        font-size: .37333rem
}

.package-rule-title[data-v-318225d9] {
        width: 100%;
        min-height: 1.06667rem;
        padding-block:.34667rem;font-size: .42667rem;
        line-height: .32rem;
        text-align: center;
        color: #8f5206
}

.package-rule-content[data-v-318225d9] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        width: 100%;
        height: 100%
}

.package-rule-content>div[data-v-318225d9] {
        width: 100%;
        padding: .34667rem 0;
        font-size: .37333rem;
        line-height: .37333rem;
        text-align: center;
        border-bottom: .01333rem solid #ff8180
}

.package-rule-content>div[data-v-318225d9]:last-of-type {
        border-bottom: none
}

.package-tips[data-v-318225d9] {
        background: #292929;
        padding: .26667rem;
        color: #d9ac4f;
        font-size: .32rem;
        font-style: normal;
        font-weight: 500;
        line-height: .48rem;
        border-radius: .66667rem;
        margin: .26667rem .26667rem 0;
        border: .01333rem solid #3F3F3F
}

.package-tips svg[data-v-318225d9] {
        width: .48rem;
        height: .48rem;
        vertical-align: middle
}

.package-list[data-v-318225d9] {
        padding: 0 .32rem;
        margin-top: .26667rem
}

.package-item[data-v-318225d9] {
        margin-bottom: .53333rem
}

.package-item-head[data-v-318225d9] {
        background: url(/assets/png/head-50a17e03.png) no-repeat center/cover;
        height: 1.33333rem;
        font-size: .37333rem;
        font-style: normal;
        font-weight: 700;
        text-align: center;
        line-height: .53333rem;
        color: #fff;
        padding-left: 1.06667rem;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center
}

.package-item-top[data-v-318225d9] {
        background: #3F3F3F;
        padding: .26667rem .4rem;
        text-align: center
}

.package-item-top ul[data-v-318225d9] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.package-item-top .moeny[data-v-318225d9] {
        width: 4.13333rem;
        height: 2.4rem;
        border-radius: .26667rem;
        background: url(/assets/png/bg-9431f309.png) no-repeat top/contain #D9AC4F
}

.package-item-top h3[data-v-318225d9] {
        color: #8f5206;
        text-align: center;
        font-size: .42667rem;
        font-style: normal;
        font-weight: 700;
        line-height: 1.06667rem
}

.package-item-top p[data-v-318225d9] {
        color: #8f5206;
        font-size: .32rem;
        font-style: normal;
        font-weight: 400;
        line-height: .32rem
}

.package-item-top svg[data-v-318225d9] {
        width: .64rem;
        height: .64rem
}

.package-item-box[data-v-318225d9] {
        background: -webkit-linear-gradient(right,#C49340 0%,#FAE59F 100%);
        background: linear-gradient(270deg,#C49340 0%,#FAE59F 100%);
        border-radius: 0 0 .26667rem .26667rem
}

.package-item-tip[data-v-318225d9] {
        background: #303030;
        border-radius: .13333rem;
        padding: .26667rem;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        color: #a6a9ae;
        font-size: .32rem;
        font-style: normal;
        font-weight: 400;
        line-height: .42667rem;
        margin-top: .26667rem
}

.package-item-tip span[data-v-318225d9]:last-child {
        color: #ff7d06;
        font-family: Inter;
        font-size: .37333rem;
        font-style: normal;
        font-weight: 500;
        line-height: .42667rem
}

.package-item-bottom[data-v-318225d9] {
        height: .93333rem;
        color: #fff;
        text-align: center;
        line-height: .93333rem
}

.package-item-bottom.done[data-v-318225d9] {
        background: -webkit-linear-gradient(right,#FF3134 0%,#F97450 100%);
        background: linear-gradient(270deg,#FF3134 0%,#F97450 100%)
}

.package-item-bottom.p[data-v-318225d9] {
        background: -webkit-linear-gradient(right,#A4B1DE 0%,#BDC9EE 100.85%);
        background: linear-gradient(270deg,#A4B1DE 0%,#BDC9EE 100.85%)
}

.package-item-btn[data-v-318225d9] {
        margin-top: .26667rem;
        background: -webkit-linear-gradient(top,#FAE59F 0%,#C4933F 100%);
        background: linear-gradient(180deg,#FAE59F 0%,#C4933F 100%);
        height: .93333rem;
        text-align: center;
        line-height: .93333rem;
        color: #fff;
        border-radius: .66667rem;
        font-size: .42667rem;
        font-weight: 700
}

.package-item-btn.done[data-v-318225d9] {
        background: -webkit-linear-gradient(top,#CFD1DF 0%,#C8CADA 100%);
        background: linear-gradient(180deg,#CFD1DF 0%,#C8CADA 100%);
        box-shadow: 0 .05333rem #b6bad0
}

.package-item-btn.apply[data-v-318225d9] {
        background: -webkit-linear-gradient(top,#D8AB4E 0%,#915408 100%);
        background: linear-gradient(180deg,#D8AB4E 0%,#915408 100%)
}

.package-item-btn.receive[data-v-318225d9] {
        background: -webkit-linear-gradient(top,#FAE59F 0%,#C4933F 100%);
        background: linear-gradient(180deg,#FAE59F 0%,#C4933F 100%)
}

.van-toast[data-v-c6772144] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-c6772144] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-c6772144] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-c6772144] {
        height: 80%
}

.pointMall__container-treasureActivity[data-v-c6772144] {
        position: relative;
        left: 50%;
        top: -1.06667rem;
        -webkit-transform: translateX(-50%);
        transform: translate(-50%);
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: start;
        -webkit-align-items: flex-start;
        align-items: flex-start;
        gap: .13333rem;
        width: 8.56rem;
        height: 3.73333rem;
        padding: .13333rem .2rem .26667rem;
        border-radius: .13333rem;
        background: url(/assets/png/headerBodyBg-7061df5f.png) no-repeat center #D9AC4F;
        background-size: cover;
        box-shadow: 0 .05333rem .16rem #ffffff40 inset
}

.pointMall__container-treasureActivity div[data-v-c6772144] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.pointMall__container-treasureActivity div svg[data-v-c6772144] {
        width: .64rem;
        height: .64rem
}

.pointMall__container-treasureActivity div span[data-v-c6772144] {
        display: inline-block;
        color: #8f5206;
        font-family: Inter;
        font-size: .48rem;
        font-style: normal;
        font-weight: 700;
        line-height: .64rem;
        margin-left: .05333rem
}

.pointMall__container-treasureActivity .treasureActivity-tips[data-v-c6772144] {
        color: #fff;
        font-family: Inter;
        font-size: .37333rem;
        font-style: normal;
        font-weight: 400;
        line-height: .34667rem;
        margin-bottom: .18667rem
}

.pointMall__container-treasureActivity .treasureActivity-desc[data-v-c6772144] {
        color: #8f5206;
        font-family: Inter;
        font-size: .32rem;
        font-style: normal;
        font-weight: 400;
        line-height: .32rem;
        margin-bottom: .4rem
}

.pointMall__container-treasureActivity button[data-v-c6772144] {
        width: 6.93333rem;
        height: .93333rem;
        color: #8f5206;
        font-size: .37333rem;
        font-weight: 700;
        border: none;
        text-align: center;
        border-radius: .66667rem;
        background: #FAE59F;
        margin: 0 auto
}

.van-toast[data-v-7189a457] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-7189a457] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-7189a457] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-7189a457] {
        height: 80%
}

.infiniteScroll[data-v-7189a457] {
        min-height: 2.66667rem;
        border-radius: .16rem
}

.infiniteScroll__loading[data-v-7189a457] {
        width: 100%;
        min-height: 1.4rem;
        margin-top: auto;
        padding-bottom: .4rem;
        color: #a6a9ae;
        font-size: .37333rem;
        text-align: center
}

.infiniteScroll__loading .van-loading[data-v-7189a457] {
        text-align: center;
        z-index: 999
}

.infiniteScroll__loading .empty[data-v-7189a457] {
        margin-top: .53333rem
}

.van-toast[data-v-e70c16ad] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-e70c16ad] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-e70c16ad] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-e70c16ad] {
        height: 80%
}

.pointMall__container-products__content-item[data-v-e70c16ad] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        width: 4.4rem
}

.pointMall__container-products__content-item__header[data-v-e70c16ad],.pointMall__container-products__content-item__body[data-v-e70c16ad] {
        position: relative;
        width: 100%
}

.pointMall__container-products__content-item__header[data-v-e70c16ad] {
        height: 3.30667rem;
        color: #fae59f;
        border-top-left-radius: .26667rem;
        border-top-right-radius: .26667rem;
        background: -webkit-linear-gradient(#6F7381,#A9AAB5);
        background: linear-gradient(#6F7381,#A9AAB5)
}

.pointMall__container-products__content-item__header-left[data-v-e70c16ad],.pointMall__container-products__content-item__header-redeemed[data-v-e70c16ad],.pointMall__container-products__content-item__header>img[data-v-e70c16ad] {
        position: absolute
}

.pointMall__container-products__content-item__header img[data-v-e70c16ad] {
        left: 50%;
        -webkit-transform: translate(-50%,0);
        transform: translate(-50%);
        height: 3.3rem;
        width: 4.4rem;
        object-fit: cover;
        border-top-right-radius: .26667rem;
        border-top-left-radius: .26667rem
}

.pointMall__container-products__content-item__header-left[data-v-e70c16ad] {
        bottom: 0;
        width: 100%;
        height: .53333rem;
        padding: .12rem .08rem;
        font-size: .29333rem;
        line-height: .32rem;
        background: #D9AC4F;
        color: #8f5206;
        z-index: 1
}

.pointMall__container-products__content-item__header-left>span[data-v-e70c16ad] {
        font-size: .4rem
}

.pointMall__container-products__content-item__header-redeemed[data-v-e70c16ad] {
        bottom: 0;
        right: 0;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        width: 1.68rem;
        height: 1rem;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        padding-block:.09333rem;font-size: .29333rem;
        background: url(/assets/png/redeemdBg-f275ec3b.png) no-repeat right/contain;
        z-index: 2
}

.pointMall__container-products__content-item__header-redeemed>span[data-v-e70c16ad] {
        font-size: .4rem;
        font-weight: 700;
        line-height: 1
}

.pointMall__container-products__content-item__footer[data-v-e70c16ad] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: start;
        -webkit-align-items: flex-start;
        align-items: flex-start;
        width: 100%;
        padding: .24rem .18667rem;
        border-bottom-left-radius: .26667rem;
        border-bottom-right-radius: .26667rem;
        background: #3F3F3F
}

.pointMall__container-products__content-item__footer>span[data-v-e70c16ad] {
        color: #fff;
        font-size: .37333rem;
        line-height: .48rem;
        font-weight: 700
}

.pointMall__container-products__content-item__footer .product-title[data-v-e70c16ad] {
        height: .8rem;
        width: 100%;
        line-height: .4rem;
        padding-right: .2rem;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2
}

.pointMall__container-products__content-item__footer div[data-v-e70c16ad] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        gap: .28rem;
        margin-top: .37333rem
}

.pointMall__container-products__content-item__footer div svg[data-v-e70c16ad] {
        width: .4rem;
        height: .4rem
}

.pointMall__container-products__content-item__footer div span[data-v-e70c16ad] {
        color: #d9ac4f;
        font-size: .34667rem;
        line-height: .34667rem;
        font-weight: 500
}

.pointMall__container-products__content-item__footer button[data-v-e70c16ad] {
        width: 3.98667rem;
        height: .8rem;
        margin-top: .29333rem;
        margin-inline:auto;color: #8f5206;
        font-weight: .37333rem;
        text-shadow: 0 .02667rem .01333rem #ff8984;
        border: none;
        border-radius: 9rem;
        background: -webkit-linear-gradient(#FAE59F,#C4933F);
        background: linear-gradient(#FAE59F,#C4933F);
        cursor: pointer;
        font-size: .37333rem
}

.van-toast[data-v-f66d0b52] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-f66d0b52] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-f66d0b52] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-f66d0b52] {
        height: 80%
}

.treasure__container__treasure-item[data-v-f66d0b52] {
        width: 100%
}

.treasure__container__treasure-item__header[data-v-f66d0b52],.treasure__container__treasure-item__footer[data-v-f66d0b52] {
        position: relative;
        width: 100%
}

.treasure__container__treasure-item__header[data-v-f66d0b52] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        padding-block:.26667rem;color: #fff;
        border-top-left-radius: .32rem;
        border-top-right-radius: .32rem;
        background: -webkit-linear-gradient(#6F7381,#A9AAB5);
        background: linear-gradient(#6F7381,#A9AAB5)
}

.treasure__container__treasure-item__header .treasure-state[data-v-f66d0b52] {
        position: absolute;
        top: .26667rem;
        left: 0;
        padding: .04rem .04rem .04rem 0;
        font-size: .29333rem;
        height: .58667rem;
        line-height: .48rem;
        border-top-right-radius: 9rem;
        border-bottom-right-radius: 9rem;
        z-index: 2
}

.treasure__container__treasure-item__header .treasure-state span[data-v-f66d0b52] {
        display: block;
        height: 100%;
        border-top-right-radius: 9rem;
        border-bottom-right-radius: 9rem;
        padding: 0 .26667rem 0 .44rem;
        color: #fff;
        white-space: nowrap;
        z-index: 1
}

.treasure__container__treasure-item__header img[data-v-f66d0b52] {
        width: 6.29333rem;
        height: 6.30667rem
}

.treasure__container__treasure-item__footer[data-v-f66d0b52] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: start;
        -webkit-align-items: flex-start;
        align-items: flex-start;
        padding: .37333rem .2rem .53333rem;
        border-bottom-left-radius: .32rem;
        border-bottom-right-radius: .32rem;
        background: #3F3F3F
}

.treasure__container__treasure-item__footer-title[data-v-f66d0b52] {
        margin-bottom: .33333rem;
        color: #fff;
        font-family: Inter;
        font-size: .4rem;
        font-style: normal;
        font-weight: 400;
        line-height: .48rem
}

.treasure__container__treasure-item__footer-title>span[data-v-f66d0b52] {
        color: #d9ac4f;
        font-weight: 700
}

.treasure__container__treasure-item__footer-progressTitle[data-v-f66d0b52] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        width: 100%;
        color: #a6a9ae;
        font-size: .32rem
}

.treasure__container__treasure-item__footer .van-progress[data-v-f66d0b52] {
        width: 100%;
        margin-block:.13333rem}

.treasure__container__treasure-item__footer-progressDetails[data-v-f66d0b52] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        width: 100%;
        color: #fff;
        margin-bottom: .26667rem
}

.treasure__container__treasure-item__footer-progressDetails>span[data-v-f66d0b52] {
        color: #848592;
        font-size: .32rem
}

.treasure__container__treasure-item__footer-progressDetails>div span[data-v-f66d0b52] {
        font-size: .32rem;
        font-weight: 600
}

.treasure__container__treasure-item__footer-ending[data-v-f66d0b52],.treasure__container__treasure-item__footer-cost[data-v-f66d0b52] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        width: 100%;
        height: .8rem;
        margin-block:.06667rem;background: #292929
}

.treasure__container__treasure-item__footer-ending>span[data-v-f66d0b52],.treasure__container__treasure-item__footer-ending>div[data-v-f66d0b52],.treasure__container__treasure-item__footer-cost>span[data-v-f66d0b52],.treasure__container__treasure-item__footer-cost>div[data-v-f66d0b52] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        height: 100%;
        padding: .06667rem .13333rem
}

.treasure__container__treasure-item__footer-ending>span[data-v-f66d0b52],.treasure__container__treasure-item__footer-cost>span[data-v-f66d0b52] {
        -webkit-box-flex: .6;
        -webkit-flex: .6;
        flex: .6;
        color: #a6a9ae
}

.treasure__container__treasure-item__footer-ending>div[data-v-f66d0b52],.treasure__container__treasure-item__footer-cost>div[data-v-f66d0b52] {
        -webkit-box-flex: .4;
        -webkit-flex: .4;
        flex: .4;
        -webkit-box-pack: end;
        -webkit-justify-content: flex-end;
        justify-content: flex-end;
        color: #d9ac4f;
        font-size: .4rem;
        font-weight: 600
}

.treasure__container__treasure-item__footer-ending>div img[data-v-f66d0b52],.treasure__container__treasure-item__footer-cost>div img[data-v-f66d0b52] {
        width: .4rem;
        height: .4rem;
        margin-inline:.06667rem}

.treasure__container__treasure-item__footer-ending>div[data-v-f66d0b52] {
        font-size: .37333rem;
        font-weight: 500
}

.treasure__container__treasure-item__footer-ending>div>span[data-v-f66d0b52] {
        font-weight: 600
}

.treasure__container__treasure-item__footer-button[data-v-f66d0b52] {
        margin-top: .6rem;
        width: 100%;
        height: .8rem;
        color: #8f5206;
        text-align: center;
        border-radius: 9rem;
        background: #D9AC4F;
        font-weight: 700;
        font-size: .37333rem;
        line-height: .8rem
}

.van-toast[data-v-769cfa78] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-769cfa78] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-769cfa78] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-769cfa78] {
        height: 80%
}

.pointMall__container-products[data-v-769cfa78] {
        position: -webkit-sticky;
        position: sticky;
        top: 5.33333rem;
        margin-block:.64rem 1.49333rem;padding-inline:.32rem}

.pointMall__container-products__tabs[data-v-769cfa78] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        margin-bottom: .26667rem
}

.pointMall__container-products__tabs>div[data-v-769cfa78] {
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        flex: 1;
        color: #a6a9ae;
        font-size: .4rem;
        white-space: nowrap
}

.pointMall__container-products__tabs>div[data-v-769cfa78]:first-of-type {
        position: relative;
        padding-left: 1rem
}

.pointMall__container-products__tabs>div[data-v-769cfa78]:first-of-type:before {
        content: "";
        position: absolute;
        top: 50%;
        left: 0;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        display: block;
        width: .08rem;
        height: .4rem;
        background: #D9AC4F
}

.pointMall__container-products__tabs>div.active[data-v-769cfa78] {
        color: #fff;
        font-weight: 700
}

.pointMall__container-products__content[data-v-769cfa78] {
        display: grid;
        grid-template-columns: repeat(2,1fr);
        row-gap: .4rem;
        -webkit-column-gap: .56rem;
        column-gap: .56rem;
        justify-items: center;
        width: 100%;
        margin-bottom: .66667rem
}

.pointMall__container-products-point[data-v-769cfa78] {
        display: grid;
        grid-template-columns: repeat(1,1fr);
        row-gap: .4rem;
        -webkit-column-gap: .56rem;
        column-gap: .56rem;
        justify-items: center;
        width: 100%;
        margin-bottom: .66667rem
}

.pointMall__container-products-all[data-v-769cfa78] {
        height: 1.06667rem;
        line-height: 1.06667rem;
        border: .01333rem solid #D9AC4F;
        border-radius: .66667rem;
        color: #d9ac4f;
        text-align: center;
        font-size: .37333rem
}

.pointMall__container-cart[data-v-769cfa78] {
        position: fixed;
        bottom: 0;
        width: 100%;
        height: 1.06667rem;
        max-width: 10rem;
        padding: .2rem 0;
        color: #8f5206;
        font-size: .45333rem;
        text-align: center;
        background: #D9AC4F;
        z-index: 1000
}

.pointMall__container-cart>div[data-v-769cfa78] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        gap: .34667rem;
        width: 100%
}

.pointMall__container-cart>div img[data-v-769cfa78],.pointMall__container-cart>div svg[data-v-769cfa78] {
        width: .66667rem;
        height: .66667rem
}

@media screen and (max-width: 500px) {
        .pointMall__container-cart[data-v-769cfa78] {
                max-width:none
        }
}

.van-toast[data-v-61c47e7a] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-61c47e7a] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-61c47e7a] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-61c47e7a] {
        height: 80%
}

.pointMall__container-info[data-v-61c47e7a] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        gap: .66667rem;
        padding-inline:.2rem;color: #fff
}

.pointMall__container-info__left[data-v-61c47e7a],.pointMall__container-info__right[data-v-61c47e7a] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        gap: .18667rem;
        width: 4.34667rem;
        height: 1.92rem;
        padding-block:.22667rem .14667rem;border-radius: .26667rem;
        background: #404040;
        color: #d9ac4f
}

.pointMall__container-info__left svg[data-v-61c47e7a],.pointMall__container-info__right svg[data-v-61c47e7a] {
        width: .96rem;
        height: .96rem
}

.pointMall__container-info__left span[data-v-61c47e7a],.pointMall__container-info__right span[data-v-61c47e7a] {
        font-size: .32rem
}

.van-toast[data-v-f7c56d10] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-f7c56d10] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-f7c56d10] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-f7c56d10] {
        height: 80%
}

.pointMall__container[data-v-f7c56d10] {
        position: relative;
        width: 100%;
        font-family: Inter,sans-serif;
        overflow: hidden
}

.pointMall__container[data-v-f7c56d10] .navbar .navbar__content .navbar__content-left .van-icon,.pointMall__container[data-v-f7c56d10] .navbar .navbar__content .navbar__content-center {
        color: #fff
}

.pointMall__container-header[data-v-f7c56d10] {
        position: relative;
        width: 100%;
        height: 7.30667rem;
        background-color: #3f3f3f;
        margin-bottom: 3.33333rem
}

.pointMall__container-header>img[data-v-f7c56d10] {
        width: 100%;
        height: 100%
}

.pointMall__container-header__content[data-v-f7c56d10] {
        position: absolute;
        left: 50%;
        bottom: 1.84rem;
        -webkit-transform: translate(-50%,30%);
        transform: translate(-50%,30%);
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        width: 100%
}

.pointMall__container-header__content img[data-v-f7c56d10] {
        width: .64rem;
        height: .64rem
}

.pointMall__container-header__content>span[data-v-f7c56d10] {
        margin-top: .10667rem;
        color: #fff;
        font-size: .32rem
}

.pointMall__container-header__content>h1[data-v-f7c56d10] {
        color: #fff;
        font-size: .56rem;
        font-weight: 600
}

.van-toast[data-v-e9fe3549] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-e9fe3549] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-e9fe3549] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-e9fe3549] {
        height: 80%
}

.turntable-page-header[data-v-e9fe3549] {
        height: 4.50667rem
}

.turntable-page-header img[data-v-e9fe3549] {
        display: block;
        width: 100%;
        object-fit: cover;
        height: 100%
}

.turntable-page .turntable-wrap[data-v-e9fe3549] {
        padding: .32rem
}

.turntable-page .turntable-rule[data-v-e9fe3549] {
        position: relative;
        height: .77333rem
}

.turntable-page .turntable-rule h3[data-v-e9fe3549] {
        position: absolute;
        width: 100%;
        text-align: center;
        color: #fff;
        font-size: .48rem;
        font-style: normal;
        font-weight: 700;
        line-height: .77333rem
}

.turntable-page .turntable-rule svg[data-v-e9fe3549] {
        width: 100%
}

.turntable-page .turntable-item[data-v-e9fe3549] {
        height: 1.06667rem;
        background: #3F3F3F;
        border-radius: .26667rem;
        margin-top: .4rem;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        padding: .13333rem .26667rem
}

.turntable-page .turntable-item .label[data-v-e9fe3549] {
        color: #a6a9ae;
        font-family: Inter;
        font-size: .37333rem;
        font-style: normal;
        font-weight: 400;
        line-height: .4rem
}

.turntable-page .turntable-item .wallet[data-v-e9fe3549] {
        min-width: 2.93333rem;
        height: .8rem;
        border-radius: .66667rem;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: end;
        -webkit-justify-content: end;
        justify-content: end;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        color: #d9ac4f;
        text-align: center;
        font-size: .32rem;
        font-style: normal;
        font-weight: 700;
        line-height: .4rem;
        padding: .24rem .26667rem
}

.turntable-page .turntable-item .wallet .re[data-v-e9fe3549] {
        width: .37333rem;
        height: .37333rem;
        margin-left: .13333rem
}

.turntable-page .turntable-item .wallet .re svg[data-v-e9fe3549] {
        width: 100%;
        height: 100%
}

.turntable-page .turntable-item .count[data-v-e9fe3549] {
        color: #a6a9ae;
        font-family: Inter;
        font-size: .4rem;
        font-style: normal;
        font-weight: 700;
        line-height: .4rem
}

.turntable-page .turntable-item .count-progress[data-v-e9fe3549] {
        color: #fae59f;
        font-family: Inter;
        font-size: .4rem;
        font-style: normal;
        font-weight: 700;
        line-height: .4rem
}

.turntable-page .turntable-main[data-v-e9fe3549] {
        height: 9.33333rem;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center
}

.turntable-page .turntable-entry[data-v-e9fe3549] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-justify-content: space-around;
        justify-content: space-around;
        margin: .26667rem 0
}

.turntable-page .turntable-entry-item[data-v-e9fe3549] {
        text-align: center;
        color: #fff
}

.turntable-page .turntable-entry-item svg[data-v-e9fe3549] {
        display: block;
        width: 1.28rem;
        height: 1.28rem;
        margin: 0 auto
}

.turntable-page .turntable-entry-item p[data-v-e9fe3549] {
        font-family: PingFang SC;
        font-size: .32rem;
        font-style: normal;
        font-weight: 400;
        line-height: normal
}

.turntable-page .turntable-title[data-v-e9fe3549] {
        margin-bottom: .2rem;
        color: #fff;
        font-family: Inter;
        font-size: .42667rem;
        font-style: normal;
        font-weight: 600;
        line-height: normal
}

.turntable-page .turntable-title svg[data-v-e9fe3549],.turntable-page .turntable-title span[data-v-e9fe3549] {
        vertical-align: middle
}

.turntable-page .turntable-title svg[data-v-e9fe3549] {
        width: .64rem;
        height: .64rem
}

.turntable-page .turntable-table[data-v-e9fe3549] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-pack: start;
        -webkit-justify-content: flex-start;
        justify-content: flex-start;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        color: #fff
}

.turntable-page .turntable-table-titlebox[data-v-e9fe3549] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        flex: 1;
        width: 100%;
        border-radius: .13333rem .13333rem 0 0;
        background: -webkit-linear-gradient(bottom,#C4933F 0%,#FAE59F 100%);
        background: linear-gradient(0deg,#C4933F 0%,#FAE59F 100%)
}

.turntable-page .turntable-table ul[data-v-e9fe3549] {
        width: 100%
}

.turntable-page .turntable-table ul li[data-v-e9fe3549] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        width: 100%
}

.turntable-page .turntable-table ul li[data-v-e9fe3549]:nth-child(odd) {
        background: #3F3F3F
}

.turntable-page .turntable-table ul li div[data-v-e9fe3549] {
        height: 100%;
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        flex: 1;
        font-size: .37333rem;
        padding: .34667rem 0;
        text-align: center
}

.turntable-page .turntable-table ul .targetAmount[data-v-e9fe3549] {
        color: #d9ac4f;
        font-family: Inter;
        font-size: .34667rem;
        font-style: normal;
        font-weight: 500;
        line-height: .34667rem;
        margin-bottom: .08rem
}

.turntable-page .turntable-table ul p[data-v-e9fe3549] {
        color: #888;
        font-size: .29333rem;
        font-style: normal;
        font-weight: 400;
        line-height: .29333rem
}

.turntable-page .turntable-table ul .rotateNum[data-v-e9fe3549] {
        color: #d9ac4f;
        font-size: .34667rem;
        font-style: normal;
        font-weight: 500;
        line-height: .34667rem
}

.turntable-page .turntable-table-title[data-v-e9fe3549] {
        width: 100%;
        height: 1.06667rem;
        padding-block:.34667rem;font-size: .37333rem;
        line-height: .32rem;
        text-align: center;
        color: #fff
}

.turntable-page .turntable-table-content[data-v-e9fe3549] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        width: 100%;
        height: 100%
}

.turntable-page .turntable-table-content>div[data-v-e9fe3549] {
        width: 100%;
        padding: .34667rem 0;
        font-size: .37333rem;
        line-height: .37333rem;
        text-align: center;
        border-bottom: .01333rem solid #ff8180
}

.turntable-page .turntable-table-content>div[data-v-e9fe3549]:last-of-type {
        border-bottom: none
}

.turntable-page[data-v-e9fe3549] .infiniteScroll {
        width: 100%
}

.turntable-page[data-v-e9fe3549] .dialog__container {
        position: relative;
        padding-top: 1.93333rem
}

.turntable-page[data-v-e9fe3549] .dialog__container-img {
        position: absolute;
        width: 3.73333rem;
        height: 2.46667rem;
        top: -.4rem
}

.van-toast[data-v-2157bbf7] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-2157bbf7] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-2157bbf7] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-2157bbf7] {
        height: 80%
}

.rule[data-v-2157bbf7] {
        margin-top: .53333rem
}

.rule .title[data-v-2157bbf7] {
        color: #d9ac4f;
        font-size: .4rem;
        font-weight: 700;
        letter-spacing: .016rem;
        text-align: center
}

.rule[data-v-2157bbf7] .van-collapse-item__wrapper {
        margin-top: .2rem
}

.rule .con[data-v-2157bbf7] {
        color: #fff;
        font-size: .32rem
}

.rule .con h1[data-v-2157bbf7] {
        position: relative;
        margin-bottom: .13333rem
}

.rule .con h1>span[data-v-2157bbf7] {
        width: 93%;
        margin: 0 auto;
        padding: .06667rem 0;
        color: #fff;
        font-size: .32rem;
        text-align: center;
        background-color: #6f6f6f;
        -webkit-clip-path: polygon(2% 0%,98% 0%,100% 50%,98% 100%,2% 100%,0% 50%);
        clip-path: polygon(2% 0%,98% 0%,100% 50%,98% 100%,2% 100%,0% 50%);
        display: block
}

.rule .con h1[data-v-2157bbf7]:before {
        content: "";
        display: block;
        position: absolute;
        top: 0;
        width: .26667rem;
        height: .61333rem;
        background-color: #6f6f6f;
        -webkit-clip-path: polygon(50% 0%,90% 0%,40% 50%,90% 100%,50% 100%,0% 50%);
        clip-path: polygon(50% 0%,90% 0%,40% 50%,90% 100%,50% 100%,0% 50%);
        z-index: 5;
        left: calc(7% - .4rem);
        -webkit-transform: translateX(-50%);
        transform: translate(-50%)
}

.rule .con h1[data-v-2157bbf7]:after {
        content: "";
        display: block;
        position: absolute;
        top: 0;
        width: .26667rem;
        height: .61333rem;
        background-color: #6f6f6f;
        -webkit-clip-path: polygon(50% 0%,90% 0%,40% 50%,90% 100%,50% 100%,0% 50%);
        clip-path: polygon(50% 0%,90% 0%,40% 50%,90% 100%,50% 100%,0% 50%);
        z-index: 5;
        left: calc(93% + .4rem);
        -webkit-transform: translateX(-50%) rotate(180deg);
        transform: translate(-50%) rotate(180deg)
}

.rule .con>div[data-v-2157bbf7],.rule .con>p[data-v-2157bbf7] {
        margin-bottom: .4rem
}

.rule .con>div span[data-v-2157bbf7],.rule .con>div[data-v-2157bbf7] span,.rule .con>p span[data-v-2157bbf7],.rule .con>p[data-v-2157bbf7] span {
        color: #d9ac4f
}

.rule .con p[data-v-2157bbf7] {
        position: relative;
        padding-left: .26667rem;
        margin-bottom: .13333rem;
        color: #a6a9ae
}

.rule .con p[data-v-2157bbf7]:after {
        position: absolute;
        content: "";
        width: .13333rem;
        height: .13333rem;
        -webkit-transform: rotate(45deg);
        transform: rotate(45deg);
        background: #D9AC4F;
        left: 0;
        top: .18667rem
}

.rule .con .condition[data-v-2157bbf7] {
        border: .01333rem solid #6F6F6F;
        border-radius: .13333rem;
        padding: .26667rem .4rem
}

.rule .con .ranking[data-v-2157bbf7] {
        border-radius: .13333rem;
        overflow: hidden
}

.rule .con .ranking>div[data-v-2157bbf7] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        padding: .2rem 0
}

.rule .con .ranking>div>span[data-v-2157bbf7] {
        width: 50%;
        text-align: center;
        word-break: break-all
}

.rule .con .ranking>div.top[data-v-2157bbf7] {
        background: #D9AC4F
}

.rule .con .ranking>div.top>span[data-v-2157bbf7] {
        color: #8f5206
}

.rule .con .ranking>div.info[data-v-2157bbf7] {
        border-bottom: .01333rem solid #3F3F3F;
        border-left: .01333rem solid #3F3F3F;
        border-right: .01333rem solid #3F3F3F;
        font-size: .37333rem
}

.rule .con .ranking>div.info>span[data-v-2157bbf7]:first-of-type {
        color: #fff
}

.rule .con .ranking>div.info>span[data-v-2157bbf7]:last-of-type {
        font-weight: 500
}

.van-toast[data-v-981ca994] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-981ca994] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-981ca994] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-981ca994] {
        height: 80%
}

.game[data-v-981ca994] {
        margin-top: .4rem;
        background: none!important;
        box-shadow: none!important
}

.game h1[data-v-981ca994] {
        color: #fff;
        font-size: .48rem;
        font-weight: 700;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        gap: .26667rem;
        margin-bottom: .13333rem
}

.game h1[data-v-981ca994]:before {
        content: "";
        display: block;
        height: .4rem;
        width: .08rem;
        background: #D9AC4F
}

.game .items[data-v-981ca994] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-flex-wrap: wrap;
        flex-wrap: wrap;
        gap: .2rem
}

.game .items .item[data-v-981ca994] {
        width: calc(33.3333333333% - .2rem);
        height: 2.93333rem;
        border-radius: .26667rem;
        overflow: hidden
}

.game .items .item img[data-v-981ca994] {
        width: 2.93333rem;
        height: 2.93333rem
}

.van-toast[data-v-c77c28e6] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-c77c28e6] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-c77c28e6] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-c77c28e6] {
        height: 80%
}

.ChampionshipDetail[data-v-c77c28e6] {
        padding: 0 .26667rem .53333rem
}

.ChampionshipDetail>div[data-v-c77c28e6] {
        border-radius: .13333rem;
        overflow: hidden
}

.ChampionshipDetail .ranking[data-v-c77c28e6] {
        margin-top: .2rem
}

.ChampionshipDetail .ranking .title[data-v-c77c28e6],.ChampionshipDetail .ranking .amount[data-v-c77c28e6] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        gap: .13333rem
}

.ChampionshipDetail .ranking .title>span[data-v-c77c28e6],.ChampionshipDetail .ranking .amount>span[data-v-c77c28e6] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        text-align: center;
        min-height: 1.06667rem;
        word-break: break-all
}

.ChampionshipDetail .ranking .title>span[data-v-c77c28e6],.ChampionshipDetail .ranking .amount>span[data-v-c77c28e6] {
        width: 33.3333333333%
}

.ChampionshipDetail .ranking .title[data-v-c77c28e6] {
        background: #D9AC4F;
        color: #8f5206;
        font-weight: 700;
        font-size: .37333rem
}

.ChampionshipDetail .ranking .amount[data-v-c77c28e6] {
        font-weight: 500;
        background-color: #3f3f3f;
        color: #fff
}

.ChampionshipDetail .ranking .amount>span[data-v-c77c28e6]:last-of-type {
        color: #d9ac4f
}

.ChampionshipDetail .rankingList[data-v-c77c28e6] {
        margin-top: .2rem
}

.ChampionshipDetail .rankingList .title[data-v-c77c28e6],.ChampionshipDetail .rankingList .info[data-v-c77c28e6] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        gap: .13333rem
}

.ChampionshipDetail .rankingList .title>span[data-v-c77c28e6],.ChampionshipDetail .rankingList .info>span[data-v-c77c28e6] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        text-align: center;
        min-height: 1.06667rem;
        word-break: break-all
}

.ChampionshipDetail .rankingList .title>span[data-v-c77c28e6]:first-of-type,.ChampionshipDetail .rankingList .info>span[data-v-c77c28e6]:first-of-type {
        width: 1.33333rem
}

.ChampionshipDetail .rankingList .title>span[data-v-c77c28e6]:not(:first-of-type),.ChampionshipDetail .rankingList .info>span[data-v-c77c28e6]:not(:first-of-type) {
        width: calc((100% - 1.33333rem) / 3)
}

.ChampionshipDetail .rankingList .title[data-v-c77c28e6] {
        background: -webkit-linear-gradient(#C4933F,#FAE59F);
        background: linear-gradient(#C4933F,#FAE59F);
        color: #8f5206;
        font-size: .34667rem;
        font-weight: 700
}

.ChampionshipDetail .rankingList .info[data-v-c77c28e6] {
        color: #666
}

.ChampionshipDetail .rankingList .info .top1[data-v-c77c28e6] {
        background: url(/assets/png/1-dcdd0031.png) no-repeat center;
        background-size: .93333rem .93333rem
}

.ChampionshipDetail .rankingList .info .top2[data-v-c77c28e6] {
        background: url(/assets/png/2-0c408958.png) no-repeat center;
        background-size: .93333rem .93333rem
}

.ChampionshipDetail .rankingList .info .top3[data-v-c77c28e6] {
        background: url(/assets/png/3-f5a58c12.png) no-repeat center;
        background-size: .93333rem .93333rem
}

.ChampionshipDetail .rankingList .info .txt[data-v-c77c28e6] {
        font-family: Roboto;
        font-size: .48rem;
        font-weight: 900;
        background: -webkit-linear-gradient(top,#C4CFDF 0%,#778DAE 100%);
        background: linear-gradient(180deg,#C4CFDF 0%,#778DAE 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent
}

.ChampionshipDetail .rankingList .info>span[data-v-c77c28e6]:nth-of-type(2) {
        color: #fff
}

.ChampionshipDetail .rankingList .info>span[data-v-c77c28e6]:nth-of-type(3) {
        color: #d9ac4f
}

.ChampionshipDetail .rankingList .info>span[data-v-c77c28e6]:last-of-type {
        color: #fae59f;
        font-weight: 500
}

.ChampionshipDetail .rankingList .info[data-v-c77c28e6]:nth-child(odd) {
        background: #303030
}

.ChampionshipDetail .rankingList .info[data-v-c77c28e6]:nth-child(2n) {
        background: #3F3F3F
}

.ChampionshipDetail .rankingList .refresh[data-v-c77c28e6] {
        padding: .33333rem;
        text-align: center;
        background: -webkit-linear-gradient(#C4933F,#FAE59F);
        background: linear-gradient(#C4933F,#FAE59F);
        color: #8f5206
}

.van-toast[data-v-19466c90] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-19466c90] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-19466c90] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-19466c90] {
        height: 80%
}

.dailySignInRecord__container[data-v-19466c90] {
        padding-inline:.32rem}

.dailySignInRecord__container-wrapper[data-v-19466c90] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        gap: .18667rem
}

.dailySignInRecord__container-wrapper__content[data-v-19466c90] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        height: 1.36rem;
        padding: .25333rem .26667rem;
        border-radius: .13333rem;
        background: #3F3F3F
}

.dailySignInRecord__container-wrapper__content-left[data-v-19466c90] {
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        flex: 1;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        gap: .10667rem
}

.dailySignInRecord__container-wrapper__content-left h1[data-v-19466c90] {
        font-size: .4rem;
        font-weight: 500;
        color: #fff
}

.dailySignInRecord__container-wrapper__content-left span[data-v-19466c90] {
        font-size: .32rem;
        color: #a6a9ae
}

.dailySignInRecord__container-wrapper__content-right[data-v-19466c90] {
        position: relative;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        width: 3.73333rem;
        height: .8rem;
        padding: .09333rem .18667rem;
        text-align: center;
        border-radius: 9rem;
        background: -webkit-linear-gradient(left,#C4933F 0%,#FAE59F 100%);
        background: linear-gradient(90deg,#C4933F 0%,#FAE59F 100%)
}

.dailySignInRecord__container-wrapper__content-right img[data-v-19466c90] {
        position: absolute;
        left: .09333rem;
        top: 50%;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        width: .56rem;
        height: .56rem
}

.dailySignInRecord__container-wrapper__content-right span[data-v-19466c90] {
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        flex: 1;
        font-size: .48rem;
        color: #8f5206
}

.van-toast[data-v-7565a95f] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-7565a95f] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-7565a95f] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-7565a95f] {
        height: 80%
}

.dailySignInRules__container[data-v-7565a95f] {
        padding-inline:.32rem;padding-bottom: 1.22667rem
}

[data-v-7565a95f] .rule {
        width: 100%
}

.van-toast[data-v-4c24f097] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-4c24f097] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-4c24f097] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-4c24f097] {
        height: 80%
}

.task-record-container[data-v-4c24f097] {
        font-family: Inter,sans-serif
}

.task-record-container .head[data-v-4c24f097] {
        height: 1.17333rem;
        color: #a6a9ae;
        font-size: .42667rem;
        line-height: 1.17333rem;
        padding: 0 .32rem;
        border-radius: .13333rem
}

.task-record-container .head>button[data-v-4c24f097] {
        border: none;
        width: 50%;
        background: #303030
}

.task-record-container .head>button.active[data-v-4c24f097] {
        background: -webkit-linear-gradient(165deg,#C4933F 10.52%,#FAE59F 89.48%);
        background: linear-gradient(285deg,#C4933F 10.52%,#FAE59F 89.48%);
        color: #8f5206;
        border-radius: .16rem
}

.task-record-container .record-panel[data-v-4c24f097] {
        padding: .26667rem .32rem
}

.task-record-container .record-panel img[data-v-4c24f097] {
        width: .53333rem;
        height: .53333rem;
        margin-right: .13333rem
}

.task-record-container .record-panel-item[data-v-4c24f097] {
        padding: .4rem .26667rem;
        background: #3F3F3F;
        border-radius: .13333rem;
        margin-bottom: .26667rem
}

.task-record-container .record-panel-item h1[data-v-4c24f097] {
        color: #fff;
        font-size: .4rem;
        margin-bottom: .13333rem
}

.task-record-container .record-panel-item h2[data-v-4c24f097] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        margin-bottom: .13333rem
}

.task-record-container .record-panel-item h2 span[data-v-4c24f097] {
        color: #a6a9ae
}

.task-record-container .record-panel-item h2 .markRed[data-v-4c24f097] {
        color: #d9ac4f;
        margin-left: .26667rem
}

.task-record-container .record-panel-item div[data-v-4c24f097] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        color: #a6a9ae
}

.task-record-container .record-panel-item div h3[data-v-4c24f097] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        color: #d9ac4f
}

.van-toast[data-v-4c952586] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-4c952586] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-4c952586] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-4c952586] {
        height: 80%
}

.member-package-rule[data-v-4c952586] .number {
        color: #d9ac4f
}

.member-package-rule .first-list-item[data-v-4c952586] {
        padding: .26667rem;
        border-radius: .26667rem;
        background: #3F3F3F
}

.member-package-rule .first-list-item.space[data-v-4c952586] {
        margin-top: .3rem
}

.member-package-rule .first-list-item.space .head[data-v-4c952586] {
        -webkit-box-pack: start!important;
        -webkit-justify-content: start!important;
        justify-content: start!important
}

.member-package-rule .first-list-item.space .head .title[data-v-4c952586] {
        color: #888
}

.member-package-rule .first-list-item .head[data-v-4c952586] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        margin-bottom: .18667rem
}

.member-package-rule .first-list-item .head .title[data-v-4c952586] {
        color: #fb5b5b;
        font-size: .4rem;
        line-height: .48rem
}

.member-package-rule .first-list-item .description[data-v-4c952586] {
        font-size: .29333rem;
        color: #a6a9ae;
        margin-bottom: .26667rem
}

.member-package-rule-bonus .table-head[data-v-4c952586] {
        padding-top: .5rem;
        color: #fff
}

.member-package-rule-bonus-title[data-v-4c952586] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        color: #fb5b5b;
        font-size: .4rem;
        line-height: .48rem;
        margin-top: .5rem
}

.member-package-rule .table-container[data-v-4c952586] {
        width: 100%;
        background: #3F3F3F;
        border-radius: .21333rem;
        overflow: hidden;
        margin-top: .2rem
}

.member-package-rule .table-container .table-title[data-v-4c952586] {
        height: 1.06667rem;
        line-height: 1.06667rem;
        background: -webkit-linear-gradient(left,#C59440 0%,#FAE59F 97.86%);
        background: linear-gradient(90deg,#C59440 0%,#FAE59F 97.86%);
        color: #8f5206
}

.member-package-rule .table-container .table-title>th[data-v-4c952586] {
        border: .01333rem solid #ffb79e;
        font-size: .32rem;
        text-align: center;
        width: 33.3%
}

.member-package-rule .table-container tbody tr[data-v-4c952586] {
        height: .93333rem;
        line-height: .46667rem;
        font-size: .32rem;
        text-align: center;
        color: #a6a9ae
}

.member-package-rule .table-container tbody tr[data-v-4c952586]:nth-child(2n) {
        background: #363636
}

.member-package-rule .table-container tbody tr td[data-v-4c952586] {
        border-right: .01333rem solid #292929;
        text-align: center;
        vertical-align: middle;
        color: #fff
}

.member-package-rule .table-container tbody tr td[data-v-4c952586]:first-child {
        color: #a6a9ae
}

.member-package-rule .table-container tbody tr td[data-v-4c952586]:last-child {
        color: #d9ac4f
}

.van-toast[data-v-a70c6696] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-a70c6696] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-a70c6696] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-a70c6696] {
        height: 80%
}

.first_list[data-v-a70c6696],.member-package-rule[data-v-a70c6696] {
        padding: .32rem
}

.member-package-rule[data-v-a70c6696] .number {
        color: #d9ac4f
}

.member-package-rule .first-list-item[data-v-a70c6696] {
        padding: .26667rem;
        border-radius: .26667rem;
        background: #3F3F3F;
        color: #a6a9ae
}

.member-package-rule .first-list-item.space[data-v-a70c6696] {
        margin-top: .3rem
}

.member-package-rule .first-list-item.space .head[data-v-a70c6696] {
        -webkit-box-pack: start!important;
        -webkit-justify-content: start!important;
        justify-content: start!important
}

.member-package-rule .first-list-item.space .head .title[data-v-a70c6696] {
        color: #888
}

.member-package-rule .first-list-item .head[data-v-a70c6696] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        margin-bottom: .18667rem
}

.member-package-rule .first-list-item .head .title[data-v-a70c6696] {
        color: #d9ac4f;
        font-size: .4rem;
        line-height: .48rem
}

.member-package-rule .first-list-item .description[data-v-a70c6696] {
        font-size: .29333rem;
        color: #a6a9ae;
        margin-bottom: .26667rem
}

.member-package-rule .first-list-item .description .number[data-v-a70c6696] {
        color: #d9ac4f
}

.member-package-rule-bonus .table-head[data-v-a70c6696] {
        padding-top: .5rem
}

.member-package-rule-bonus-title[data-v-a70c6696] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        color: #d9ac4f;
        font-size: .4rem;
        line-height: .48rem;
        margin-top: .5rem
}

.van-toast[data-v-5b99033c] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-5b99033c] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-5b99033c] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-5b99033c] {
        height: 80%
}

.addAddress__container[data-v-5b99033c] {
        padding-inline:.32rem;padding-bottom: .66667rem;
        font-family: bahnschrift
}

.addAddress__container-warning[data-v-5b99033c] {
        margin-top: .2rem;
        width: 9.36rem;
        height: -webkit-max-content;
        height: max-content;
        margin-bottom: .73333rem;
        padding: .26667rem .30667rem;
        border: .01333rem solid #757575;
        border-radius: .13333rem
}

.addAddress__container-warning h1[data-v-5b99033c] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        gap: .06667rem;
        margin-bottom: .26667rem;
        color: #d9ac4f;
        font-size: .32rem;
        line-height: .38667rem
}

.addAddress__container-warning h1 i[data-v-5b99033c] {
        color: #d9ac4f;
        font-size: .48rem
}

.addAddress__container-warning p[data-v-5b99033c] {
        padding-left: .82667rem;
        color: #a6a9ae;
        font-size: .29333rem;
        line-height: .37333rem
}

.addAddress__container-forum[data-v-5b99033c] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        gap: 1.26667rem
}

.addAddress__container-forum__item h1[data-v-5b99033c] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        gap: .2rem;
        color: #fff;
        font-size: .42667rem;
        margin-bottom: .26667rem
}

.addAddress__container-forum__item h1 i[data-v-5b99033c] {
        color: #d9ac4f;
        font-size: .64rem
}

.addAddress__container-forum__item .input_model[data-v-5b99033c] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex
}

.addAddress__container-forum__item .input_model[data-v-5b99033c] .dropdown {
        line-height: 1.06667rem;
        height: 1.06667rem;
        color: #fff
}

.addAddress__container-forum__item .input_model .dropCon[data-v-5b99033c] {
        width: 2.48rem;
        border-radius: .13333rem;
        height: 1.06667rem;
        margin-right: .21333rem;
        overflow: hidden
}

.addAddress__container-forum__item .input_model input[data-v-5b99033c] {
        -webkit-box-flex: 1;
        -webkit-flex: auto;
        flex: auto;
        height: 1.06667rem;
        padding-inline:.26667rem;font-size: .37333rem;
        border: none;
        border-radius: .13333rem;
        background: #484848;
        color: #fff
}

.addAddress__container-forum__item .input_model input[data-v-5b99033c]::-webkit-input-placeholder {
        color: #757575
}

.addAddress__container-forum__item .input_model input[data-v-5b99033c]::placeholder {
        color: #757575
}

.addAddress__container-forum__item textarea[data-v-5b99033c] {
        width: 100%;
        height: 3.73333rem;
        margin-top: .37333rem;
        padding: .30667rem .26667rem;
        font-size: .37333rem;
        outline: none;
        border: none;
        border-radius: .13333rem;
        background: #292929;
        resize: none;
        color: #a6a9ae;
        outline: .01333rem solid #757575
}

.addAddress__container-forum__item textarea[data-v-5b99033c]::-webkit-input-placeholder {
        color: #757575
}

.addAddress__container-forum__item textarea[data-v-5b99033c]::placeholder {
        color: #757575
}

.addAddress__container-saveBtn[data-v-5b99033c] {
        height: .93333rem;
        margin-top: 1.26667rem;
        margin-inline:.18667rem;color: #8f5206;
        text-align: center;
        line-height: .93333rem;
        letter-spacing: .13333rem;
        border-radius: 9rem;
        font-size: .4rem;
        background: -webkit-linear-gradient(#FAE59F,#C4933F);
        background: linear-gradient(#FAE59F,#C4933F)
}

.addAddress__container-saveBtn.disabled[data-v-5b99033c] {
        background: -webkit-linear-gradient(top,#A9AAB5 0%,#6F7381 100%);
        background: linear-gradient(180deg,#A9AAB5 0%,#6F7381 100%);
        color: #fff
}

.addAddress__container-saveBtn[data-v-5b99033c]:active {
        outline: .05333rem solid #ccc
}

.van-toast[data-v-fdbd8659] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-fdbd8659] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-fdbd8659] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-fdbd8659] {
        height: 80%
}

.lotteryActivity__container[data-v-fdbd8659] {
        padding-top: .13333rem
}

.lotteryActivity__container[data-v-fdbd8659] .van-nav-bar {
        background-color: #292929
}

.lotteryActivity__container[data-v-fdbd8659] .van-nav-bar .van-nav-bar__content .van-nav-bar__left .van-icon,.lotteryActivity__container[data-v-fdbd8659] .van-nav-bar .van-nav-bar__content .van-nav-bar__title {
        color: #151515
}

.lotteryActivity__container[data-v-fdbd8659] .van-tabs__line {
        width: 0;
        height: 0;
        border-left: .10667rem solid transparent;
        border-right: .10667rem solid transparent;
        border-bottom: .10667rem solid #292929;
        background-color: unset!important
}

.lotteryActivity__container-list[data-v-fdbd8659] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        gap: .4rem;
        padding: .4rem .32rem
}

.lotteryActivity__container-button[data-v-fdbd8659] {
        width: 100%;
        height: 1.06667rem;
        color: #f95959;
        text-align: center;
        font-size: .45333rem;
        line-height: 1.06667rem;
        border: .01333rem solid #f95959;
        border-radius: 9rem
}

.lotteryActivity__container-products__treasure[data-v-fdbd8659] {
        position: relative;
        left: 50%;
        -webkit-transform: translateX(-50%);
        transform: translate(-50%);
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-flex-wrap: wrap;
        flex-wrap: wrap;
        gap: 1rem;
        width: calc(100% - .4rem);
        margin-top: 1rem
}

.lotteryActivity__container-products__treasure-item[data-v-fdbd8659] {
        width: 100%
}

.lotteryActivity__container-products__treasure-item__header[data-v-fdbd8659],.lotteryActivity__container-products__treasure-item__footer[data-v-fdbd8659] {
        position: relative;
        width: 100%
}

.lotteryActivity__container-products__treasure-item__header[data-v-fdbd8659] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        padding-block:.26667rem;color: #fff;
        border-top-left-radius: .13333rem;
        border-top-right-radius: .13333rem;
        background: -webkit-linear-gradient(top,#ff7172 0%,#ffb19b 98.95%);
        background: linear-gradient(180deg,#ff7172 0%,#ffb19b 98.95%)
}

.lotteryActivity__container-products__treasure-item__header>span[data-v-fdbd8659] {
        position: absolute;
        top: .26667rem;
        left: 0;
        padding: .04rem .26667rem .04rem .4rem;
        font-size: .875rem;
        border-top-right-radius: 9rem;
        border-bottom-right-radius: 9rem;
        z-index: 2
}

.lotteryActivity__container-products__treasure-item__header>span[data-v-fdbd8659]:last-of-type {
        top: .22667rem;
        padding: .08rem .26667rem .08rem .44rem;
        color: transparent;
        white-space: nowrap;
        z-index: 1
}

.lotteryActivity__container-products__treasure-item__footer[data-v-fdbd8659] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: start;
        -webkit-align-items: flex-start;
        align-items: flex-start;
        padding: .13333rem .2rem;
        border-bottom-left-radius: .13333rem;
        border-bottom-right-radius: .13333rem;
        box-shadow: 0 .05333rem .21333rem #d0d0ed5c
}

.lotteryActivity__container-products__treasure-item__footer-title[data-v-fdbd8659] {
        margin-bottom: .5rem;
        color: #333;
        font-size: 1.125rem
}

.lotteryActivity__container-products__treasure-item__footer-title>span[data-v-fdbd8659] {
        color: #f95959;
        font-weight: 700
}

.lotteryActivity__container-products__treasure-item__footer-progressTitle[data-v-fdbd8659] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        width: 100%;
        color: #aeb0c6;
        font-size: .875rem
}

.lotteryActivity__container-products__treasure-item__footer .van-progress[data-v-fdbd8659] {
        width: 100%;
        margin-block:.5rem}

.lotteryActivity__container-products__treasure-item__footer-progressDetails[data-v-fdbd8659] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        width: 100%;
        color: #333
}

.lotteryActivity__container-products__treasure-item__footer-progressDetails>span[data-v-fdbd8659] {
        color: #848592;
        font-size: .875rem
}

.lotteryActivity__container-products__treasure-item__footer-progressDetails>div span[data-v-fdbd8659] {
        font-size: 1.125rem;
        font-weight: 600
}

.lotteryActivity__container-products__treasure-item__footer-ending[data-v-fdbd8659],.lotteryActivity__container-products__treasure-item__footer-cost[data-v-fdbd8659] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        width: 100%;
        height: 2rem;
        margin-block:.06667rem;border: .01333rem solid #f1f3ff;
        background: #f7f8ff
}

.lotteryActivity__container-products__treasure-item__footer-ending>span[data-v-fdbd8659],.lotteryActivity__container-products__treasure-item__footer-ending>div[data-v-fdbd8659],.lotteryActivity__container-products__treasure-item__footer-cost>span[data-v-fdbd8659],.lotteryActivity__container-products__treasure-item__footer-cost>div[data-v-fdbd8659] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        height: 100%;
        padding: .06667rem .13333rem
}

.lotteryActivity__container-products__treasure-item__footer-ending>span[data-v-fdbd8659],.lotteryActivity__container-products__treasure-item__footer-cost>span[data-v-fdbd8659] {
        -webkit-box-flex: .6;
        -webkit-flex: .6;
        flex: .6;
        background: #f1f3ff;
        -webkit-clip-path: polygon(0 0,100% 0%,90% 100%,0% 100%);
        clip-path: polygon(0 0,100% 0%,90% 100%,0% 100%)
}

.lotteryActivity__container-products__treasure-item__footer-ending>div[data-v-fdbd8659],.lotteryActivity__container-products__treasure-item__footer-cost>div[data-v-fdbd8659] {
        -webkit-box-flex: .4;
        -webkit-flex: .4;
        flex: .4;
        -webkit-box-pack: end;
        -webkit-justify-content: flex-end;
        justify-content: flex-end;
        color: #f95959;
        font-size: 1.125rem;
        font-weight: 600
}

.lotteryActivity__container-products__treasure-item__footer-ending>div img[data-v-fdbd8659],.lotteryActivity__container-products__treasure-item__footer-cost>div img[data-v-fdbd8659] {
        width: 1.125rem;
        height: 1.125rem;
        margin-inline:.06667rem}

.lotteryActivity__container-products__treasure-item__footer-ending>div[data-v-fdbd8659] {
        color: #333;
        font-size: 1rem;
        font-weight: 500
}

.lotteryActivity__container-products__treasure-item__footer-ending>div>span[data-v-fdbd8659] {
        font-size: 1.125rem;
        font-weight: 600
}

.lotteryActivity__container-products__treasure-item__footer-button[data-v-fdbd8659] {
        width: 100%;
        margin-block:1.5rem 1rem;padding-block:.06667rem;color: #fff;
        text-align: center;
        border-radius: 9rem;
        background: #ff7374;
        box-shadow: 0 .05333rem .10667rem #d0d0ed80
}

.lotteryActivity__container-products__treasure-button[data-v-fdbd8659] {
        width: 100%;
        padding: .10667rem;
        color: #f95959;
        text-align: center;
        font-size: 1.25rem;
        border: .01333rem solid #f95959;
        border-radius: 9rem
}

.van-toast[data-v-dcab328a] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-dcab328a] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-dcab328a] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-dcab328a] {
        height: 80%
}

.lotteryDetail__container[data-v-dcab328a] {
        padding-bottom: 2.4rem;
        font-family: bahnschrift
}

.lotteryDetail__container[data-v-dcab328a] .van-nav-bar .van-nav-bar__content .van-nav-bar__left .van-icon,.lotteryDetail__container[data-v-dcab328a] .van-nav-bar .van-nav-bar__content .van-nav-bar__title {
        color: #fff
}

.lotteryDetail__container[data-v-dcab328a] .van-action-sheet__header {
        line-height: 1.06667rem;
        padding-top: .26667rem;
        color: #fff
}

.lotteryDetail__container-item[data-v-dcab328a] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column
}

.lotteryDetail__container-item__hero[data-v-dcab328a] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: stretch;
        -webkit-align-items: stretch;
        align-items: stretch;
        overflow: hidden
}

.lotteryDetail__container-item__hero-img[data-v-dcab328a] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        -webkit-box-align: end;
        -webkit-align-items: flex-end;
        align-items: flex-end;
        background: -webkit-linear-gradient(top,#ff7273 0%,#ffad99 99.91%);
        background: linear-gradient(180deg,#ff7273 0%,#ffad99 99.91%);
        height: 7.02667rem;
        position: relative
}

.lotteryDetail__container-item__hero-img img[data-v-dcab328a] {
        height: 6.66667rem;
        width: 6.66667rem
}

.lotteryDetail__container-item__hero-img .van-swipe-item[data-v-dcab328a] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center
}

.lotteryDetail__container-item__hero-img .van-swipe[data-v-dcab328a] {
        width: 100%;
        height: 100%
}

.lotteryDetail__container-item__hero-title[data-v-dcab328a],.lotteryDetail__container-item__hero-detail[data-v-dcab328a],.lotteryDetail__container-item__hero-progress[data-v-dcab328a],.lotteryDetail__container-item__hero-footer[data-v-dcab328a] {
        padding-inline:.16rem}

.lotteryDetail__container-item__hero-title[data-v-dcab328a] {
        font-size: .4rem;
        color: #fff;
        margin: .32rem 0
}

.lotteryDetail__container-item__hero-title>span[data-v-dcab328a] {
        color: #d9ac4f
}

.lotteryDetail__container-item__hero-detail[data-v-dcab328a] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        gap: .26667rem;
        width: 100%;
        color: #a6a9ae;
        margin-bottom: .6rem
}

.lotteryDetail__container-item__hero-detail>div[data-v-dcab328a] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        gap: .13333rem;
        padding: .06667rem .13333rem;
        font-size: .32rem;
        background: #3F3F3F
}

.lotteryDetail__container-item__hero-detail>div>span[data-v-dcab328a]:first-child {
        -webkit-box-flex: 20%;
        -webkit-flex: 20%;
        flex: 20%;
        text-align: center
}

.lotteryDetail__container-item__hero-detail>div>span.text-spacing[data-v-dcab328a] {
        text-align: justify;
        text-align-last: justify;
        padding: 0 .26667rem
}

.lotteryDetail__container-item__hero-detail>div>span[data-v-dcab328a]:last-child {
        -webkit-box-flex: 80%;
        -webkit-flex: 80%;
        flex: 80%;
        font-size: .32rem;
        line-height: .32rem;
        color: #b2b2b2
}

.lotteryDetail__container-item__hero-detail>div>div[data-v-dcab328a] {
        height: .24rem;
        width: .01333rem;
        background: #b2b2b2
}

.lotteryDetail__container-item__hero-progress[data-v-dcab328a] {
        margin-bottom: .26667rem
}

.lotteryDetail__container-item__hero-progress h1[data-v-dcab328a] {
        font-size: .37333rem;
        font-weight: 700;
        color: #fff
}

.lotteryDetail__container-item__hero-progress>div[data-v-dcab328a]:first-of-type,.lotteryDetail__container-item__hero-progress>div[data-v-dcab328a]:last-of-type {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        font-size: .32rem
}

.lotteryDetail__container-item__hero-progress>div[data-v-dcab328a]:first-of-type {
        font-size: .32rem;
        margin-bottom: .13333rem;
        color: #a6a9ae
}

.lotteryDetail__container-item__hero-progress>div[data-v-dcab328a]:last-of-type {
        margin-top: .13333rem;
        color: #a6a9ae
}

.lotteryDetail__container-item__hero-footer__ending[data-v-dcab328a],.lotteryDetail__container-item__hero-footer__cost[data-v-dcab328a] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        width: 100%;
        height: .8rem;
        margin-block:.06667rem;background: #3F3F3F
}

.lotteryDetail__container-item__hero-footer__ending>span[data-v-dcab328a],.lotteryDetail__container-item__hero-footer__ending>div[data-v-dcab328a],.lotteryDetail__container-item__hero-footer__cost>span[data-v-dcab328a],.lotteryDetail__container-item__hero-footer__cost>div[data-v-dcab328a] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        height: 100%;
        padding: .06667rem .13333rem
}

.lotteryDetail__container-item__hero-footer__ending>span[data-v-dcab328a],.lotteryDetail__container-item__hero-footer__cost>span[data-v-dcab328a] {
        -webkit-box-flex: .6;
        -webkit-flex: .6;
        flex: .6
}

.lotteryDetail__container-item__hero-footer__ending>div[data-v-dcab328a],.lotteryDetail__container-item__hero-footer__cost>div[data-v-dcab328a] {
        -webkit-box-flex: .4;
        -webkit-flex: .4;
        flex: .4;
        -webkit-box-pack: end;
        -webkit-justify-content: flex-end;
        justify-content: flex-end;
        color: #fff;
        font-size: 1.125rem;
        font-weight: 600
}

.lotteryDetail__container-item__hero-footer__ending>div img[data-v-dcab328a],.lotteryDetail__container-item__hero-footer__cost>div img[data-v-dcab328a] {
        width: .42667rem;
        height: .42667rem;
        margin-inline:.06667rem}

.lotteryDetail__container-item__hero-footer__ending span[data-v-dcab328a],.lotteryDetail__container-item__hero-footer__cost span[data-v-dcab328a] {
        font-size: .37333rem;
        color: #a6a9ae
}

.lotteryDetail__container-item__hero-footer__ending h5[data-v-dcab328a],.lotteryDetail__container-item__hero-footer__cost h5[data-v-dcab328a] {
        font-weight: 700;
        font-size: .4rem;
        color: #d9ac4f
}

.lotteryDetail__container-item__hero-footer__ending>div[data-v-dcab328a],.lotteryDetail__container-item__hero-footer__cost>div[data-v-dcab328a] {
        font-size: .34667rem;
        font-weight: 500
}

.lotteryDetail__container-item__hero-footer__ending>div>span[data-v-dcab328a],.lotteryDetail__container-item__hero-footer__cost>div>span[data-v-dcab328a] {
        font-size: .37333rem;
        font-weight: 600
}

.lotteryDetail__container-item__hero-footer__cost>div[data-v-dcab328a] {
        color: #d9ac4f
}

.lotteryDetail__container-item__luckyNumber[data-v-dcab328a] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        margin-top: .53333rem;
        padding-inline:.16rem}

.lotteryDetail__container-item__luckyNumber h1[data-v-dcab328a] {
        position: relative;
        margin-bottom: .2rem;
        padding-left: .26667rem;
        color: #fff;
        font-size: .48rem;
        font-weight: 600
}

.lotteryDetail__container-item__luckyNumber h1[data-v-dcab328a]:before {
        content: "";
        position: absolute;
        top: 50%;
        left: 0;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        width: .08rem;
        height: .48rem;
        background: #D9AC4F
}

.lotteryDetail__container-item__luckyNumber>div[data-v-dcab328a] {
        position: relative;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        color: #d37116
}

.lotteryDetail__container-item__luckyNumber>div img[data-v-dcab328a] {
        width: 100%
}

.lotteryDetail__container-item__luckyNumber>div>div[data-v-dcab328a] {
        position: absolute
}

.lotteryDetail__container-item__luckyNumber>div>div[data-v-dcab328a]:first-of-type {
        top: 30%;
        font-size: .56rem;
        font-weight: 900
}

.lotteryDetail__container-item__luckyNumber>div>div[data-v-dcab328a]:last-of-type {
        bottom: 25%;
        font-weight: 700
}

.lotteryDetail__container-item__footer[data-v-dcab328a] {
        margin-top: .66667rem;
        padding-inline:.16rem}

.lotteryDetail__container-item__footer .fontWeight600[data-v-dcab328a] {
        font-weight: 600!important;
        color: #fff
}

.lotteryDetail__container-item__footer-tabBar[data-v-dcab328a] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        gap: 1rem
}

.lotteryDetail__container-item__footer-tabBar h1[data-v-dcab328a] {
        position: relative;
        margin-bottom: .5rem;
        padding-left: .26667rem;
        color: #a6a9ae;
        font-size: .48rem
}

.lotteryDetail__container-item__footer-tabBar h1[data-v-dcab328a]:first-of-type:before {
        content: "";
        position: absolute;
        top: 50%;
        left: 0;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        width: .08rem;
        height: .48rem;
        background: #D9AC4F
}

.lotteryDetail__container-item__footer[data-v-dcab328a] .van-tabs .van-tabs__wrap {
        display: none
}

.lotteryDetail__container-item__footer-participants[data-v-dcab328a] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        gap: .26667rem
}

.lotteryDetail__container-item__footer-participants__item[data-v-dcab328a] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        padding: .21333rem .18667rem;
        border-radius: .13333rem;
        background: #3F3F3F
}

.lotteryDetail__container-item__footer-participants__item-header[data-v-dcab328a] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        padding: .13333rem 0 .21333rem;
        font-weight: 500;
        line-height: .33333rem
}

.lotteryDetail__container-item__footer-participants__item-header span[data-v-dcab328a] {
        color: #fff
}

.lotteryDetail__container-item__footer-participants__item-header span.me[data-v-dcab328a] {
        color: #d9ac4f
}

.lotteryDetail__container-item__footer-participants__item-header span[data-v-dcab328a]:last-of-type {
        color: #a6a9ae;
        font-size: .32rem
}

.lotteryDetail__container-item__footer-participants__item-tickets[data-v-dcab328a] {
        display: grid;
        grid-template-columns: repeat(2,1fr);
        row-gap: .875em;
        padding: .2rem 0;
        color: #fff;
        border-radius: .13333rem;
        background: #333
}

.lotteryDetail__container-item__footer-participants__item-tickets>div[data-v-dcab328a] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        font-size: .75rem
}

.lotteryDetail__container-item__footer-participants__item-tickets>div img[data-v-dcab328a] {
        width: 1.04rem;
        height: .56rem;
        margin-right: .06667rem
}

.lotteryDetail__container-item__footer-participants__item-tickets>div span[data-v-dcab328a] {
        display: block;
        width: 3.2rem;
        color: #fff;
        font-size: .34667rem;
        font-style: normal;
        font-weight: 400;
        line-height: .37333rem
}

.lotteryDetail__container-item__footer-participants__item-tickets__viewAll[data-v-dcab328a] {
        color: #d9ac4f
}

.lotteryDetail__container-item__footer-participants__item-tickets__viewAll>div[data-v-dcab328a] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        width: 4rem;
        height: .66667rem;
        padding: .02667rem 0;
        border: .01333rem solid #D9AC4F;
        border-radius: 9rem;
        font-size: .34667rem;
        font-style: normal;
        font-weight: 400;
        line-height: .37333rem
}

.lotteryDetail__container-item__footer-participants__item-tickets__viewAll>div svg[data-v-dcab328a] {
        width: .42667rem;
        height: .42667rem;
        margin-inline:.06667rem}

.lotteryDetail__container-item__footer-detail[data-v-dcab328a] {
        color: #fff
}

.lotteryDetail__container-item__footer-detail img[data-v-dcab328a] {
        width: 100%
}

.lotteryDetail__container-item__participate[data-v-dcab328a] {
        position: fixed;
        bottom: 0;
        width: 100%;
        padding: .13333rem 0;
        color: #fff;
        text-align: center;
        background: #6F7381;
        z-index: 100;
        height: 1.6rem;
        line-height: 1.33333rem;
        font-size: .45333rem;
        max-width: 10rem
}

.lotteryDetail__container-item__participate.lotteryDetailActive[data-v-dcab328a] {
        background: #D9AC4F;
        color: #8f5206
}

.lotteryDetail__container-item__participate span[data-v-dcab328a] {
        display: block;
        width: 100%;
        height: 100%
}

.lotteryDetail__container-item__actionSheet[data-v-dcab328a] {
        padding: 0 .16rem
}

.lotteryDetail__container-item__actionSheet>div[data-v-dcab328a]:first-of-type {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        color: #d9ac4f
}

.lotteryDetail__container-item__actionSheet>div:first-of-type svg[data-v-dcab328a] {
        width: .64rem;
        height: .64rem;
        margin-right: .08rem
}

.lotteryDetail__container-item__actionSheet>div:first-of-type span[data-v-dcab328a] {
        display: block;
        margin-top: .08rem;
        line-height: .64rem;
        font-size: .48rem
}

.lotteryDetail__container-item__actionSheet>div[data-v-dcab328a]:nth-of-type(2) {
        text-align: center;
        margin-block:.26667rem;color: #a6a9ae;
        font-size: .32rem
}

.lotteryDetail__container-item__actionSheet>div[data-v-dcab328a]:nth-of-type(3) {
        display: grid;
        grid-template-columns: repeat(4,1fr);
        justify-items: center;
        gap: .4rem;
        width: 100%;
        margin-top: .13333rem
}

.lotteryDetail__container-item__actionSheet>div:nth-of-type(3)>div[data-v-dcab328a] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        width: 100%;
        padding-block:.06667rem;color: #fff;
        font-family: Inter,sans-serif;
        border-radius: .13333rem;
        background: #292929;
        -webkit-transition: all .1s ease-in-out;
        transition: all .1s ease-in-out
}

.lotteryDetail__container-item__actionSheet>div:nth-of-type(3)>div span[data-v-dcab328a] {
        font-size: .42667rem
}

.lotteryDetail__container-item__actionSheet>div:nth-of-type(3)>div span[data-v-dcab328a]:last-of-type {
        font-size: .29333rem;
        color: #a6a9ae
}

.lotteryDetail__container-item__actionSheet>div:nth-of-type(3)>div.activeTicket[data-v-dcab328a] {
        color: #8f5206;
        background: -webkit-linear-gradient(top,#FAE59F 0%,#C4933F 100%);
        background: linear-gradient(180deg,#FAE59F 0%,#C4933F 100%)
}

.lotteryDetail__container-item__actionSheet>div:nth-of-type(3)>div.activeTicket span[data-v-dcab328a]:last-of-type {
        color: #8f5206
}

.lotteryDetail__container-item__actionSheet>div[data-v-dcab328a]:nth-of-type(4) {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        width: 100%;
        height: .8rem;
        margin-top: .26667rem;
        background: #303030;
        border-radius: .13333rem
}

.lotteryDetail__container-item__actionSheet>div:nth-of-type(4)>span[data-v-dcab328a],.lotteryDetail__container-item__actionSheet>div:nth-of-type(4)>div[data-v-dcab328a] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        height: 100%;
        padding: .06667rem .13333rem
}

.lotteryDetail__container-item__actionSheet>div:nth-of-type(4)>span[data-v-dcab328a] {
        -webkit-box-flex: .6;
        -webkit-flex: .6;
        flex: .6;
        color: #a6a9ae
}

.lotteryDetail__container-item__actionSheet>div:nth-of-type(4)>div[data-v-dcab328a] {
        -webkit-box-flex: .4;
        -webkit-flex: .4;
        flex: .4;
        -webkit-box-pack: end;
        -webkit-justify-content: flex-end;
        justify-content: flex-end;
        gap: .375rem;
        color: #d9ac4f
}

.lotteryDetail__container-item__actionSheet>div:nth-of-type(4)>div svg[data-v-dcab328a] {
        width: .42667rem;
        height: .42667rem
}

.lotteryDetail__container-item__actionSheet-button[data-v-dcab328a] {
        border: none;
        background: #D9AC4F;
        height: 1.6rem;
        border-radius: 0;
        color: #8f5206;
        margin-top: .53333rem
}

.lotteryDetail__container-item__actionSheetCloseBtn[data-v-dcab328a] {
        position: fixed;
        bottom: 10.4rem;
        left: 50%;
        margin-left: -.4rem;
        text-align: center;
        -webkit-transition: all .3s ease-in-out;
        transition: all .3s ease-in-out;
        z-index: 2000
}

.lotteryDetail__container-item__actionSheetCloseBtn svg[data-v-dcab328a] {
        width: .8rem;
        height: .8rem
}

.lotteryDetail__container[data-v-dcab328a] .van-dialog {
        background: transparent;
        width: 8.93333rem
}

.lotteryDetail__container[data-v-dcab328a] .van-dialog .van-dialog__content {
        position: relative
}

.lotteryDetail__container[data-v-dcab328a] .van-dialog .van-dialog__content img {
        width: 100%
}

.lotteryDetail__container .van-dialog__content-title[data-v-dcab328a],.lotteryDetail__container .van-dialog__content-subTitle[data-v-dcab328a],.lotteryDetail__container .van-dialog__content-ticketsList[data-v-dcab328a],.lotteryDetail__container .van-dialog__content-tips[data-v-dcab328a],.lotteryDetail__container .van-dialog__content-confirm[data-v-dcab328a],.lotteryDetail__container .van-dialog__content-myTreasure[data-v-dcab328a],.lotteryDetail__container .van-dialog__content-successfullyParticipatedBottom[data-v-dcab328a] {
        position: absolute;
        left: 50%;
        -webkit-transform: translateX(-50%);
        transform: translate(-50%);
        width: 100%;
        text-align: center
}

.lotteryDetail__container .van-dialog__content-title[data-v-dcab328a] {
        top: 1.6rem;
        color: #fff;
        font-size: .56rem;
        font-weight: 700
}

.lotteryDetail__container .van-dialog__content-subTitle[data-v-dcab328a] {
        top: 2.8rem;
        color: #ffef89
}

.lotteryDetail__container .van-dialog__content-subTitle>span[data-v-dcab328a] {
        color: #ffef89;
        font-size: .64rem;
        vertical-align: middle
}

.lotteryDetail__container .van-dialog__content-tips[data-v-dcab328a] {
        top: 3.66667rem;
        color: #fff;
        font-size: .32rem;
        font-weight: 300
}

.lotteryDetail__container .van-dialog__content-ticketsList[data-v-dcab328a] {
        top: 55%;
        -webkit-transform: translate(-50%,-50%);
        transform: translate(-50%,-50%);
        width: 90%;
        height: 40%;
        padding-inline:.06667rem;overflow: hidden;
        z-index: 1
}

.lotteryDetail__container .van-dialog__content-ticketsList>div[data-v-dcab328a] {
        display: grid;
        grid-template-columns: repeat(2,1fr);
        grid-template-rows: .53333rem .53333rem;
        -webkit-column-gap: .06667rem;
        column-gap: .06667rem;
        width: 100%;
        height: 100%;
        padding-bottom: .53333rem;
        overflow-y: auto
}

.lotteryDetail__container .van-dialog__content-ticketsList>div>div[data-v-dcab328a] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        height: .53333rem;
        color: #e45222;
        font-size: .32rem
}

.lotteryDetail__container .van-dialog__content-ticketsList__ticket[data-v-dcab328a] {
        width: .88rem;
        height: .48rem;
        margin-right: .06667rem
}

.lotteryDetail__container .van-dialog__content-confirm[data-v-dcab328a] {
        bottom: 2.8rem;
        color: #8f5206;
        font-size: .48rem;
        font-weight: 700;
        z-index: 3
}

.lotteryDetail__container .van-dialog__content-myTreasure[data-v-dcab328a] {
        bottom: .26667rem;
        width: 7.97333rem;
        height: 1.06667rem;
        padding: .25rem;
        color: #fff;
        text-align: center;
        font-size: .42667rem;
        border: .01333rem solid #fff;
        border-radius: 9rem;
        z-index: 3
}

.lotteryDetail__container .van-dialog__content-successfullyParticipatedBottom[data-v-dcab328a] {
        bottom: 0;
        z-index: 2
}

.van-toast[data-v-ad6ccd74] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-ad6ccd74] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-ad6ccd74] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-ad6ccd74] {
        height: 80%
}

.myLottery__container[data-v-ad6ccd74] {
        font-family: bahnschrift
}

.myLottery__container .boxShadow4px[data-v-ad6ccd74] {
        background: -webkit-linear-gradient(top,#FAE59F 0%,#C4933F 100%);
        background: linear-gradient(180deg,#FAE59F 0%,#C4933F 100%);
        color: #8f5206
}

.myLottery__container[data-v-ad6ccd74] .van-nav-bar {
        background-color: #f7f8ff
}

.myLottery__container[data-v-ad6ccd74] .van-nav-bar .van-nav-bar__content .van-nav-bar__left .van-icon,.myLottery__container[data-v-ad6ccd74] .van-nav-bar .van-nav-bar__content .van-nav-bar__title {
        color: #151515
}

.myLottery__container[data-v-ad6ccd74] .van-tabs__line {
        width: 0;
        height: 0;
        border-left: .10667rem solid transparent;
        border-right: .10667rem solid transparent;
        border-bottom: .10667rem solid #fff;
        background-color: unset!important
}

.myLottery__container-products[data-v-ad6ccd74] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        gap: .4rem;
        padding: .4rem .16rem 0
}

.myLottery__container-products__item[data-v-ad6ccd74] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        gap: .26667rem;
        padding: .18667rem;
        border-radius: .13333rem;
        background: #3F3F3F
}

.myLottery__container-products__item-header[data-v-ad6ccd74] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: stretch;
        -webkit-align-items: stretch;
        align-items: stretch;
        gap: .2rem
}

.myLottery__container-products__item-header__left[data-v-ad6ccd74] {
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        flex: 1;
        position: relative;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        padding: .13333rem 0;
        color: #fff;
        font-size: .75em;
        border-radius: .13333rem;
        width: 4.26667rem;
        height: 3.2rem;
        background: -webkit-linear-gradient(top,#6F7381 0%,#A9AAB5 98.95%);
        background: linear-gradient(180deg,#6F7381 0%,#A9AAB5 98.95%)
}

.myLottery__container-products__item-header__left>span[data-v-ad6ccd74] {
        position: absolute;
        top: .13333rem;
        left: 0;
        padding: .04rem .26667rem;
        border-top-right-radius: 9rem;
        border-bottom-right-radius: 9rem;
        z-index: 2
}

.myLottery__container-products__item-header__left>span[data-v-ad6ccd74]:last-of-type {
        top: .09333rem;
        padding: .08rem .26667rem .08rem .30667rem;
        color: transparent;
        white-space: nowrap;
        z-index: 1
}

.myLottery__container-products__item-header__left>img[data-v-ad6ccd74] {
        width: 2.82667rem;
        height: 2.82667rem
}

.myLottery__container-products__item-header__right[data-v-ad6ccd74] {
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        flex: 1;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        gap: .26667rem;
        font-size: .75rem;
        color: #fff
}

.myLottery__container-products__item-header__right h1[data-v-ad6ccd74] {
        margin-bottom: .4rem;
        font-size: .37333rem;
        font-weight: 600
}

.myLottery__container-products__item-header__right h1>span[data-v-ad6ccd74] {
        color: #f95959
}

.myLottery__container-products__item-header__right>div[data-v-ad6ccd74] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        padding: .21333rem .13333rem;
        color: #a6a9ae;
        border-radius: .13333rem;
        background: #292929;
        font-size: .29333rem
}

.myLottery__container-products__item-header__right>div img[data-v-ad6ccd74] {
        width: .32rem;
        height: .32rem;
        margin-right: .04rem
}

.myLottery__container-products__item-header__right>div:last-of-type span[data-v-ad6ccd74] {
        font-size: .37333rem
}

.myLottery__container-products__item-header__right>div:last-of-type span.left[data-v-ad6ccd74] {
        color: #fff
}

.myLottery__container-products__item-header__right>div:last-of-type span.right[data-v-ad6ccd74] {
        position: relative;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        margin-left: auto;
        color: #d9ac4f
}

.myLottery__container-products__item-header__right>div:last-of-type span.right[data-v-ad6ccd74]:before {
        content: "";
        position: absolute;
        left: -25%;
        width: .01333rem;
        padding-block:.09333rem;background: #bdcadd
}

.myLottery__container-products__item-footer[data-v-ad6ccd74] {
        color: #fff;
        font-size: .34667rem;
        border-radius: .13333rem;
        background: #333333;
        overflow: hidden
}

.myLottery__container-products__item-footer__header[data-v-ad6ccd74] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        padding: .2rem .13333rem;
        background: #D9AC4F;
        color: #8f5206;
        font-weight: 600
}

.myLottery__container-products__item-footer__body[data-v-ad6ccd74] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-flex-wrap: wrap;
        flex-wrap: wrap;
        row-gap: .5rem;
        padding: .13333rem .06667rem
}

.myLottery__container-products__item-footer__body-item[data-v-ad6ccd74] {
        -webkit-box-flex: 40%;
        -webkit-flex: 40%;
        flex: 40%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        font-size: .34667rem
}

.myLottery__container-products__item-footer__body-item img[data-v-ad6ccd74] {
        width: 1.04rem;
        height: .56rem;
        margin-right: .06667rem
}

.myLottery__container-products__item-button[data-v-ad6ccd74] {
        width: 100%;
        margin-block:.26667rem;color: #fff;
        text-align: center;
        border-radius: 9rem;
        height: .93333rem;
        line-height: .93333rem;
        font-size: .4rem;
        background: -webkit-linear-gradient(top,#A9AAB5 0%,#6F7381 100%);
        background: linear-gradient(180deg,#A9AAB5 0%,#6F7381 100%)
}

.myLottery__container-products__empty[data-v-ad6ccd74] {
        position: relative;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        gap: .5rem;
        width: 100%;
        padding-top: 6rem;
        color: #acafc2;
        font-size: .875rem
}

.myLottery__container-products__empty img[data-v-ad6ccd74] {
        width: 2rem;
        max-width: 4rem
}

.van-toast[data-v-7c8d0cc7] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-7c8d0cc7] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-7c8d0cc7] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-7c8d0cc7] {
        height: 80%
}

.myOrders__container[data-v-7c8d0cc7] {
        padding-inline:.32rem;padding-block:0 .26667rem;font-family: bahnschrift
}

.myOrders__container[data-v-7c8d0cc7] .van-nav-bar {
        background-color: #f7f8ff
}

.myOrders__container[data-v-7c8d0cc7] .van-nav-bar .van-nav-bar__content .van-nav-bar__left .van-icon,.myOrders__container[data-v-7c8d0cc7] .van-nav-bar .van-nav-bar__content .van-nav-bar__title {
        color: #151515
}

.myOrders__container-item[data-v-7c8d0cc7] {
        height: -webkit-fit-content;
        height: fit-content;
        margin: .26667rem 0;
        padding: .21333rem .18667rem;
        border-radius: .26667rem;
        background: #3F3F3F
}

.myOrders__container-item__header[data-v-7c8d0cc7] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        gap: .16rem
}

.myOrders__container-item__header-left[data-v-7c8d0cc7] {
        position: relative;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        width: 5.06667rem;
        height: 2.4rem;
        border-radius: .26667rem
}

.myOrders__container-item__header-left>img[data-v-7c8d0cc7] {
        width: 100%;
        height: 100%
}

.myOrders__container-item__header-left>div[data-v-7c8d0cc7] {
        position: absolute;
        bottom: 0;
        left: 0;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        width: 2.26667rem;
        height: .8rem
}

.myOrders__container-item__header-left>div img[data-v-7c8d0cc7] {
        width: 100%;
        height: .8rem
}

.myOrders__container-item__header-left>div span[data-v-7c8d0cc7] {
        position: absolute;
        top: 50%;
        left: 50%;
        -webkit-transform: translate(-50%,-50%);
        transform: translate(-50%,-50%);
        color: #fff;
        font-size: .29333rem
}

.myOrders__container-item__header-right[data-v-7c8d0cc7] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: start;
        -webkit-align-items: flex-start;
        align-items: flex-start;
        gap: .21333rem;
        width: 100%;
        font-size: .37333rem;
        color: #fff
}

.myOrders__container-item__header-right h1[data-v-7c8d0cc7] {
        height: .88rem;
        width: 100%;
        line-height: 1.1em;
        padding-right: .2rem;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        color: #fff
}

.myOrders__container-item__header-right>div[data-v-7c8d0cc7] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        width: 100%
}

.myOrders__container-item__header-right>div span[data-v-7c8d0cc7] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        font-size: .4rem
}

.myOrders__container-item__header-right>div span[data-v-7c8d0cc7]:first-of-type {
        gap: .10667rem;
        color: #d9ac4f
}

.myOrders__container-item__header-right>div span:first-of-type i[data-v-7c8d0cc7] {
        font-size: .42667rem
}

.myOrders__container-item__footer[data-v-7c8d0cc7] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        gap: .16rem;
        margin-top: .16rem
}

.myOrders__container-item__footer>div[data-v-7c8d0cc7] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        color: #a6a9ae;
        font-weight: 400;
        font-size: .32rem;
        background: #292929
}

.myOrders__container-item__footer>div span[data-v-7c8d0cc7] {
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        flex: 1;
        height: .56rem;
        padding: 0 .26667rem;
        text-align: end;
        line-height: .66667rem
}

.myOrders__container-item__footer>div span[data-v-7c8d0cc7]:first-of-type {
        text-align: start;
        background: #292929;
        -webkit-clip-path: polygon(0 0,100% 0,90% 100%,0 100%);
        clip-path: polygon(0 0,100% 0,90% 100%,0 100%)
}

.van-toast[data-v-8d06f16f] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-8d06f16f] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-8d06f16f] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-8d06f16f] {
        height: 80%
}

.orderDetail__container[data-v-8d06f16f] {
        padding-block:0 1.49333rem;font-family: bahnschrift
}

.orderDetail__container[data-v-8d06f16f] .navbar .navbar__content .navbar__content-left .van-icon,.orderDetail__container[data-v-8d06f16f] .navbar .navbar__content .navbar__content-center {
        color: #fff
}

.orderDetail__container-header[data-v-8d06f16f] {
        position: relative;
        width: 100%;
        height: 4.8rem;
        background: -webkit-linear-gradient(right,#8F5206,#D9AC4F);
        background: linear-gradient(-90deg,#8F5206,#D9AC4F)
}

.orderDetail__container-header__content[data-v-8d06f16f] {
        position: absolute;
        top: 50%;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        width: 100%;
        padding-inline:.32rem 1.24rem;color: #fff
}

.orderDetail__container-header__content h1[data-v-8d06f16f] {
        margin-bottom: .32rem;
        font-size: .48rem;
        font-weight: 600
}

.orderDetail__container-header__content p[data-v-8d06f16f] {
        font-size: .34667rem
}

.orderDetail__container-header__content img[data-v-8d06f16f] {
        width: 2.13333rem;
        height: 2.13333rem
}

.orderDetail__container-content[data-v-8d06f16f] {
        position: relative;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        gap: .5rem;
        margin-top: -.86667rem;
        padding-inline:.32rem}

.orderDetail__container-content__itemDetail[data-v-8d06f16f] {
        padding: .36rem .13333rem;
        background: #3F3F3F;
        border-radius: .26667rem;
        color: #fff
}

.orderDetail__container-content__itemDetail-header[data-v-8d06f16f] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: start;
        -webkit-align-items: flex-start;
        align-items: flex-start
}

.orderDetail__container-content__itemDetail-header h1[data-v-8d06f16f] {
        width: 100%;
        padding-bottom: .23333rem;
        font-size: .42667rem;
        font-weight: 700;
        border-bottom: .01333rem solid #292929
}

.orderDetail__container-content__itemDetail-header i[data-v-8d06f16f] {
        margin-right: .25rem;
        font-size: .64rem
}

.orderDetail__container-content__itemDetail-body[data-v-8d06f16f] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        margin-top: .40667rem
}

.orderDetail__container-content__itemDetail-body__top[data-v-8d06f16f] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        gap: .16rem
}

.orderDetail__container-content__itemDetail-body__top>div[data-v-8d06f16f]:first-of-type {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-flex-shrink: 0;
        flex-shrink: 0;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        width: 3.2rem;
        height: 2.4rem;
        border-radius: .26667rem;
        background: -webkit-linear-gradient(top,#6F7381,#A9AAB5);
        background: linear-gradient(180deg,#6F7381,#A9AAB5)
}

.orderDetail__container-content__itemDetail-body__top>div:first-of-type img[data-v-8d06f16f] {
        width: 100%;
        height: 100%
}

.orderDetail__container-content__itemDetail-body__top>div[data-v-8d06f16f]:last-of-type {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: start;
        -webkit-align-items: flex-start;
        align-items: flex-start;
        gap: .29333rem;
        width: 100%
}

.orderDetail__container-content__itemDetail-body__top>div:last-of-type h1[data-v-8d06f16f] {
        font-size: .37333rem;
        line-height: .37333rem;
        line-height: 1.2
}

.orderDetail__container-content__itemDetail-body__top>div:last-of-type>div[data-v-8d06f16f] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        width: 100%
}

.orderDetail__container-content__itemDetail-body__top>div:last-of-type>div .colorf95959[data-v-8d06f16f] {
        color: #d9ac4f
}

.orderDetail__container-content__itemDetail-body__top>div:last-of-type>div span[data-v-8d06f16f] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        font-size: .37333rem;
        line-height: .37333rem
}

.orderDetail__container-content__itemDetail-body__top>div:last-of-type>div span[data-v-8d06f16f]:first-of-type {
        gap: .08rem;
        color: #d9ac4f
}

.orderDetail__container-content__itemDetail-body__top>div:last-of-type>div span:first-of-type i[data-v-8d06f16f] {
        font-size: .42667rem
}

.orderDetail__container-content__itemDetail-body__top>div:last-of-type>div span:first-of-type span[data-v-8d06f16f] {
        font-size: .4rem
}

.orderDetail__container-content__itemDetail-body__footer[data-v-8d06f16f] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        gap: .16rem;
        margin-top: .16rem
}

.orderDetail__container-content__itemDetail-body__footer>div[data-v-8d06f16f] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        color: #a6a9ae;
        font-weight: 500;
        font-size: .32rem;
        background: #292929
}

.orderDetail__container-content__itemDetail-body__footer>div[data-v-8d06f16f]:first-of-type {
        padding-right: .5rem
}

.orderDetail__container-content__itemDetail-body__footer>div:first-of-type span[data-v-8d06f16f]:last-of-type {
        padding-right: .25rem
}

.orderDetail__container-content__itemDetail-body__footer>div:first-of-type i[data-v-8d06f16f] {
        font-size: .32rem
}

.orderDetail__container-content__itemDetail-body__footer>div span[data-v-8d06f16f] {
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        flex: 1;
        padding: .09333rem .26667rem;
        text-align: end
}

.orderDetail__container-content__itemDetail-body__footer>div span[data-v-8d06f16f]:first-of-type {
        text-align: start;
        background: #292929;
        -webkit-clip-path: polygon(0 0,100% 0,90% 100%,0 100%);
        clip-path: polygon(0 0,100% 0,90% 100%,0 100%)
}

.orderDetail__container-content__address[data-v-8d06f16f] {
        padding: .4rem .14667rem;
        color: #fff;
        background: #3F3F3F;
        border-radius: .26667rem
}

.orderDetail__container-content__address-header[data-v-8d06f16f] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: start;
        -webkit-align-items: flex-start;
        align-items: flex-start
}

.orderDetail__container-content__address-header h1[data-v-8d06f16f] {
        width: 100%;
        padding-bottom: .23333rem;
        font-size: .42667rem;
        font-weight: 700;
        border-bottom: .01333rem solid #292929
}

.orderDetail__container-content__address-header i[data-v-8d06f16f] {
        margin-right: .06667rem;
        color: #d9ac4f;
        font-size: .64rem
}

.orderDetail__container-content__address-body[data-v-8d06f16f] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        gap: .25333rem;
        margin-top: .55333rem;
        font-size: .4rem
}

.orderDetail__container-content__address-body p[data-v-8d06f16f] {
        color: #888;
        font-size: .34667rem;
        word-wrap: break-word
}

.orderDetail__container-content__orderDetail[data-v-8d06f16f] {
        padding: .4rem .14667rem;
        color: #fff;
        background: #3F3F3F;
        border-radius: .26667rem
}

.orderDetail__container-content__orderDetail-header[data-v-8d06f16f] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: start;
        -webkit-align-items: flex-start;
        align-items: flex-start;
        font-size: 1.125rem
}

.orderDetail__container-content__orderDetail-header h1[data-v-8d06f16f] {
        width: 100%;
        padding-bottom: .23333rem;
        font-size: .42667rem;
        font-weight: 700;
        border-bottom: .01333rem solid #292929
}

.orderDetail__container-content__orderDetail-header i[data-v-8d06f16f] {
        margin-right: .06667rem;
        color: #d9ac4f;
        font-size: .64rem
}

.orderDetail__container-content__orderDetail-body[data-v-8d06f16f] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        gap: .62667rem;
        margin-top: .55333rem
}

.orderDetail__container-content__orderDetail-body__item[data-v-8d06f16f] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between
}

.orderDetail__container-content__orderDetail-body__item span[data-v-8d06f16f] {
        color: #888;
        font-size: .34667rem
}

.orderDetail__container-content__orderDetail-body__item:first-of-type span[data-v-8d06f16f]:last-of-type {
        color: #d9ac4f
}

.orderDetail__container-content__btn[data-v-8d06f16f] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        gap: .37333rem;
        width: 100%;
        padding-inline:.6rem}

.orderDetail__container-content__btn-cancel[data-v-8d06f16f] {
        color: #fff;
        background: -webkit-linear-gradient(top,#FAE59F,#C4933F);
        background: linear-gradient(180deg,#FAE59F,#C4933F)
}

.orderDetail__container-content__btn div[data-v-8d06f16f] {
        border-radius: 9rem;
        width: 100%;
        height: .93333rem;
        text-align: center;
        line-height: .93333rem
}

.orderDetail__container-content__btn div[data-v-8d06f16f]:last-of-type {
        color: #d9ac4f;
        border: .01333rem solid #D9AC4F
}

.van-toast[data-v-72838e3b] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-72838e3b] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-72838e3b] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-72838e3b] {
        height: 80%
}

.receiveLottery__container[data-v-72838e3b] {
        padding-inline:.16rem;padding-block:.66667rem 1rem;font-family: bahnschrift
}

.receiveLottery__container[data-v-72838e3b] .van-nav-bar {
        background-color: #f7f8ff
}

.receiveLottery__container[data-v-72838e3b] .van-nav-bar .van-nav-bar__content .van-nav-bar__left .van-icon,.receiveLottery__container[data-v-72838e3b] .van-nav-bar .van-nav-bar__content .van-nav-bar__title {
        color: #151515
}

.receiveLottery__container-hero[data-v-72838e3b] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        height: 3.57333rem;
        padding: .16rem;
        border-radius: .13333rem;
        background: #3F3F3F
}

.receiveLottery__container-hero__left[data-v-72838e3b],.receiveLottery__container-hero__right[data-v-72838e3b] {
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        flex: 1;
        height: 100%
}

.receiveLottery__container-hero__left[data-v-72838e3b] {
        position: relative;
        display: grid;
        place-items: center;
        border-radius: .13333rem;
        width: 4.26667rem;
        height: 3.2rem;
        background: -webkit-linear-gradient(top,#6F7381 -3.33%,#A9AAB5 100%);
        background: linear-gradient(180deg,#6F7381 -3.33%,#A9AAB5 100%)
}

.receiveLottery__container-hero__left img[data-v-72838e3b] {
        height: 2.82667rem;
        width: 2.82667rem
}

.receiveLottery__container-hero__left span[data-v-72838e3b] {
        position: absolute;
        top: .13333rem;
        left: 0;
        padding: .06667rem .26667rem .06667rem .4rem;
        color: #fff;
        font-size: .29333rem;
        border-top-right-radius: 9rem;
        border-bottom-right-radius: 9rem
}

.receiveLottery__container-hero__left span[data-v-72838e3b]:first-of-type {
        background: -webkit-linear-gradient(right,#fabb2a 18.36%,#eb9315 89.84%,#fbe571 96.48%,#ed8b19 100%);
        background: linear-gradient(270deg,#fabb2a 18.36%,#eb9315 89.84%,#fbe571 96.48%,#ed8b19 100%);
        z-index: 2
}

.receiveLottery__container-hero__left span[data-v-72838e3b]:last-of-type {
        top: .09333rem;
        padding: .10667rem .26667rem .10667rem .44rem;
        background: -webkit-linear-gradient(left,#ff9c3a -3.24%,#ffe55c 4.63%,#ffb936 13.43%,#fff962 76.75%);
        background: linear-gradient(90deg,#ff9c3a -3.24%,#ffe55c 4.63%,#ffb936 13.43%,#fff962 76.75%);
        z-index: 1
}

.receiveLottery__container-hero__right[data-v-72838e3b] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-pack: end;
        -webkit-justify-content: flex-end;
        justify-content: flex-end;
        padding-left: .29333rem
}

.receiveLottery__container-hero__right h1[data-v-72838e3b] {
        margin-bottom: auto;
        color: #fff;
        font-size: .37333rem
}

.receiveLottery__container-hero__right h1 span[data-v-72838e3b] {
        color: #d9ac4f
}

.receiveLottery__container-hero__right div[data-v-72838e3b] {
        padding: .06667rem .13333rem;
        font-size: .29333rem;
        border-radius: .06667rem
}

.receiveLottery__container-hero__right div[data-v-72838e3b]:first-of-type {
        color: #a6a9ae;
        height: .8rem;
        line-height: .8rem;
        background: #292929;
        margin-bottom: .2rem
}

.receiveLottery__container-hero__right div[data-v-72838e3b]:last-of-type {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        height: .90667rem;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        color: #8f5206;
        background: #D9AC4F;
        font-size: .34667rem
}

.receiveLottery__container-hero__right div:last-of-type img[data-v-72838e3b] {
        width: 1.04rem;
        height: .56rem;
        margin-right: .06667rem
}

.receiveLottery__container-empty[data-v-72838e3b] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center
}

.receiveLottery__container-empty img[data-v-72838e3b] {
        width: 8rem;
        margin-bottom: .5rem
}

.receiveLottery__container-empty p[data-v-72838e3b] {
        color: #acafc2;
        font-size: .75rem
}

.receiveLottery__container-note[data-v-72838e3b] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        margin-top: 1rem;
        padding: .29333rem .26667rem;
        color: #a6a9ae;
        font-size: .32rem;
        border-radius: 9rem;
        background: #404040
}

.receiveLottery__container-note img[data-v-72838e3b] {
        width: .42667rem;
        height: .42667rem;
        margin-right: .26667rem
}

.receiveLottery__container-address[data-v-72838e3b] {
        margin-top: .69333rem
}

.receiveLottery__container-address__header[data-v-72838e3b] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        margin-bottom: .33333rem;
        color: #fff;
        font-size: .48rem
}

.receiveLottery__container-address__header i[data-v-72838e3b] {
        margin-right: .24rem;
        color: #d9ac4f;
        font-size: .64rem
}

.receiveLottery__container-address__body[data-v-72838e3b] {
        margin-bottom: .26667rem;
        border-radius: .13333rem;
        background: #3F3F3F;
        overflow: hidden
}

.receiveLottery__container-address__body-content[data-v-72838e3b] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        gap: .33333rem;
        padding: .26667rem .16rem;
        color: #fff
}

.receiveLottery__container-address__body-content div[data-v-72838e3b] {
        font-size: .4rem
}

.receiveLottery__container-address__body-content div[data-v-72838e3b]:last-of-type {
        color: #a6a9ae;
        font-size: .34667rem;
        word-wrap: break-word
}

.receiveLottery__container-address__body-footer[data-v-72838e3b] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        padding: .37333rem .26667rem;
        background: #404040
}

.receiveLottery__container-address__body-footer div[data-v-72838e3b] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        color: #d9ac4f;
        font-size: .32rem
}

.receiveLottery__container-address__body-footer div img[data-v-72838e3b] {
        width: .48rem;
        height: .48rem;
        margin-right: .13333rem
}

.receiveLottery__container-address__body-footer div[data-v-72838e3b] .van-checkbox__label {
        color: #d9ac4f
}

.receiveLottery__container-address__empty[data-v-72838e3b] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        padding: 1.12rem 0;
        border-radius: .13333rem;
        background: #3F3F3F
}

.receiveLottery__container-address__empty img[data-v-72838e3b] {
        width: 1.17333rem;
        height: 1.17333rem;
        margin-bottom: .2rem
}

.receiveLottery__container-address__empty p[data-v-72838e3b] {
        color: #a6a9ae;
        font-size: .37333rem
}

.receiveLottery__container-receiverBtn[data-v-72838e3b] {
        width: 100%;
        margin-top: 2rem;
        padding-block:.375rem;color: #8f5206;
        text-align: center;
        border-radius: 9rem;
        font-weight: 700;
        font-size: .4rem;
        background: -webkit-linear-gradient(top,#FAE59F 0%,#C4933F 100%);
        background: linear-gradient(180deg,#FAE59F 0%,#C4933F 100%)
}

.receiveLottery__container-receiverBtn.disabled[data-v-72838e3b] {
        background: -webkit-linear-gradient(top,#A9AAB5 0%,#6F7381 100%);
        background: linear-gradient(180deg,#A9AAB5 0%,#6F7381 100%);
        color: #fff
}

.receiveLottery__container[data-v-72838e3b] .van-dialog {
        overflow: visible
}

.receiveLottery__container[data-v-72838e3b] .van-dialog .van-dialog__content {
        position: relative;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        padding-block:2.46667rem .6rem}

.receiveLottery__container .van-dialog__content img[data-v-72838e3b] {
        position: absolute;
        left: 50%;
        -webkit-transform: translateX(-50%);
        transform: translate(-50%)
}

.receiveLottery__container .van-dialog__content img[data-v-72838e3b]:first-of-type {
        top: -1rem;
        width: 3.73333rem
}

.receiveLottery__container .van-dialog__content img[data-v-72838e3b]:last-of-type {
        bottom: -1.13333rem;
        width: .8rem;
        height: .8rem
}

.receiveLottery__container .van-dialog__content-title[data-v-72838e3b] {
        margin-bottom: .29333rem;
        color: #fff;
        font-size: .48rem
}

.receiveLottery__container .van-dialog__content-note[data-v-72838e3b] {
        margin-bottom: 1.13333rem;
        color: #a6a9ae;
        font-size: .32rem
}

.receiveLottery__container .van-dialog__content-btn[data-v-72838e3b] {
        width: 4.34667rem;
        height: 1.06667rem;
        color: #8f5206;
        line-height: 1.06667rem;
        text-align: center;
        font-size: .42667rem;
        border-radius: 9rem;
        font-weight: 700;
        background: -webkit-linear-gradient(top,#FAE59F 0%,#C4933F 100%);
        background: linear-gradient(180deg,#FAE59F 0%,#C4933F 100%)
}

.van-toast[data-v-569c9a34] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-569c9a34] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-569c9a34] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-569c9a34] {
        height: 80%
}

.pointMallRecord__container[data-v-569c9a34] {
        padding-block:0 1rem;font-family: Inter,sans-serif
}

.pointMallRecord__container[data-v-569c9a34] .navbar__content-right .navbar__content-right__buttons {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        gap: .4rem
}

.pointMallRecord__container[data-v-569c9a34] .navbar__content-right .navbar__content-right__buttons img {
        width: .64rem;
        height: .64rem
}

.pointMallRecord__container .color2fb192[data-v-569c9a34] {
        color: #2fb192!important
}

.pointMallRecord__container .colorf95959[data-v-569c9a34] {
        color: #f95959!important
}

.pointMallRecord__container .no-data[data-v-569c9a34] {
        margin-top: 5rem
}

.pointMallRecord__container .no-data .empty__container[data-v-569c9a34] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center
}

.pointMallRecord__container-header[data-v-569c9a34] {
        display: grid;
        place-items: center;
        width: 100%;
        height: 3.2rem;
        color: #fff;
        background: url(/assets/png/recordHeaderBg-fa25bf7e.png) no-repeat;
        background-size: cover;
        background-color: #d9ac4f
}

.pointMallRecord__container-header .points-date[data-v-569c9a34] {
        width: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: start;
        -webkit-justify-content: flex-start;
        justify-content: flex-start;
        padding-left: .8rem;
        padding-top: .26667rem
}

.pointMallRecord__container-header-date[data-v-569c9a34] {
        min-width: 3.46667rem;
        height: .61333rem;
        line-height: .61333rem;
        border: .00667rem solid #8F5206;
        border-radius: .53333rem;
        font-size: .37333rem;
        text-align: center;
        color: #8f5206;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        display: -webkit-inline-box;
        display: -webkit-inline-flex;
        display: inline-flex;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between
}

.pointMallRecord__container-header-date img[data-v-569c9a34] {
        width: .53333rem;
        height: .53333rem;
        margin-left: .2rem
}

.pointMallRecord__container-header-date div[data-v-569c9a34] {
        padding: .05333rem 0 0 .21333rem
}

.pointMallRecord__container-header__content[data-v-569c9a34] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        gap: 3.08rem;
        width: 100%
}

.pointMallRecord__container-header__content-left[data-v-569c9a34],.pointMallRecord__container-header__content-right[data-v-569c9a34] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: end;
        -webkit-justify-content: flex-end;
        justify-content: flex-end;
        height: 100%
}

.pointMallRecord__container-header__content-left div[data-v-569c9a34]:first-of-type,.pointMallRecord__container-header__content-right div[data-v-569c9a34]:first-of-type {
        margin-bottom: .32rem;
        font-size: .64rem;
        font-weight: 500;
        line-height: .64rem;
        color: #fffcb0
}

.pointMallRecord__container-header__content-left div[data-v-569c9a34]:last-of-type,.pointMallRecord__container-header__content-right div[data-v-569c9a34]:last-of-type {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        width: 100%;
        font-size: .37333rem;
        color: #8f5206
}

.pointMallRecord__container-header__content-left div:last-of-type img[data-v-569c9a34],.pointMallRecord__container-header__content-right div:last-of-type img[data-v-569c9a34] {
        width: .64rem;
        height: .64rem;
        margin-right: .10667rem
}

.pointMallRecord__container-list[data-v-569c9a34] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        gap: .21333rem;
        margin-top: .53333rem;
        padding-inline:.32rem}

.pointMallRecord__container-list__item[data-v-569c9a34] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        padding: .25333rem .26667rem;
        border-radius: .13333rem;
        background: #3F3F3F
}

.pointMallRecord__container-list__item-left div[data-v-569c9a34]:first-of-type {
        color: #fff;
        font-size: .4rem
}

.pointMallRecord__container-list__item-left div[data-v-569c9a34]:last-of-type {
        margin-top: .10667rem;
        font-size: .32rem;
        color: #a6a9ae
}

.pointMallRecord__container-list__item-right[data-v-569c9a34] {
        font-size: .48rem;
        font-weight: 400;
        font-family: bahnschrift
}

.progress__container[data-v-d2a4aa69] {
        position: relative;
        width: 1.46667rem;
        height: 1.46667rem;
        border-radius: 50%;
        border: .01333rem solid #ebeef5;
        -webkit-transform: rotateY(180deg);
        transform: rotateY(180deg)
}

.progress__container-content[data-v-d2a4aa69] {
        position: absolute;
        top: -.02667rem;
        left: 0;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        width: 1.46667rem;
        height: 1.46667rem
}

.progress__container-content__left[data-v-d2a4aa69] {
        overflow: hidden;
        width: 1.46667rem;
        height: 1.46667rem
}

.progress__container-content__left-content[data-v-d2a4aa69] {
        width: 1.46667rem;
        height: 1.46667rem;
        border-radius: 50%;
        border-width: .02667rem;
        border-color: var(--28e29a89);
        border-top-color: transparent;
        border-right-color: transparent;
        border-style: solid;
        -webkit-transform: var(--aa69dc96);
        transform: var(--aa69dc96);
        -webkit-transition: -webkit-transform .1s;
        transition: -webkit-transform .1s;
        transition: transform .1s;
        transition: transform .1s,-webkit-transform .1s
}

.progress__container-content__right[data-v-d2a4aa69] {
        position: relative;
        overflow: hidden;
        width: 1.46667rem;
        height: 1.46667rem
}

.progress__container-content__right-content[data-v-d2a4aa69] {
        position: absolute;
        right: 0;
        width: 1.46667rem;
        height: 1.46667rem;
        border-radius: 50%;
        border-width: .02667rem;
        border-color: var(--28e29a89);
        border-top-color: transparent;
        border-left-color: transparent;
        border-style: solid;
        -webkit-transform: var(--2daea9e8);
        transform: var(--2daea9e8);
        -webkit-transition: -webkit-transform .1s;
        transition: -webkit-transform .1s;
        transition: transform .1s;
        transition: transform .1s,-webkit-transform .1s
}

.van-toast[data-v-3da1204d] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-3da1204d] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-3da1204d] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-3da1204d] {
        height: 80%
}

.redeem__container[data-v-3da1204d] {
        padding-block:0 2.30667rem}

.redeem__container[data-v-3da1204d] .dropdown {
        position: relative;
        width: 100%;
        height: 1.06667rem;
        line-height: 1.06667rem;
        background-color: #292929;
        color: #fff
}

.redeem__container[data-v-3da1204d] .van-nav-bar .van-nav-bar__content .van-nav-bar__left .van-icon,.redeem__container[data-v-3da1204d] .van-nav-bar .van-nav-bar__content .van-nav-bar__title {
        color: #151515
}

.redeem__container-hero[data-v-3da1204d] {
        display: grid;
        place-items: center;
        width: 100%;
        height: 7.5rem;
        text-align: center;
        background: -webkit-linear-gradient(#6F7381,#A9AAB5);
        background: linear-gradient(#6F7381,#A9AAB5)
}

.redeem__container-hero img[data-v-3da1204d] {
        height: 7.5rem;
        width: 10rem
}

.redeem__container-detail[data-v-3da1204d] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        gap: .4rem;
        padding-inline:.32rem}

.redeem__container-detail__description[data-v-3da1204d] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        width: 100%;
        margin-top: .26667rem;
        padding: .32rem .26667rem;
        border-radius: .13333rem;
        background: #3F3F3F
}

.redeem__container-detail__description-header[data-v-3da1204d] {
        margin-bottom: .38667rem;
        color: #fff;
        font-size: .37333rem;
        font-weight: 700;
        height: -webkit-fit-content;
        height: fit-content;
        line-height: 1.2em;
        overflow: hidden;
        text-overflow: ellipsis
}

.redeem__container-detail__description-body[data-v-3da1204d] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        margin-bottom: .35333rem
}

.redeem__container-detail__description-body__left[data-v-3da1204d] {
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        flex: 1;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        gap: .36rem
}

.redeem__container-detail__description-body__left>div[data-v-3da1204d] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.redeem__container-detail__description-body__left>div[data-v-3da1204d]:first-of-type {
        gap: .18667rem
}

.redeem__container-detail__description-body__left>div:first-of-type img[data-v-3da1204d] {
        width: .48rem;
        height: .48rem
}

.redeem__container-detail__description-body__left>div:first-of-type span[data-v-3da1204d] {
        color: #d9ac4f;
        font-size: .50667rem;
        line-height: .50667rem
}

.redeem__container-detail__description-body__left>div[data-v-3da1204d]:last-of-type {
        width: 4.8rem;
        height: .56rem;
        padding: .09333rem .26667rem;
        color: #a6a9ae;
        background: #303030
}

.redeem__container-detail__description-body__left>div:last-of-type span[data-v-3da1204d] {
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        flex: 1
}

.redeem__container-detail__description-body__left>div:last-of-type span[data-v-3da1204d]:first-of-type {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        font-size: .29333rem;
        white-space: nowrap
}

.redeem__container-detail__description-body__left>div:last-of-type span[data-v-3da1204d]:first-of-type:after {
        content: "";
        display: block;
        width: .01333rem;
        height: .24rem;
        margin-left: .85333rem;
        background: #c5cae4
}

.redeem__container-detail__description-body__left>div:last-of-type span[data-v-3da1204d]:last-of-type {
        color: #fff;
        font-size: .32rem;
        text-align: center
}

.redeem__container-detail__description-body__right[data-v-3da1204d] {
        position: relative;
        right: .24rem
}

.redeem__container-detail__description-body__right div[data-v-3da1204d]:first-of-type {
        position: absolute;
        top: 50%;
        left: 50%;
        -webkit-transform: translate(-50%,-50%);
        transform: translate(-50%,-50%);
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        color: #fae59f;
        font-size: .32rem
}

.redeem__container-detail__description-body__right .nowrap[data-v-3da1204d] {
        white-space: nowrap
}

.redeem__container-detail__description .van-divider[data-v-3da1204d] {
        margin: 0;
        border-color: #292929
}

.redeem__container-detail__description-footer[data-v-3da1204d] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        gap: .06667rem;
        margin-top: .26667rem;
        color: #a6a9ae;
        font-size: .32rem
}

.redeem__container-detail__description-footer img[data-v-3da1204d] {
        width: .42667rem;
        height: .42667rem
}

.redeem__container-detail__redeem[data-v-3da1204d] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        height: -webkit-fit-content;
        height: fit-content;
        padding: .46667rem .26667rem;
        border-radius: .26667rem;
        background: #3F3F3F
}

.redeem__container-detail__redeem.active[data-v-3da1204d] {
        overflow: hidden
}

.redeem__container-detail__redeem.active .redeem__container-detail__redeem-insufficient[data-v-3da1204d] {
        opacity: 1
}

.redeem__container-detail__redeem-content[data-v-3da1204d] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        height: .66667rem
}

.redeem__container-detail__redeem-content>span[data-v-3da1204d] {
        color: #fff;
        font-size: .37333rem
}

.redeem__container-detail__redeem-content__amount[data-v-3da1204d] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        gap: .5rem
}

.redeem__container-detail__redeem-content__amount img[data-v-3da1204d] {
        width: .66667rem;
        height: .66667rem
}

.redeem__container-detail__redeem-content__amount img[data-v-3da1204d]:active {
        -webkit-transform: scale(.9);
        transform: scale(.9)
}

.redeem__container-detail__redeem-content__amount input[data-v-3da1204d] {
        width: 3.28rem;
        height: .66667rem;
        color: #d9ac4f;
        font-size: .37333rem;
        line-height: .66667rem;
        text-align: center;
        border-radius: 9rem;
        background: #292929;
        border: none
}

.redeem__container-detail__redeem-content__amount span[data-v-3da1204d]:nth-of-type(1),.redeem__container-detail__redeem-content__amount span[data-v-3da1204d]:nth-of-type(3) {
        line-height: 1
}

.redeem__container-detail__redeem-insufficient[data-v-3da1204d] {
        margin-top: .24rem;
        opacity: 0;
        -webkit-transition: opacity .1s ease-in-out;
        transition: opacity .1s ease-in-out
}

.redeem__container-detail__redeem-insufficient p[data-v-3da1204d] {
        color: #f64646
}

.redeem__container-detail__redeem-insufficient div[data-v-3da1204d] {
        font-size: .29333rem
}

.redeem__container-detail__redeem-insufficient div[data-v-3da1204d]:first-of-type {
        color: #fae59f
}

.redeem__container-detail__redeem-insufficient div[data-v-3da1204d]:last-of-type {
        color: #d9ac4f;
        text-align: end
}

.redeem__container-detail__redeem-insufficient div:last-of-type i[data-v-3da1204d]:last-of-type {
        left: -.10667rem
}

.redeem__container-detail__notes[data-v-3da1204d] {
        height: -webkit-fit-content;
        height: fit-content;
        padding: .37333rem .32rem;
        border-radius: .26667rem;
        background: #3F3F3F
}

.redeem__container-detail__notes h1[data-v-3da1204d] {
        color: #fff;
        font-size: .4rem
}

.redeem__container-detail__notes li[data-v-3da1204d] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        gap: .11333rem;
        height: .4rem;
        margin-top: .30667rem;
        color: #a6a9ae;
        font-size: .32rem;
        list-style: none
}

.redeem__container-detail__notes li[data-v-3da1204d]:before {
        content: "";
        display: inline-block;
        width: .10667rem;
        height: .10667rem;
        border-radius: .02667rem;
        background: #D9AC4F;
        -webkit-transform: rotate(45deg);
        transform: rotate(45deg)
}

.redeem__container-button[data-v-3da1204d] {
        position: fixed;
        bottom: 0;
        left: 50%;
        -webkit-transform: translateX(-50%);
        transform: translate(-50%);
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        width: 100%;
        max-width: 10rem;
        height: 1.6rem;
        z-index: 3000
}

.redeem__container-button__left[data-v-3da1204d] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        width: 4.4rem;
        height: 100%;
        padding: .26667rem .32rem;
        color: #d9ac4f;
        font-size: .32rem;
        background: #404040
}

.redeem__container-button__left>div[data-v-3da1204d]:last-of-type {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        gap: .06667rem;
        color: #d9ac4f;
        margin-top: .21333rem
}

.redeem__container-button__left>div:last-of-type img[data-v-3da1204d] {
        width: .42667rem;
        height: .42667rem
}

.redeem__container-button__left>div:last-of-type>span[data-v-3da1204d] {
        font-size: .34667rem
}

.redeem__container-button__right[data-v-3da1204d],.redeem__container-button__cancel[data-v-3da1204d] {
        display: grid;
        place-items: center;
        height: 100%;
        font-size: .45333rem;
        font-weight: 600
}

.redeem__container-button__cancel[data-v-3da1204d] {
        width: 4.4rem;
        color: #a6a9ae;
        background: #292929
}

.redeem__container-button__right[data-v-3da1204d] {
        width: 5.6rem;
        color: #8f5206;
        background: #D9AC4F
}

.redeem__container-button__right span[data-v-3da1204d] {
        width: 100%;
        height: 100%;
        text-align: center;
        line-height: 1.6rem
}

.redeem__container-button__right.disabled[data-v-3da1204d] {
        color: #fff;
        background: #6F7381;
        pointer-events: none
}

.redeem__container-actionSheet[data-v-3da1204d] {
        padding-inline:.32rem;padding-bottom: 2rem
}

.redeem__container-actionSheet__note[data-v-3da1204d] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        gap: .04rem;
        margin-bottom: .37333rem;
        color: #fae59f;
        font-size: .29333rem
}

.redeem__container-actionSheet__note i[data-v-3da1204d] {
        font-size: .4rem
}

.redeem__container-actionSheet__item[data-v-3da1204d] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        gap: .26667rem;
        height: 1.46667rem;
        padding: .22667rem .13333rem;
        border-radius: .08rem;
        background: #303030
}

.redeem__container-actionSheet__item-img[data-v-3da1204d] {
        display: grid;
        place-items: center;
        width: 1.33333rem;
        height: 1rem;
        border-radius: .06667rem;
        border-radius: .13333rem;
        overflow: hidden
}

.redeem__container-actionSheet__item-img img[data-v-3da1204d] {
        width: 1.33333rem;
        height: 1rem;
        object-fit: contain
}

.redeem__container-actionSheet__item p[data-v-3da1204d] {
        color: #fff;
        font-size: .32rem
}

.redeem__container-actionSheet__cost[data-v-3da1204d] {
        margin-block:.41333rem .72rem;color: #fff;
        font-size: .48rem
}

.redeem__container-actionSheet__cost>span[data-v-3da1204d] {
        color: #d9ac4f;
        font-family: Poppins,sans-serif;
        font-weight: 600
}

.redeem__container-actionSheet__address[data-v-3da1204d] {
        padding: .26667rem .18667rem;
        border-radius: .13333rem;
        background: #303030;
        color: #fff;
        margin-bottom: .26667rem
}

.redeem__container-actionSheet__address-header[data-v-3da1204d] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.redeem__container-actionSheet__address-header i[data-v-3da1204d] {
        margin-right: .24rem;
        color: #d9ac4f;
        font-size: .64rem
}

.redeem__container-actionSheet__address-header>span[data-v-3da1204d] {
        font-size: .4rem
}

.redeem__container-actionSheet__address-header>div[data-v-3da1204d] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        gap: .08rem;
        width: 2.4rem;
        height: .8rem;
        margin-left: auto;
        padding: .2rem 0;
        color: #d9ac4f;
        font-size: .32rem;
        border: .01333rem solid #D9AC4F;
        border-radius: 9rem
}

.redeem__container-actionSheet__address-header>div img[data-v-3da1204d] {
        width: .48rem
}

.redeem__container-actionSheet__address .van-divider[data-v-3da1204d] {
        color: #d9d9d9
}

.redeem__container-actionSheet__address-default[data-v-3da1204d] {
        color: #333
}

.redeem__container-actionSheet__address-default>div[data-v-3da1204d] {
        font-size: .4rem;
        margin-bottom: .5rem;
        color: #fff
}

.redeem__container-actionSheet__address-default p[data-v-3da1204d] {
        color: #888;
        font-size: .34667rem
}

.redeem__container-actionSheet__address-empty[data-v-3da1204d] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        font-size: .37333rem;
        color: #b8bcd0
}

.addAddress__container-forum__item[data-v-3da1204d] {
        margin-bottom: .69333rem
}

.addAddress__container-forum__item h1[data-v-3da1204d] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        gap: .2rem;
        font-size: .42667rem
}

.addAddress__container-forum__item h1 i[data-v-3da1204d] {
        color: #d9ac4f;
        font-size: .64rem
}

.addAddress__container-forum__item .input_model[data-v-3da1204d] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: end;
        -webkit-align-items: flex-end;
        align-items: flex-end
}

.addAddress__container-forum__item .input_model>div[data-v-3da1204d] {
        width: 2.66667rem;
        border-radius: .13333rem;
        height: 1.06667rem;
        margin-right: .21333rem;
        background-color: #292929
}

.addAddress__container-forum__item .input_model input[data-v-3da1204d] {
        width: 100%;
        height: 1.06667rem;
        margin-top: .26667rem;
        padding-inline:.26667rem;font-size: .37333rem;
        border: none;
        border-radius: .13333rem;
        background: #292929;
        color: #a6a9ae
}

.addAddress__container-forum__item .input_model input[data-v-3da1204d]::-webkit-input-placeholder {
        color: #a6a9ae
}

.addAddress__container-forum__item .input_model input[data-v-3da1204d]::placeholder {
        color: #a6a9ae
}

.addAddress__container-forum__item textarea[data-v-3da1204d] {
        width: 100%;
        height: 3.73333rem;
        margin-top: .37333rem;
        padding: .30667rem .26667rem;
        font-size: .37333rem;
        outline: none;
        border: none;
        border-radius: .13333rem;
        background: #292929;
        resize: none
}

.addAddress__container-forum__item textarea[data-v-3da1204d]:focus {
        outline: .01333rem solid #757575
}

.addAddress__container-forum__item textarea[data-v-3da1204d]::-webkit-input-placeholder {
        color: #757575
}

.addAddress__container-forum__item textarea[data-v-3da1204d]::placeholder {
        color: #757575
}

.addAddress__container-forum__item[data-v-3da1204d]:last-child {
        margin-bottom: .13333rem
}

[data-v-3da1204d] .van-action-sheet__header {
        color: #fff
}

.van-toast[data-v-91e59acf] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-91e59acf] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-91e59acf] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-91e59acf] {
        height: 80%
}

.pointMall-rule__container[data-v-91e59acf] {
        font-family: bahnschrift
}

.pointMall-rule__container[data-v-91e59acf] .van-nav-bar {
        background-color: #f7f8ff
}

.pointMall-rule__container[data-v-91e59acf] .van-nav-bar .van-nav-bar__content .van-nav-bar__left .van-icon,.pointMall-rule__container[data-v-91e59acf] .van-nav-bar .van-nav-bar__content .van-nav-bar__title {
        color: #151515
}

.pointMall-rule__container-claimRule__title[data-v-91e59acf],.pointMall-rule__container-pointRule__title[data-v-91e59acf] {
        height: .93333rem;
        padding: .21333rem .26667rem;
        color: #8f5206;
        font-size: .37333rem;
        font-weight: 400;
        line-height: .50667rem;
        background: -webkit-linear-gradient(#fae59f,#c4933f);
        background: linear-gradient(#fae59f,#c4933f);
        -webkit-clip-path: polygon(0 0,65% 0%,60% 100%,0% 100%);
        clip-path: polygon(0 0,65% 0%,60% 100%,0% 100%)
}

.pointMall-rule__container-claimRule__body[data-v-91e59acf],.pointMall-rule__container-pointRule__body[data-v-91e59acf] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        gap: .4rem;
        padding: .4rem .29333rem .30667rem;
        border-radius: .13333rem;
        background: #292929
}

.pointMall-rule__container-claimRule__body>div[data-v-91e59acf]:first-of-type,.pointMall-rule__container-pointRule__body>div[data-v-91e59acf]:first-of-type {
        color: #a6a9ae;
        font-size: .34667rem
}

.pointMall-rule__container-claimRule__body>div[data-v-91e59acf]:last-of-type,.pointMall-rule__container-pointRule__body>div[data-v-91e59acf]:last-of-type {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        gap: .18667rem;
        color: #d9ac4f;
        font-size: .4rem
}

.pointMall-rule__container-claimRule__body>div:last-of-type i[data-v-91e59acf],.pointMall-rule__container-pointRule__body>div:last-of-type i[data-v-91e59acf] {
        font-size: .42667rem;
        -webkit-transform: rotate(90deg);
        transform: rotate(90deg)
}

.pointMall-rule__container-claimRule[data-v-91e59acf] {
        margin-bottom: 1rem
}

.pointMall-rule__container-claimRule__body div[data-v-91e59acf]:nth-of-type(2) {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: start;
        -webkit-align-items: flex-start;
        align-items: flex-start;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        height: 1.86667rem;
        padding: .45333rem 0 .45333rem 9rem;
        color: #fff;
        font-size: .42667rem;
        border-radius: .13333rem;
        background: url(/assets/png/claimRuleBg-98d00852.png) no-repeat center;
        background-color: #f95959;
        background-position-x: .13333rem;
        background-size: cover
}

.pointMall-rule__container-pointRule__body>div[data-v-91e59acf]:nth-of-type(2) {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        width: 100%;
        min-height: 2.26667rem;
        border-radius: .13333rem;
        overflow: hidden
}

.pointMall-rule__container-pointRule__body>div:nth-of-type(2)>div[data-v-91e59acf] {
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        flex: 1;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        color: #fff;
        background: #404040
}

.pointMall-rule__container-pointRule__body>div:nth-of-type(2)>div p[data-v-91e59acf],.pointMall-rule__container-pointRule__body>div:nth-of-type(2)>div li[data-v-91e59acf] {
        width: 100%;
        font-size: .37333rem;
        text-align: center
}

.pointMall-rule__container-pointRule__body>div:nth-of-type(2)>div p[data-v-91e59acf] {
        height: .93333rem;
        line-height: .93333rem;
        background: #d9ac4f;
        color: #8f5206
}

.pointMall-rule__container-pointRule__body>div:nth-of-type(2)>div li[data-v-91e59acf] {
        height: .81333rem;
        line-height: .81333rem;
        list-style: none;
        border-bottom: .01333rem solid #292929;
        background-color: #404040
}

.pointMall-rule__container-pointRule__body>div:nth-of-type(2)>div li[data-v-91e59acf]:last-of-type {
        border-bottom: none
}

.van-toast[data-v-0c9e9618] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-0c9e9618] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-0c9e9618] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-0c9e9618] {
        height: 80%
}

.turntable-detail-wrap[data-v-0c9e9618] {
        padding: .4rem .26667rem
}

.turntable-detail-hero[data-v-0c9e9618] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        margin: 0 auto .53333rem;
        color: #666;
        border-radius: .13333rem;
        overflow: hidden
}

.turntable-detail-hero__wrapper[data-v-0c9e9618] {
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        flex: 1;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-pack: start;
        -webkit-justify-content: flex-start;
        justify-content: flex-start;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.turntable-detail-hero__wrapper-titlebox[data-v-0c9e9618] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        flex: 1;
        width: 100%
}

.turntable-detail-hero__wrapper ul[data-v-0c9e9618] {
        width: 100%
}

.turntable-detail-hero__wrapper ul li[data-v-0c9e9618] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        width: 100%;
        border-bottom: .01333rem solid #292929
}

.turntable-detail-hero__wrapper ul li div[data-v-0c9e9618] {
        height: 100%;
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        flex: 1;
        font-size: .37333rem;
        padding: .34667rem 0;
        text-align: center
}

.turntable-detail-hero__wrapper ul .targetAmount[data-v-0c9e9618] {
        color: #d9ac4f;
        font-family: Inter;
        font-size: .34667rem;
        font-style: normal;
        font-weight: 500;
        line-height: .34667rem;
        margin-bottom: .08rem
}

.turntable-detail-hero__wrapper ul p[data-v-0c9e9618] {
        color: #a6a9ae;
        font-size: .29333rem;
        font-style: normal;
        font-weight: 400;
        line-height: .29333rem
}

.turntable-detail-hero__wrapper ul .rotateNum[data-v-0c9e9618] {
        color: #fff;
        font-size: .34667rem;
        font-style: normal;
        font-weight: 500;
        line-height: .34667rem
}

.turntable-detail-hero__wrapper-title[data-v-0c9e9618] {
        width: 100%;
        height: 1.06667rem;
        padding-block:.34667rem;font-size: .42667rem;
        line-height: .32rem;
        text-align: center;
        color: #fff;
        background: -webkit-linear-gradient(bottom,#C4933F 0%,#FAE59F 100%);
        background: linear-gradient(0deg,#C4933F 0%,#FAE59F 100%)
}

.turntable-detail-hero__wrapper-content[data-v-0c9e9618] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        width: 100%;
        height: 100%
}

.turntable-detail-hero__wrapper-content>div[data-v-0c9e9618] {
        width: 100%;
        padding: .34667rem 0;
        font-size: .37333rem;
        line-height: .37333rem;
        text-align: center;
        border-bottom: .01333rem solid #ff8180
}

.turntable-detail-hero__wrapper-content>div[data-v-0c9e9618]:last-of-type {
        border-bottom: none
}

.turntable-detail-tips[data-v-0c9e9618] {
        border-radius: .13333rem;
        border: .01333rem solid #707070;
        min-height: 2.93333rem;
        padding: .26667rem;
        color: #a6a9ae;
        font-size: .34667rem
}

.turntable-detail[data-v-0c9e9618] .rule span {
        color: #d9ac4f
}

.van-toast[data-v-bd4a7e01] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-bd4a7e01] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-bd4a7e01] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-bd4a7e01] {
        height: 80%
}

.turntable-pointRule[data-v-bd4a7e01] {
        color: #fff
}

.turntable-pointRule-wrap[data-v-bd4a7e01] {
        padding: .4rem .26667rem
}

.turntable-pointRule__title[data-v-bd4a7e01] {
        height: .93333rem;
        padding: .21333rem .26667rem;
        color: #fff;
        font-size: .42667rem;
        line-height: .50667rem;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        background: -webkit-linear-gradient(right,#FAE59F 7.9%,#C4933F 100%);
        background: linear-gradient(270deg,#FAE59F 7.9%,#C4933F 100%);
        -webkit-clip-path: polygon(0 0,100% 0%,96% 100%,0% 100%);
        clip-path: polygon(0 0,100% 0%,96% 100%,0% 100%)
}

.turntable-pointRule__title svg[data-v-bd4a7e01] {
        width: .45333rem;
        height: .45333rem;
        margin-right: .26667rem
}

.turntable-pointRule__body[data-v-bd4a7e01] {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        gap: .13333rem;
        border-radius: .13333rem;
        margin-bottom: .2rem
}

.turntable-pointRule__body p[data-v-bd4a7e01] {
        padding: .26667rem
}

.turntable-pointRule__body p[data-v-bd4a7e01] span {
        color: #d9ac4f
}

.turntable-tips[data-v-bd4a7e01] {
        margin: .53333rem 0;
        border: .01333rem solid #dadfe7;
        color: #d9ac4f;
        font-family: Inter;
        font-size: .34667rem;
        font-style: normal;
        font-weight: 400;
        line-height: .53333rem;
        border-radius: .26667rem;
        padding: .37333rem
}

.van-toast[data-v-a1fa8920] {
        word-break: break-word!important
}

.cg-default.van-button[data-v-a1fa8920] {
        box-shadow: 0 .05333rem #e04846;
        font-weight: 700;
        font-size: .4rem
}

.cg-default.van-button .van-button__icon[data-v-a1fa8920] {
        height: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center
}

.cg-default.van-button .van-button__icon>img[data-v-a1fa8920] {
        height: 80%
}

.turntable-introduce[data-v-a1fa8920] {
        padding: .4rem .26667rem
}

.turntable-introduce .promotion-box[data-v-a1fa8920] {
        position: relative;
        padding: .57333rem .24rem .33333rem;
        border: .01333rem solid #6F6F6F;
        border-top-left-radius: .26667rem;
        border-top-right-radius: .26667rem;
        background: #3F3F3F;
        margin-bottom: .66667rem
}

.turntable-introduce .promotion-box__borderTopStyle[data-v-a1fa8920] {
        position: absolute;
        top: 0;
        left: 50%;
        -webkit-transform: translateX(-50%);
        transform: translate(-50%);
        width: 100%
}

.turntable-introduce .promotion-box__borderTopStyle span[data-v-a1fa8920] {
        position: absolute;
        top: 0
}

.turntable-introduce .promotion-box__borderTopStyle span[data-v-a1fa8920]:first-of-type,.turntable-introduce .promotion-box__borderTopStyle span[data-v-a1fa8920]:last-of-type {
        width: .68rem;
        height: .68rem
}

.turntable-introduce .promotion-box__borderTopStyle span[data-v-a1fa8920]:first-of-type:after,.turntable-introduce .promotion-box__borderTopStyle span[data-v-a1fa8920]:last-of-type:after {
        content: "";
        position: absolute;
        top: .29333rem;
        width: .13333rem;
        height: .13333rem;
        border-radius: 50%;
        background-color: #6f6f6f
}

.turntable-introduce .promotion-box__borderTopStyle span[data-v-a1fa8920]:first-of-type {
        left: -.01333rem;
        border-top-left-radius: .26667rem;
        border-top: .06667rem solid #6F6F6F;
        border-left: .06667rem solid #6F6F6F
}

.turntable-introduce .promotion-box__borderTopStyle span[data-v-a1fa8920]:first-of-type:after {
        left: .2rem
}

.turntable-introduce .promotion-box__borderTopStyle span[data-v-a1fa8920]:last-of-type {
        right: 0;
        border-top-right-radius: .26667rem;
        border-top: .06667rem solid #6F6F6F;
        border-right: .06667rem solid #6F6F6F
}

.turntable-introduce .promotion-box__borderTopStyle span[data-v-a1fa8920]:last-of-type:after {
        right: .2rem
}

.turntable-introduce .promotion-box__titleLeft[data-v-a1fa8920],.turntable-introduce .promotion-box__titleRight[data-v-a1fa8920] {
        position: absolute;
        top: -.26667rem;
        width: .26667rem;
        height: .53333rem;
        background-color: #6f6f6f;
        -webkit-clip-path: polygon(50% 0%,100% 0%,50% 50%,100% 100%,50% 100%,0% 50%);
        clip-path: polygon(50% 0%,100% 0%,50% 50%,100% 100%,50% 100%,0% 50%);
        z-index: 5
}

.turntable-introduce .promotion-box__titleLeft[data-v-a1fa8920] {
        left: calc(50% - 1.2rem);
        -webkit-transform: translateX(-50%);
        transform: translate(-50%)
}

.turntable-introduce .promotion-box__titleRight[data-v-a1fa8920] {
        left: calc(50% + 1.2rem);
        -webkit-transform: translateX(-50%) rotate(180deg);
        transform: translate(-50%) rotate(180deg)
}

.turntable-introduce .promotion-box .promotion-title[data-v-a1fa8920] {
        position: absolute;
        top: -.26667rem;
        left: 50%;
        -webkit-transform: translateX(-50%);
        transform: translate(-50%);
        width: 2.13333rem;
        height: .53333rem;
        color: #fff;
        font-size: .32rem;
        text-align: center;
        line-height: .53333rem;
        background-color: #6f6f6f;
        -webkit-clip-path: polygon(7% 0%,93% 0%,100% 50%,93% 100%,7% 100%,0% 50%);
        clip-path: polygon(7% 0%,93% 0%,100% 50%,93% 100%,7% 100%,0% 50%)
}

.turntable-introduce .promotion-box .promotion-txt[data-v-a1fa8920] {
        color: #a6a9ae;
        font-size: .32rem;
        letter-spacing: .01333rem;
        line-height: .50667rem
}

.turntable-introduce .promotion-box .promotion-txt h3[data-v-a1fa8920] {
        color: #fff
}

.turntable-introduce .promotion-box[data-v-a1fa8920]:nth-of-type(6) {
        margin-bottom: 0;
        border-bottom: none
}

.turntable-introduce .promotion-box:nth-of-type(6) .promotion-txt[data-v-a1fa8920] {
        padding-bottom: 1.06667rem
}
