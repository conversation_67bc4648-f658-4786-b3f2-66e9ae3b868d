<!DOCTYPE html>
<html translate="no" data-dpr="1" style="font-size: 48.24px;">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="robots" content="noindex,nofollow">
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport">
    <title>Team</title>
    <link rel="stylesheet" href="./index_files/index-96409872.css">
    <link href="/css/promotion/app.css" rel="stylesheet" />
    <link href="/css/promotion/chunk-0_1.css" rel="stylesheet" />
    <link href="/css/promotion/chunk-0_2.css" rel="stylesheet" />
    <link href="/css/promotion/chunk-0_3.css" rel="stylesheet" />
    <link href="/css/promotion/chunk-0_4.css" rel="stylesheet" />
    <link href="/css/promotion/chunk-0_5.css" rel="stylesheet" />
    <link href="/css/promotion/chunk-0_6.css" rel="stylesheet" />
    <link rel="shortcut icon" href="/images/bitbug_favicon.ico" type="image/x-icon">
    <style>
        html,
        body {
            height: 100%;
            width: 100%;
            background-color: #090909;
            padding: 0;
            margin: 0;
        }

        .details-button {
            cursor: pointer;
            color: #fff;
        }

        .details-arrow {
            display: none;
            cursor: pointer;
            color: #f3c300;
        }

        .details-show .details-arrow {
            transform: rotate(180deg);
        }

        .bdshow {
            background-color: #3F3F3F !important;
            border: none !important;
            padding: 15px;
            margin-top: 8px;
            border-radius: 0 !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-height: 0;
            transition: max-height 2s ease-in-out, padding 5s ease-in-out;
        }

        .bdshow .c-row {
            font-weight: bold;
        }

        .bdshow div[data-v-0ff6946a=""] {
            margin-top: 6px;
        }

        .bdshow.open {
            max-height: 1000px;
            padding: 15px;
            transition: max-height 2s ease-in-out, padding 5s ease-in-out;
        }

        .c-row-between {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 400px;
            margin: 0 auto;
        }

        .ipt {
            flex: 1;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.3s;
            margin-left: 10px;
        }

        .ipt::placeholder {
            color: #999;
        }

        .ipt:focus {
            border-color: #555 !important;
        }

        .select-wrapper {
            position: relative;
            width: 200px;
            margin-right: 10px;
        }

        .select-wrapper select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            background-color: #fff;
            color: #333;
            font-size: 14px;
            cursor: pointer;
            appearance: none;
        }

        .select-wrapper::after {
            content: "\25BC";
            position: absolute;
            top: 50%;
            right: 10px;
            transform: translateY(-50%);
            pointer-events: none;
        }

        .select-wrapper:hover select {
            border-color: #999;
        }

        .select-wrapper select:focus {
            outline: none;
            border-color: red;
        }

        .select-wrapper select option {
            background-color: #fff;
            color: #333;
            font-size: 14px;
        }

        .select-wrapper select option:checked {
            background-color: red;
            color: #fff;
        }

        /* .p-r-10{
            margin-left: 10px;
            width: 200px;
        } */
    </style>
</head>

<body style="font-size: 12px;">
    <div id="app">
        <div data-v-7c8bbbf4="" class="mian">
            <div data-v-106b99c8="" data-v-7c8bbbf4="" class="navbar">
                <div data-v-106b99c8="" class="navbar-left" onclick="location.href='/home'">
                    <div data-v-a9660e98="" data-v-106b99c8="" class="c-row c-row-middle-center">
                        <i class="fa-light fa-angle-left fa-fade fa-xl center" style="color: #FFF"></i>
                    </div>
                </div>
                <div data-v-106b99c8="" class="navbar-title"> My Team </div>
                <div data-v-106b99c8="" class="navbar-right" onclick="location.href='/promotion/bonusrecord'">
                    <div data-v-7c8bbbf4="" data-v-106b99c8="" class="c-row">
                        <i class="fa-duotone fa-calendar-lines fa-fade fa-lg"
                            style="--fa-primary-color: #fff; --fa-secondary-color: #fff;"></i>
                    </div>
                </div>
            </div>
            <div data-v-0ff6946a="" class="promotion">
                <div data-v-0ff6946a="" class="tab">
                    <ul data-v-0ff6946a="" class="c-row c-row-between">
                        <li data-v-7c8bbbf4="" onclick="location.href='/promotion'">Data</li>
                        <li data-v-7c8bbbf4="" onclick="location.href='/promotion/myTeam'" class="action block-click">My
                            Team</li>
                        <li data-v-7c8bbbf4="" onclick="location.href='/promotion/promotionDes'">History</li>
                        <li data-v-7c8bbbf4="" onclick="location.href='/promotion/tutorial'">Tutorial</li>
                    </ul>
                </div>
                <div data-v-0ff6946a="" class="box">
                    <div data-v-0ff6946a="" class="tit c-row c-row-between" id="total-mem"
                        style="align-items: center; justify-content: center;">Team(0 People)</div>
                    <div data-v-0ff6946a="" class=" c-row c-row-between p-l-15 p-r-15 m-t-5">
                        <div data-v-0ff6946a="" class="p-r-10">
                            <input data-v-0ff6946a="" type="text" maxlength="5" placeholder="UID"
                                oninput="value=value.replace(/\D/g,'')" class="ipt">
                        </div>

                        <div data-v-0ff6946a="" class="select-wrapper">
                            <select id="levelSelect" class="c-tc">
                                <option value="">All Levels</option>
                                <option value="1">Level 1</option>
                                <option value="2">Level 2</option>
                                <option value="3">Level 3</option>
                                <option value="4">Level 4</option>
                                <option value="5">Level 5</option>
                                <option value="6">Level 6</option>
                                <option value="7">Level 7</option>
                                <option value="8">Level 8</option>
                                <option value="9">Level 9</option>
                                <option value="10">Level 10</option>
                            </select>
                        </div>

                        <!-- <div data-v-0ff6946a="" class="btn c-tc">Search</div> -->
                    </div>
                    <!--  <div data-v-0ff6946a="" class="c-row number c-flex-warp">
                        <div data-v-0ff6946a="" class="item">Total Bet Amount:0</div>
                        <div data-v-0ff6946a="" class="item">Total Rebate Amount:0</div>
                    </div> -->
                    <div data-v-0ff6946a="" class="table">
                        <div data-v-0ff6946a="" class="hd van-row">
                            <div data-v-0ff6946a="" class="c-tc van-col van-col--4">UID</div>
                            <div data-v-0ff6946a="" class="c-tc van-ellipsis van-col van-col--5">Nick Name</div>
                            <div data-v-0ff6946a="" class="c-tc van-ellipsis van-col van-col--6">Turnover</div>
                            <!-- <div data-v-0ff6946a="" class="c-tc van-ellipsis van-col van-col--4">Status</div> -->
                            <div data-v-0ff6946a="" class="c-tc van-ellipsis van-col van-col--4">Level</div>
                            <div data-v-0ff6946a="" class="c-tc van-ellipsis van-col van-col--4">Operate</div>
                        </div>
                        <div data-v-0ff6946a="" class="list">
                            <div data-v-0ff6946a="" role="feed" class="van-list">
                                <div id="van-list"></div>
                                <div data-v-a9660e98="" class="p-t-5 p-b-5">
                                    <div data-v-a9660e98="" class="van-empty">
                                        <div class="van-empty__image">
                                            <img src="/images/empty-image-default.png" />
                                        </div>
                                        <div class="van-list__finished-text">No More Available</div>
                                        <div class="van-list__placeholder"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!---->
                    <%- include('../nav') -%>
                </div>
            </div>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
            <script src="/js/client.js"></script>
            <script>
                $(document).on('click', '.details-button, .details-arrow', function () {
                    var item = $(this).closest('.item');
                    var bdshow = item.find('.bdshow');
                    var detailsButton = item.find('.details-button');
                    var detailsArrow = item.find('.details-arrow');
                    bdshow.toggleClass('open').toggle();
                    detailsButton.toggle();
                    detailsArrow.toggle();
                    $('.bdshow.open').not(bdshow).hide().removeClass('open');
                    $('.details-button').not(detailsButton).show();
                    $('.details-arrow').not(detailsArrow).hide();
                });

                function formateT(params) {
                    let result = (params < 10) ? "0" + params : params;
                    return result;
                }

                function timerJoin(params = '', addHours = 0) {
                    let date = '';
                    if (params) {
                        date = new Date(Number(params));
                    } else {
                        date = new Date();
                    }

                    date.setHours(date.getHours() + addHours);

                    let years = formateT(date.getFullYear());
                    let months = formateT(date.getMonth() + 1);
                    let days = formateT(date.getDate());

                    let hours = date.getHours() % 12;
                    hours = hours === 0 ? 12 : hours;
                    let ampm = date.getHours() < 12 ? "AM" : "PM";

                    let minutes = formateT(date.getMinutes());
                    let seconds = formateT(date.getSeconds());

                    return years + '-' + months + '-' + days + ' ' + hours + ':' + minutes + ':' + seconds + ' ' + ampm;
                }

                $('.nav .van-tabbar-item:eq(0) img').attr('src', '/images/home1.png');
                $('.nav .van-tabbar-item:eq(0) .name').removeClass('action');
                $('.nav .van-tabbar-item:eq(2) .name').addClass('action');
                function MemRender(datas) {
                    let html = '';
                    datas.map((data) => {
                        html += `
                        <div data-v-0ff6946a="" class="item">
                            <div data-v-0ff6946a="" class="bd van-row">
                                <div data-v-0ff6946a="" class="c-tc van-col van-col--5">${data.id_user}</div>
                                <div data-v-0ff6946a="" class="c-tc van-ellipsis van-col van-col--6">${data.name_user}</div>
                                <div data-v-0ff6946a="" class="c-tc red van-col van-col--4">${data.total_turn_over}</div>
                                <!--<div data-v-0ff6946a="" class="c-tc van-col van-col--4">
                                    <span data-v-0ff6946a="">${(data.status == 1) ? 'Open' : 'Close'}</span>
                                </div> -->
                                <div data-v-0ff6946a="" class="c-tc red van-col van-col--4">${data.user_level}</div>
                                <div data-v-0ff6946a="" class="c-tc van-col van-col--4">
                                    <span data-v-0ff6946a="" class="details-button">Details</span>
                                    <span data-v-0ff6946a="" class="details-arrow" style="display: none;">▼</span>
                                </div>
                            </div>
                            <div data-v-0ff6946a="" class="bdshow" style="display: none;">
                                <div data-v-0ff6946a="" class="c-row c-row-between">
                                    <div data-v-0ff6946a="">Personal flow: ${data.daily_turn_over}</div>
                                    <div data-v-0ff6946a="">Personal Recharge:${data.total_money}</div>
                                </div>
                                <div data-v-0ff6946a="">Registration time: ${timerJoin(data.time)}</div>
                               <div data-v-0ff6946a="" id="subordinateList">Subordinate: ${Array.isArray(data.invite_count) ? data.invite_count.map(item => `<li>${item}</li>`).join('') : 'No Subordinates'}</div>
                            </div>
                        </div>
                        `;
                    });
                    $('#van-list').html(html);

                    $('.showListButton').on('click', function () {
                        const inviteCount = $(this).data('invite-count');
                        const listHTML = Array.isArray(inviteCount) ? `<ul>${inviteCount.map(item => `<li>${item}</li>`).join('')}</ul>` : 'No Subordinates';
                        $('#subordinateList').html(`Subordinate: ${listHTML}`);
                    });

                }
                $.ajax({
                    type: "GET",
                    url: "/api/webapi/myTeam",
                    dataType: "json",
                    success: function (response) {
                        console.log('user name is :' + response.f1);
                        const f1Copy = [...response.f1];
                        f1Copy.splice(0, 4);
                        const f1Data = response.f1.map(item => {
                            item.daily_turn_over = item.daily_turn_over || 0;
                            item.invite_count = Array.isArray(item.invite_count) ? item.invite_count.join(', ') : '';
                            item.total_turn_over = item.total_turn_over || 0;
                            console.log(item.invite_count);
                            return item;
                        });
                        const totalTurnover = f1Data.reduce((total, item) => {
                            const turnover = parseFloat(item.total_turn_over);
                            return isNaN(turnover) ? total : total + turnover;
                        }, 0);

                        const totalMoneySum = response.f1.reduce((total, user) => {
                            const money = parseFloat(user.total_money) || 0;
                            return total + money;
                        }, 0);
                        $('#total-mem').text(`Total Recharge: ${totalMoneySum} | Total Turnover: ${parseFloat(totalTurnover) + parseFloat(response.myTunrOver)}`);
                        MemRender(f1Data);
                    }
                });
            </script>
            <script>

                function filterData(uid, level) {
                    $.ajax({
                        type: "GET",
                        url: "/api/webapi/myTeam",
                        dataType: "json",
                        success: function (response) {
                        console.log(response);
                            const allData = response.f1;
                            const filteredData = allData.filter(item => {
                                const uidMatch = uid === "" || item.id_user.includes(uid);
                                const levelMatch = level === "" || parseInt(item.user_level) === parseInt(level);

                                return uidMatch && levelMatch;
                            });

                            if (filteredData.length > 0) {
                                let html = '';
                                filteredData.forEach(data => {
                                    html += `
                                     <div data-v-0ff6946a="" class="item">
                                        <div data-v-0ff6946a="" class="bd van-row">
                                            <div data-v-0ff6946a="" class="c-tc van-col van-col--5">${data.id_user}</div>
                                            <div data-v-0ff6946a="" class="c-tc van-ellipsis van-col van-col--6">${data.name_user}</div>
                                            <div data-v-0ff6946a="" class="c-tc red van-col van-col--4">${data.total_turn_over}</div>
                                            <!--<div data-v-0ff6946a="" class="c-tc van-col van-col--4">
                                                <span data-v-0ff6946a="">${(data.status == 1) ? 'Open' : 'Close'}</span>
                                            </div> -->
                                            <div data-v-0ff6946a="" class="c-tc red van-col van-col--4">${data.user_level}</div>
                                            <div data-v-0ff6946a="" class="c-tc van-col van-col--4">
                                                <span data-v-0ff6946a="" class="details-button">Details</span>
                                                <span data-v-0ff6946a="" class="details-arrow" style="display: none;">▼</span>
                                            </div>
                                        </div>
                                        <div data-v-0ff6946a="" class="bdshow" style="display: none;">
                                            <div data-v-0ff6946a="" class="c-row c-row-between">
                                                <div data-v-0ff6946a="">Personal Flow: ${data.daily_turn_over}</div>
                                                <div data-v-0ff6946a="">Personal Recharge: ${data.total_money}</div>
                                            </div>
                                            <div data-v-0ff6946a="">Registration time: ${timerJoin(data.time)}</div>
                <div data-v-0ff6946a="" id="subordinateList">Subordinate: ${Array.isArray(data.invite_count) ? data.invite_count.map(item => `<li>${item}</li>`).join('') : 'No Subordinates'}</div>
                                        </div>
                                 </div>
                    `;
                                });
                                $('#van-list').html(html);

                                $('.showListButton').on('click', function () {
                                    const inviteCount = $(this).data('invite-count');
                                    const listHTML = Array.isArray(inviteCount) ? `<ul>${inviteCount.map(item => `<li>${item}</li>`).join('')}</ul>` : 'No Subordinates';
                                    $('#subordinateList').html(`Subordinate: ${listHTML}`);
                                });
                            }
                        }
                    });
                }

                $('.ipt').on('input', function () {
                    const partialUID = $(this).val().trim();
                    const selectedLevel = $('#levelSelect').val();
                    filterData(partialUID, selectedLevel);
                });

                $('#levelSelect').on('change', function () {
                    const selectedLevel = $(this).val();
                    const partialUID = $('.ipt').val().trim();
                    filterData(partialUID, selectedLevel);
                });
                filterData("", "");

            </script>
</body>

</html>