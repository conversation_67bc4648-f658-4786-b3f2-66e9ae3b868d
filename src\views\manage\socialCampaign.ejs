<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Settings</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <link rel="stylesheet" href="/plugins/fontawesome-free/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="/dist/css/adminlte.min.css">
    <link rel="stylesheet" href="/css/admin.css">
    <link href="//cdn.jsdelivr.net/npm/@sweetalert2/theme-dark@4/dark.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.6.7/axios.min.js" integrity="sha512-NQfB/bDaB8kaSXF8E77JjhHG5PM6XVRxvHzkZiwl3ddWCEPBa23T76MuWSwAJdMGJnmQqM0VeY9kFszsrBEFrQ==" crossorigin="anonymous" referrerpolicy="no-referrer">
    </script>
    <style>
       .form-group {
            margin-top: 20px;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 5px #2ecc71;
        }

       .form-group button {
            margin-top: 30px;
        }
    </style>
</head>
  <body class="hold-transition sidebar-mini">


  <%- include('nav') %>
<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Social Campaign Management</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="/admin/manager/index">Home</a></li>
                        <li class="breadcrumb-item active">Social Campaigns</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="content">
        <div class="container-fluid">


            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Active Campaigns</h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addCampaignModal">
                                    <i class="fas fa-plus"></i> Add New Campaign
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <table id="activeCampaigns" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Title</th>
                                        <th>Description</th>
                                        <th>Share URL</th>
                                        <th>Reward</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="activeCampaignsBody">
                                    <!-- Active campaigns will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
             <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Pending Social Share Submissions</h3>

                        </div>
                        <div class="card-body">
                            <table id="pendingShares" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>User</th>
                                        <th>Campaign</th>
                                        <th>Shared Link</th>
                                        <th>Submission Date</th>
                                        <th>Reward</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="pendingSharesBody">
                                    <!-- Pending shares will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- View Share Modal -->
<div class="modal fade" id="viewShareModal" tabindex="-1" role="dialog" aria-labelledby="viewShareModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewShareModalLabel">View Shared Link</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="embed-responsive embed-responsive-16by9">
                    <iframe id="sharePreview" class="embed-responsive-item" src="" allowfullscreen></iframe>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Campaign Modal -->
<div class="modal fade" id="addCampaignModal" tabindex="-1" role="dialog" aria-labelledby="addCampaignModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCampaignModalLabel">Add New Campaign</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="addCampaignForm">
                    <div class="form-group">
                        <label for="campaignTitle">Title</label>
                        <input type="text" class="form-control" id="campaignTitle" required>
                    </div>
                    <div class="form-group">
                        <label for="campaignDescription">Description</label>
                        <textarea class="form-control" id="campaignDescription" rows="3" required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="campaignShareUrl">Share URL</label>
                        <input type="url" class="form-control" id="campaignShareUrl" required>
                    </div>
                    <div class="form-group">
                        <label for="campaignReward">Reward ($)</label>
                        <input type="number" class="form-control" id="campaignReward" step="0.01" min="0.01" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="saveCampaignBtn">Save Campaign</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Campaign Modal -->
<div class="modal fade" id="editCampaignModal" tabindex="-1" role="dialog" aria-labelledby="editCampaignModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editCampaignModalLabel">Edit Campaign</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="editCampaignForm">
                    <input type="hidden" id="editCampaignId">
                    <div class="form-group">
                        <label for="editCampaignTitle">Title</label>
                        <input type="text" class="form-control" id="editCampaignTitle" required>
                    </div>
                    <div class="form-group">
                        <label for="editCampaignDescription">Description</label>
                        <textarea class="form-control" id="editCampaignDescription" rows="3" required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="editCampaignShareUrl">Share URL</label>
                        <input type="url" class="form-control" id="editCampaignShareUrl" required>
                    </div>
                    <div class="form-group">
                        <label for="editCampaignReward">Reward ($)</label>
                        <input type="number" class="form-control" id="editCampaignReward" step="0.01" min="0.01" required>
                    </div>
                    <div class="form-group">
                        <label for="editCampaignStatus">Status</label>
                        <select class="form-control" id="editCampaignStatus" required>
                            <option value="1">Active</option>
                            <option value="0">Inactive</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="updateCampaignBtn">Update Campaign</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteCampaignModal" tabindex="-1" role="dialog" aria-labelledby="deleteCampaignModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteCampaignModalLabel">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this campaign? This action cannot be undone.</p>
                <p>If the campaign has associated shares, it will be deactivated instead of deleted.</p>
                <input type="hidden" id="deleteCampaignId">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>
<script src="/plugins/jquery/jquery.min.js"></script>
    <script src="/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="/dist/js/adminlte.min.js"></script>
    <script src="/js/admin/admin.js"></script>
    <script src="//cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.js"></script>
<script>
    $(document).ready(function() {
        // Load pending shares
        loadPendingShares();

        // Load active campaigns
        loadActiveCampaigns();

        // Handle approve button click
        $(document).on('click', '.approve-btn', function() {
            const shareId = $(this).data('id');
            reviewShare(shareId, 'approve');
        });

        // Handle reject button click
        $(document).on('click', '.reject-btn', function() {
            const shareId = $(this).data('id');
            reviewShare(shareId, 'reject');
        });

        // Handle view share button click
        $(document).on('click', '.view-share-btn', function() {
            const shareLink = $(this).data('link');
            $('#sharePreview').attr('src', shareLink);
            $('#viewShareModal').modal('show');
        });

        // Handle edit campaign button click
        $(document).on('click', '.edit-campaign-btn', function() {
            const campaignId = $(this).data('id');
            loadCampaignDetails(campaignId);
        });

        // Handle delete campaign button click
        $(document).on('click', '.delete-campaign-btn', function() {
            const campaignId = $(this).data('id');
            $('#deleteCampaignId').val(campaignId);
            $('#deleteCampaignModal').modal('show');
        });

        // Handle confirm delete button click
        $('#confirmDeleteBtn').click(function() {
            const campaignId = $('#deleteCampaignId').val();
            deleteCampaign(campaignId);
        });

        // Handle update campaign button click
        $('#updateCampaignBtn').click(function() {
            updateCampaign();
        });

        // Handle save campaign button click
        $('#saveCampaignBtn').click(function() {
            const title = $('#campaignTitle').val();
            const description = $('#campaignDescription').val();
            const shareUrl = $('#campaignShareUrl').val();
            const reward = $('#campaignReward').val();

            if (!title || !description || !shareUrl || !reward) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Please fill in all fields'
                });
                return;
            }

            $.ajax({
                type: "POST",
                url: "/api/webapi/admin/social-campaigns/add",
                data: {
                    title: title,
                    description: description,
                    share_url: shareUrl,
                    reward: reward
                },
                dataType: "json",
                success: function(response) {
                    if (response.status) {
                        $('#addCampaignModal').modal('hide');
                        $('#addCampaignForm')[0].reset();
                        loadActiveCampaigns();
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            text: 'Campaign added successfully'
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: response.message
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Failed to add campaign'
                    });
                }
            });
        });
    });

    function loadPendingShares() {
        $.ajax({
            type: "GET",
            url: "/api/webapi/admin/social-shares/pending",
            dataType: "json",
            success: function(response) {
                if (response.status) {
                    renderPendingShares(response.pendingShares);
                }
            }
        });
    }

    function renderPendingShares(shares) {
        let html = '';

        if (shares.length === 0) {
            html = '<tr><td colspan="7" class="text-center">No pending shares</td></tr>';
        } else {
            shares.forEach(share => {
                const date = new Date(parseInt(share.created_at));
                html += `
                    <tr>
                        <td>${share.id}</td>
                        <td>${share.name_user} (${share.phone})</td>
                        <td>${share.campaign_title}</td>
                        <td>
                            <a href="${share.share_link}" target="_blank">${share.share_link.substring(0, 30)}...</a>
                            <button class="btn btn-sm btn-info view-share-btn" data-link="${share.share_link}">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                        <td>${date.toLocaleString()}</td>
                        <td>₹${share.campaign_reward}</td>
                        <td>
                            <button class="btn btn-sm btn-success approve-btn" data-id="${share.id}">
                                <i class="fas fa-check"></i> Approve
                            </button>
                            <button class="btn btn-sm btn-danger reject-btn" data-id="${share.id}">
                                <i class="fas fa-times"></i> Reject
                            </button>
                        </td>
                    </tr>
                `;
            });
        }

        $('#pendingSharesBody').html(html);
    }

    function loadActiveCampaigns() {
        $.ajax({
            type: "GET",
            url: "/api/webapi/social-campaigns",
            dataType: "json",
            success: function(response) {
                if (response.status) {
                    renderActiveCampaigns(response.campaigns);
                }
            }
        });
    }

    function renderActiveCampaigns(campaigns) {
        let html = '';

        if (campaigns.length === 0) {
            html = '<tr><td colspan="7" class="text-center">No active campaigns</td></tr>';
        } else {
            campaigns.forEach(campaign => {
                const status = campaign.status === 1 ?
                    '<span class="badge badge-success">Active</span>' :
                    '<span class="badge badge-danger">Inactive</span>';

                html += `
                    <tr>
                        <td>${campaign.id}</td>
                        <td>${campaign.title}</td>
                        <td>${campaign.description.substring(0, 50)}...</td>
                        <td>${campaign.share_url}</td>
                        <td>₹${campaign.reward}</td>
                        <td>${status}</td>
                        <td>
                            <button class="btn btn-sm btn-primary edit-campaign-btn" data-id="${campaign.id}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger delete-campaign-btn" data-id="${campaign.id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });
        }

        $('#activeCampaignsBody').html(html);
    }

    function loadCampaignDetails(campaignId) {
        $.ajax({
            type: "GET",
            url: `/api/webapi/admin/social-campaigns/${campaignId}`,
            dataType: "json",
            success: function(response) {
                if (response.status) {
                    const campaign = response.campaign;

                    // Populate the edit form
                    $('#editCampaignId').val(campaign.id);
                    $('#editCampaignTitle').val(campaign.title);
                    $('#editCampaignDescription').val(campaign.description);
                    $('#editCampaignShareUrl').val(campaign.share_url);
                    $('#editCampaignReward').val(campaign.reward);
                    $('#editCampaignStatus').val(campaign.status);

                    // Show the edit modal
                    $('#editCampaignModal').modal('show');
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: response.message
                    });
                }
            },
            error: function() {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to load campaign details'
                });
            }
        });
    }

    function updateCampaign() {
        const campaignId = $('#editCampaignId').val();
        const title = $('#editCampaignTitle').val();
        const description = $('#editCampaignDescription').val();
        const shareUrl = $('#editCampaignShareUrl').val();
        const reward = $('#editCampaignReward').val();
        const status = $('#editCampaignStatus').val();

        if (!campaignId || !title || !description || !shareUrl || !reward || status === undefined) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Please fill in all fields'
            });
            return;
        }

        $.ajax({
            type: "POST",
            url: "/api/webapi/admin/social-campaigns/update",
            data: {
                campaign_id: campaignId,
                title: title,
                description: description,
                share_url: shareUrl,
                reward: reward,
                status: status
            },
            dataType: "json",
            success: function(response) {
                if (response.status) {
                    $('#editCampaignModal').modal('hide');
                    loadActiveCampaigns();
                    Swal.fire({
                        icon: 'success',
                        title: 'Success',
                        text: 'Campaign updated successfully'
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: response.message
                    });
                }
            },
            error: function() {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to update campaign'
                });
            }
        });
    }

    function deleteCampaign(campaignId) {
        $.ajax({
            type: "POST",
            url: "/api/webapi/admin/social-campaigns/delete",
            data: {
                campaign_id: campaignId
            },
            dataType: "json",
            success: function(response) {
                $('#deleteCampaignModal').modal('hide');

                if (response.status) {
                    loadActiveCampaigns();
                    Swal.fire({
                        icon: 'success',
                        title: 'Success',
                        text: response.message
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: response.message
                    });
                }
            },
            error: function() {
                $('#deleteCampaignModal').modal('hide');
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to delete campaign'
                });
            }
        });
    }

    function reviewShare(shareId, action) {
        $.ajax({
            type: "POST",
            url: "/api/webapi/admin/social-shares/review",
            data: {
                share_id: shareId,
                action: action
            },
            dataType: "json",
            success: function(response) {
                if (response.status) {
                    loadPendingShares();
                    Swal.fire({
                        icon: 'success',
                        title: 'Success',
                        text: response.message
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: response.message
                    });
                }
            },
            error: function() {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to review share'
                });
            }
        });
    }
</script>
</body>

</html>