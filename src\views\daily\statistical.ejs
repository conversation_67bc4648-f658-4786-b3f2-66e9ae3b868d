<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Statistics</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <link rel="stylesheet" href="/plugins/fontawesome-free/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
    <link rel="stylesheet" href="/dist/css/adminlte.min.css">
    <link href="//cdn.jsdelivr.net/npm/@sweetalert2/theme-dark@4/dark.css" rel="stylesheet">
    <link rel="stylesheet" href="/css/admin.css">
</head>

<body class="dark-mode sidebar-mini layout-fixed layout-navbar-fixed layout-footer-fixed">
    <div class="wrapper">
        <%- include('nav') %>

            <!-- Content Wrapper. Contains page content -->
            <div class="content-wrapper">
                <!-- Content Header (Page header) -->
                <section class="content-header">
                    <div class="container-fluid">
                        <div class="row mb-2">
                            <div class="col-sm-6">
                                <h1>Statistical </h1>
                            </div>
                        </div>
                    </div>
                    <!-- /.container-fluid -->
                </section>

                <!-- Main content -->
                <section class="content">
                    <div class="container-fluid">
                        <!-- <h5 class="mb-2">Info Box</h5> -->
                        <div class="row">
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box">
                                    <span class="info-box-icon bg-info"><i class="fa fa-user-circle" aria-hidden="true"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total number of members</span>
                                        <span class="info-box-number totalMember">0</span>
                                    </div>
                                    <!-- /.info-box-content -->
                                </div>
                                <!-- /.info-box -->
                             </div>
                            <!-- /.col -->
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box">
                                    <span class="info-box-icon bg-success"><i class="far fa-flag"></i></span>

                                    <div class="info-box-content">
                                        <span class="info-box-text">Win today</span>
                                        <span class="info-box-number totalWin">0</span>
                                    </div>
                                    <!-- /.info-box-content -->
                                </div>
                                <!-- /.info-box -->
                            </div>
                            <!-- /.col -->
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box">
                                    <span class="info-box-icon bg-danger"><i class="far fa-flag"></i></span>

                                    <div class="info-box-content">
                                        <span class="info-box-text">Lose today</span>
                                        <span class="info-box-number totalLoss">0</span>
                                    </div>
                                    <!-- /.info-box-content -->
                                </div>
                                <!-- /.info-box -->
                            </div>
                            <!-- /.col -->
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box">
                                    <span class="info-box-icon bg-warning"><i class="fa fa-user-times" aria-hidden="true"></i></span>

                                    <div class="info-box-content">
                                        <span class="info-box-text">Member is locked</span>
                                        <span class="info-box-number totalMemberFail">0</span>
                                    </div>
                                    <!-- /.info-box-content -->
                                </div>
                                <!-- /.info-box -->
                            </div>
                            <!-- /.col -->
                        </div>
                        <!-- /.row -->

                        <!-- =========================================================== -->
                        <!-- <h5 class="mb-2">Info Box</h5> -->
                        <div class="row">
                            <!-- /.col -->
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box shadow-sm">
                                    <span class="info-box-icon bg-success"><i class="fa fa-check" aria-hidden="true"></i></span>

                                    <div class="info-box-content">
                                        <span class="info-box-text">Recharge successful</span>
                                        <span class="info-box-number totalRecharge">0</span>
                                    </div>
                                    <!-- /.info-box-content -->
                                </div>
                                <!-- /.info-box -->
                            </div>
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box shadow-sm">
                                    <span class="info-box-icon bg-success"><i class="fa fa-check" aria-hidden="true"></i></span>

                                    <div class="info-box-content">
                                        <span class="info-box-text">Recharge successfully today</span>
                                        <span class="info-box-number totalRechargeToday">0</span>
                                    </div>
                                    <!-- /.info-box-content -->
                                </div>
                                <!-- /.info-box -->
                            </div>
                            <!-- /.col -->
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box shadow">
                                    <span class="info-box-icon bg-warning"><i class="fa fa-university" aria-hidden="true"></i></span>

                                    <div class="info-box-content">
                                        <span class="info-box-text">Withdrawal successful</span>
                                        <span class="info-box-number totalwithdraw">0</span>
                                    </div>
                                    <!-- /.info-box-content -->
                                </div>
                                <!-- /.info-box -->
                            </div>
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box shadow">
                                    <span class="info-box-icon bg-warning"><i class="fa fa-university" aria-hidden="true"></i></span>

                                    <div class="info-box-content">
                                        <span class="info-box-text">Withdraw money today</span>
                                        <span class="info-box-number totalwithdrawToday">0</span>
                                    </div>
                                    <!-- /.info-box-content -->
                                </div>
                                <!-- /.info-box -->
                            </div>
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box shadow">
                                    <span class="info-box-icon bg-info"><i class="fa fa-line-chart" aria-hidden="true"></i></span>

                                    <div class="info-box-content">
                                        <span class="info-box-text">Total commission</span>
                                        <span class="info-box-number totalRoses">0</span>
                                    </div>
                                    <!-- /.info-box-content -->
                                </div>
                                <!-- /.info-box -->
                            </div>
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box shadow">
                                    <span class="info-box-icon bg-info"><i class="fa fa-line-chart" aria-hidden="true"></i></span>

                                    <div class="info-box-content">
                                        <span class="info-box-text">Roses today</span>
                                        <span class="info-box-number totalRosesToday">0</span>
                                    </div>
                                    <!-- /.info-box-content -->
                                </div>
                                <!-- /.info-box -->
                            </div>
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box shadow">
                                    <span class="info-box-icon bg-info"><i class="fa fa-line-chart" aria-hidden="true"></i></span>

                                    <div class="info-box-content">
                                        <span class="info-box-text">Rose F1</span>
                                        <span class="info-box-number totalRosesF1">0</span>
                                    </div>
                                    <!-- /.info-box-content -->
                                </div>
                                <!-- /.info-box -->
                            </div>
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box shadow">
                                    <span class="info-box-icon bg-info"><i class="fa fa-line-chart" aria-hidden="true"></i></span>

                                    <div class="info-box-content">
                                        <span class="info-box-text">Team Roses</span>
                                        <span class="info-box-number totalRosesF">0</span>
                                    </div>
                                    <!-- /.info-box-content -->
                                </div>
                                <!-- /.info-box -->
                            </div>
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box">
                                    <span class="info-box-icon bg-info"><i class="fa fa-money" aria-hidden="true"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Balance used to create giftcode</span>
                                        <span class="info-box-number moneyCTV">0</span>
                                    </div>
                                    <!-- /.info-box-content -->
                                </div>
                                <!-- /.info-box -->
                             </div>
                        </div>
                    </div>
                    <!-- /.container-fluid -->
                </section>
                <!-- /.content -->

                <!-- Time filter -->
                <section class="content">
                    <!-- Default box -->
                    <div class="card">
                        <div class="card-header text-center">
                            <div class="text-center">
                                <input type="date" id="sort-date">
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- New member -->
                <section class="content">
                    <!-- Default box -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">List of new members &nbsp</h3> <span id="ip_address"></span>
                            <div class="card-tools">
                                <button type="button" class="btn btn-tool" data-card-widget="collapse" title="Collapse">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <button type="button" class="btn btn-tool" data-card-widget="remove" title="Remove">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div> 
                        </div>
                        <div class="card-body p-0" style="display: block; overflow-y: hidden;">
                            <table class="table table-striped projects" id="table1">
                                <thead>
                                    <tr>
                                        <th class="text-center">ID</th>
                                        <th class="text-center">Phone</th>
                                        <th class="text-center">Invite</th>
                                        <th class="text-center">Amount</th>
                                        <th class="text-center">Loaded</th>
                                        <th class="text-center">IP</th>
                                        <th class="text-center">Time</th>
                                    </tr>
                                </thead>
                                <tbody id="list-mem-news">
                                    
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>

                <!-- Recharge today -->
                <section class="content">
                    <!-- Default box -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Recharge today</h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-tool" data-card-widget="collapse" title="Collapse">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <button type="button" class="btn btn-tool" data-card-widget="remove" title="Remove">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-0" style="display: block; overflow-y: hidden;">
                            <table class="table table-striped projects" id="table1">
                                <thead>
                                    <tr>
                                        <th class="text-center">ID</th>
                                        <th class="text-center">Phone</th>
                                        <th class="text-center">Amount</th>
                                        <th class="text-center">Status</th>
                                        <th class="text-center">Time</th>
                                    </tr>
                                </thead>
                                <tbody id="list-recharge-news">
                                    
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>

                <!-- Withdraw money today -->
                <section class="content">
                    <!-- Default box -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Withdraw money today</h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-tool" data-card-widget="collapse" title="Collapse">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <button type="button" class="btn btn-tool" data-card-widget="remove" title="Remove">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-0" style="display: block; overflow-y: hidden;">
                            <table class="table table-striped projects" id="table1">
                                <thead>
                                    <tr>
                                        <th class="text-center">ID</th>
                                        <th class="text-center">Phone</th>
                                        <th class="text-center">Amount</th>
                                        <th class="text-center">Status</th>
                                        <th class="text-center">Time</th>
                                    </tr>
                                </thead>
                                <tbody id="list-withdraw-news">
                                    
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>

                <!-- Financial details -->
                <section class="content">
                    <!-- Default box -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Usage history Giftcode &nbsp</h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-tool" data-card-widget="collapse" title="Collapse">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <button type="button" class="btn btn-tool" data-card-widget="remove" title="Remove">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-0" style="display: block; overflow-y: hidden;">
                            <table class="table table-striped projects" id="table1">
                                <thead>
                                    <tr>
                                        <th class="text-center">ID</th>
                                        <th class="text-center">Creator</th>
                                        <th class="text-center">User</th>
                                        <th class="text-center">Amount</th>
                                        <th class="text-center">Time</th>
                                    </tr>
                                </thead>
                                <tbody id="list-details-news">
                                    
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>

                <!-- Increase or decrease the amount of the account -->
                <section class="content">
                    <!-- Default box -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Increase | member discount &nbsp</h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-tool" data-card-widget="collapse" title="Collapse">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <button type="button" class="btn btn-tool" data-card-widget="remove" title="Remove">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-0" style="display: block; overflow-y: hidden;">
                            <table class="table table-striped projects" id="table1">
                                <thead>
                                    <tr>
                                        <th class="text-center">ID</th>
                                        <th class="text-center">Increased | decrease</th>
                                        <th class="text-center">Recipient</th>
                                        <th class="text-center">Amount</th>
                                        <th class="text-center">Time</th>
                                    </tr>
                                </thead>
                                <tbody id="financial_details">
                                    
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>
                <a id="back-to-top" href="#" class="btn btn-primary back-to-top" role="button" aria-label="Scroll to top">
                    <i class="fas fa-chevron-up"></i>
                </a>
            </div>
    </div>
    <script src="/plugins/jquery/jquery.min.js"></script>
    <script src="/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="/dist/js/adminlte.min.js"></script>
    <script src="/js/admin/admin.js"></script>
    <script src="//cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.js"></script>
    <script>
        function formateT(params) {
    let result = (params < 10) ? "0" + params : params;
    return result;
    }
    
    function timerJoin(params = '', addHours = 0) {
        let date = '';
        if (params) {
            date = new Date(Number(params));
        } else {
            date = new Date();
        }
    
        date.setHours(date.getHours() + addHours);
    
        let years = formateT(date.getFullYear());
        let months = formateT(date.getMonth() + 1);
        let days = formateT(date.getDate());
    
        let hours = date.getHours() % 12;
        hours = hours === 0 ? 12 : hours;
        let ampm = date.getHours() < 12 ? "AM" : "PM";
    
        let minutes = formateT(date.getMinutes());
        let seconds = formateT(date.getSeconds());
    
        return years + '-' + months + '-' + days + ' ' + hours + ':' + minutes + ':' + seconds + ' ' + ampm;
    }
    </script>
    <script>
        const RenderMemberNews = (datas) => {
            if (datas.length == 0) {
                $('#list-mem-news').html(`
                    <tr class="text-center">
                    <td colspan="7">No more data...</td>
                    </tr>
                `);
                return;
            }
            let html = '';
            datas.map((data) => {
                html += 
                `
                <tr class="text-center" style="">
                    <td>${data.id_user}</td>
                    <td style="color: #3498db;font-weight: 600;min-width: 210px">${data.phone}</td>
                    <td style="color: #3498db;font-weight: 600;min-width: 210px">${data.phone_invite}</td>
                    <td><b style="color: #e74c3c">${formatMoney(data.total_money)}</b></td>
                    <td class="project-state"><span class="badge badge-${(data.total_money > 0 ? 'success' : 'warning')}">${(data.total_money > 0 ? 'success' : 'pending')}</span></td>
                    <td style="min-width: 110px;"><b>${data.ip_address}</b></td>
                    <td style="min-width: 110px;"><b>${timerJoin(data.time)}</b></td>
                </tr>
                `;
                $("#list-mem-news").html(html);
            });
        }

        const RenderRechargeNews = (datas) => {
            if (datas.length == 0) {
                $('#list-recharge-news').html(`
                    <tr class="text-center">
                    <td colspan="7">No more data...</td>
                    </tr>
                `);
                return;
            }
            let html = '';
            datas.map((data) => {
                html += 
                `
                <tr class="text-center" style="">
                    <td>${data.id}</td>
                    <td style="color: #3498db;font-weight: 600;min-width: 210px">${data.phone}</td>
                    <td><b style="color: #e74c3c">${formatMoney(data.money)}</b></td>
                    <td class="project-state"><span class="badge badge-success">success</span></td>
                    <td style="min-width: 110px;"><b>${timerJoin(data.time)}</b></td>
                </tr>
                `;
                $("#list-recharge-news").html(html);
            });
        }

        const RenderWithdrawNews = (datas) => {
            if (datas.length == 0) {
                $('#list-withdraw-news').html(`
                    <tr class="text-center">
                    <td colspan="7">No more data...</td>
                    </tr>
                `);
                return;
            }
            let html = '';
            datas.map((data) => {
                html += 
                `
                <tr class="text-center" style="">
                    <td>${data.id}</td>
                    <td style="color: #3498db;font-weight: 600;min-width: 210px">${data.phone}</td>
                    <td><b style="color: #e74c3c">${formatMoney(data.money)}</b></td>
                    <td class="project-state"><span class="badge badge-success">success</span></td>
                    <td style="min-width: 110px;"><b>${timerJoin(data.time)}</b></td>
                </tr>
                `;
                $("#list-withdraw-news").html(html);
            });
        }
        
        const redenvelopesUsed = (datas) => {
            if (datas.length == 0) {
                $('#list-details-news').html(`
                    <tr class="text-center">
                    <td colspan="7">No more data...</td>
                    </tr>
                `);
                return;
            }
            let html = '';
            datas.map((data) => {
                html += 
                `
                <tr class="text-center" style="">
                    <td>${data.id_redenvelops}</td>
                    <td style="color: #3498db;font-weight: 600;min-width: 210px">${data.phone}</td>
                    <td style="color: #3498db;font-weight: 600;min-width: 210px">${data.phone_used}</td>
                    <td><b style="color: #e74c3c">+${formatMoney(data.money)}</b></td>
                    <td style="min-width: 110px;"><b>${timerJoin(data.time)}</b></td>
                </tr>
                `;
                $("#list-details-news").html(html);
            });
        }

        const financial_details = (datas) => {
            if (datas.length == 0) {
                $('#financial_details').html(`
                    <tr class="text-center">
                    <td colspan="7">No more data...</td>
                    </tr>
                `);
                return;
            }
            let html = '';
            datas.map((data) => {
                html += 
                `
                <tr class="text-center" style="">
                    <td>${data.id}</td>
                    <td style="color: #3498db;font-weight: 600;min-width: 210px">${data.phone}</td>
                    <td style="color: #3498db;font-weight: 600;min-width: 210px">${data.phone_used}</td>
                    <td><b style="color: #e74c3c">${(data.type == '1') ? '+' : '-'} ${formatMoney(data.money)}</b></td>
                    <td style="min-width: 110px;"><b>${timerJoin(data.time)}</b></td>
                </tr>
                `;
                $("#financial_details").html(html);
            });
        }
    </script>
    <script>
        $('#sort-date').change(function (e) { 
            e.preventDefault();
            let timeDate = $(this).val();
            $.ajax({
                type: "POST",
                url: "/manager/infoCtv/select",
                data: {
                    timeDate: timeDate,
                }, 
                dataType: "json",
                success: function (response) {
                    RenderMemberNews(response.list_mems);
                    RenderRechargeNews(response.list_recharge_news);
                    RenderWithdrawNews(response.list_withdraw_news);
                    redenvelopesUsed(response.redenvelopes_used);
                    financial_details(response.financial_details_today);  
                }
            });
        });
        $.ajax({
            type: "POST",
            url: "/manager/infoCtv",
            data: {

            }, 
            dataType: "json",
            success: function (response) {
                RenderMemberNews(response.list_mems);
                RenderRechargeNews(response.list_recharge_news);
                RenderWithdrawNews(response.list_withdraw_news);
                redenvelopesUsed(response.redenvelopes_used);  
                financial_details(response.financial_details_today);  

                $('.totalMember').text(formatMoney(response.f1 + response.f2 + response.f3 + response.f4));
                $('.totalRoses').text(formatMoney(response.datas[0].roses_f + response.datas[0].roses_f1));
                $('.totalRosesToday').text(formatMoney(response.datas[0].roses_today));
                $('.totalRosesF1').text(formatMoney(response.datas[0].roses_f1));
                $('.totalRosesF').text(formatMoney(response.datas[0].roses_f));
                $('#ip_address').text(' - IP Address ( ' + response.datas[0].ip_address + ' )');

                $('.totalRecharge').text(formatMoney(response.total_recharge));
                $('.totalwithdraw').text(formatMoney(response.total_withdraw));
                $('.totalRechargeToday').text(formatMoney(response.total_recharge_today));
                $('.totalwithdrawToday').text(formatMoney(response.total_withdraw_today));
                $('.totalMemberFail').text(formatMoney(response.list_mem_baned));

                $('.totalWin').text(formatMoney(response.win));
                $('.totalLoss').text(formatMoney(response.loss));

                $('.moneyCTV').text(formatMoney(response.moneyCTV));
            }
        });
    </script>
</body>

</html>