<!DOCTYPE html>
<html translate="no" data-dpr="1" style="font-size: 48.24px;">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="robots" content="noindex,nofollow">
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport">
    <title>Promotion</title>
    <link href="/css/promotion/app.css" rel="stylesheet" />
    <link href="/css/promotion/chunk-0_1.css" rel="stylesheet" />
    <link href="/css/promotion/chunk-0_2.css" rel="stylesheet" />
    <link href="/css/promotion/chunk-0_3.css" rel="stylesheet" />
    <link href="/css/promotion/chunk-0_4.css" rel="stylesheet" />
    <link href="/css/promotion/chunk-0_5.css" rel="stylesheet" />
    <link href="/css/promotion/chunk-0_6.css" rel="stylesheet" />
    <link rel="shortcut icon" href="/images/bitbug_favicon.ico" type="image/x-icon">
    <style>
        html,
        body {
            height: 100%;
            width: 100%;
            background-color: #090909;
            padding: 0;
            margin: 0;
        }
    </style>
</head>

<body style="font-size: 12px;">
    <div id="app">
        <div data-v-7c8bbbf4="" class="mian">
            <div data-v-106b99c8="" data-v-7c8bbbf4="" class="navbar">
                <div data-v-106b99c8="" class="navbar-left" onclick="location.href='/home'">
                    <div data-v-a9660e98="" data-v-106b99c8="" class="c-row c-row-middle-center">
                        <i class="fa-light fa-angle-left fa-fade fa-xl center" style="color: #FFF"></i>
                    </div>
                </div>
                <div data-v-106b99c8="" class="navbar-title"> History </div>
                <div data-v-106b99c8="" class="navbar-right" onclick="location.href='/promotion/bonusrecord'">
                    <div data-v-7c8bbbf4="" data-v-106b99c8="" class="c-row">
                        <i class="fa-duotone fa-calendar-lines fa-fade fa-lg"
                            style="--fa-primary-color: #fff; --fa-secondary-color: #fff;"></i>
                    </div>
                </div>
            </div>
            <div data-v-14912091="" class="promotion">
                <div data-v-14912091="" class="tab">
                    <ul data-v-14912091="" class="c-row c-row-between">
                        <li data-v-7c8bbbf4="" onclick="location.href='/promotion'">Data</li>
                        <li data-v-7c8bbbf4="" onclick="location.href='/promotion/myTeam'">My Team</li>
                        <li data-v-7c8bbbf4="" onclick="location.href='/promotion/promotionDes'"
                            class="action block-click">History</li>
                        <li data-v-7c8bbbf4="" onclick="location.href='/promotion/tutorial'">Tutorial</li>
                    </ul>
                </div>
                <div data-v-14912091="" class="box">
                    <div data-v-14912091="" class="table">
                        <div data-v-14912091="" class="hd van-row" style="overflow: hidden;">
                            <div data-v-14912091="" class="c-tc van-ellipsis van-col van-col--6">Time</div>
                            <div data-v-14912091="" class="c-tc van-ellipsis van-col van-col--6">Phone</div>
                            <div data-v-14912091="" class="c-tc van-ellipsis van-col van-col--6">Amount</div>
                            <div data-v-14912091="" class="c-tc van-ellipsis van-col van-col--6">Invite Code</div>
                        </div>
                        <div data-v-14912091="" class="list">
                            <div data-v-14912091="" role="feed" class="van-list">
                                <div id="van-list">

                                </div>
                                <div data-v-a9660e98="" class="p-t-5 p-b-5">
                                    <div data-v-a9660e98="" class="van-empty">
                                        <div class="van-empty__image">
                                            <img src="/images/empty-image-default.png" />
                                        </div>
                                        <div class="van-list__finished-text">No More Available</div>
                                        <div class="van-list__placeholder"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!---->
                    <%- include('../nav') -%>
                </div>
            </div>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
            <script src="/js/client.js"></script>
            <script>

                function formateT(params) {
                    let result = (params < 10) ? "0" + params : params;
                    return result;
                }

                function timerJoin(params = '', addHours = 0) {
                    let date = '';
                    if (params) {
                        date = new Date(Number(params));
                    } else {
                        date = new Date();
                    }
                    let years = formateT(date.getFullYear());
                    let months = formateT(date.getMonth() + 1);
                    let days = formateT(date.getDate());

                    let hours = date.getHours() % 12;
                    hours = hours === 0 ? 12 : hours;

                    let minutes = formateT(date.getMinutes());
                    let seconds = formateT(date.getSeconds());
                    return months + '-' + days + ' ' + hours + ':' + minutes;
                }
                $('.nav .van-tabbar-item:eq(0) img').attr('src', '/images/home1.png');
                $('.nav .van-tabbar-item:eq(0) .name').removeClass('action');
                $('.nav .van-tabbar-item:eq(2) .name').addClass('action');
                function MemRender(datas) {
                    let html = '';
                    datas.map((data) => {
                        const dataF1 = parseFloat(data.f1).toFixed(2);
                        html += `
                        <div data-v-14912091="" class="bd van-row" style="overflow:hidden;">
                            <div data-v-14912091="" class="c-tc van-col van-col--6">${timerJoin(data.time)}</div>
                            <div data-v-14912091="" class="c-tc van-col van-col--6">${data.phone.slice(0, 2)}****${data.phone.slice(-4)}</div>
                            <div data-v-14912091="" class="c-tc van-col van-col--6">${dataF1}</div>
                            <div data-v-14912091="" class="c-tc van-col van-col--6">${data.code}</div>
                        </div>
                        `;
                    });
                    $('#van-list').html(html);
                    console.log(datas);
                }
                $.ajax({
                    type: "GET",
                    url: "/api/webapi/myTeam",
                    dataType: "json",
                    success: function (response) {
                        MemRender(response.total_roses);
                    }
                });

            </script>
</body>

</html>