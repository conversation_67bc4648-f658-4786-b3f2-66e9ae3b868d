<div class="van-overlay" style="z-index: 2001;display: none;"></div>
<div data-v-03b808c2="" class="van-popup van-popup--center pop-quytac" style="display: none;width: 80%; border-radius: 10px; max-width: 340px; z-index: 2002;"><div data-v-03b808c2="" class="rule-box"><div data-v-03b808c2="" class="title c-row c-row-middle-center">Rules</div><div data-v-03b808c2="" class="info"><div data-v-03b808c2="" class="comment"><div data-v-03b808c2=""><div data-v-03b808c2=""><p class="MsoNormal"><font face="宋体">In each Fast 3 lottery draw, there will be three numbers randomly drawn from 111 to 666, excluding the number 0. The drawn numbers are not in any specific order. Fast 3 is a game where you can guess all or part of the three winning numbers.</font></p><p class="MsoNormal"><font face="宋体">Total bet</font></p><p class="MsoNormal"><font face="宋体">Bet on the total sum of 3 dice</font></p><p class="MsoNormal"><font face="宋体">Bet on the entire set of 3 (numbers/dice/etc.)</font></p><p class="MsoNormal"><font face="宋体">The players place a bet on a group of identical triples, such as: (111, 222, ..., 666), covering all possible triples, and proceed with the betting</font></p><p class="MsoNormal"><font face="宋体">Bet on a single triple set</font></p><p class="MsoNormal"><font face="宋体">The player chooses one of all possible triple sets (111, ..., 666) and proceeds with the betting</font></p><p class="MsoNormal"><font face="宋体">Bet on two numbers</font></p><p class="MsoNormal"><font face="宋体">Place a bet on three numbers, including two specified pairs and one random number</font></p><p class="MsoNormal"><font face="宋体">Bet on two pairs + 1 single number</font></p><p class="MsoNormal"><font face="宋体">Place a bet on three specified numbers, including two pairs and one other number.</font></p><p class="MsoNormal"><font face="宋体">Betting on 3 different numbers</font></p><p class="MsoNormal"><font face="宋体">Place a bet on three completely different numbers</font></p><p class="MsoNormal"><font face="宋体">Bet on 2 different numbers</font></p><p class="MsoNormal"><font face="宋体">Place a bet on 2 different numbers + 1 other number combined in one betting ticket</font></p><p class="MsoNormal"><font face="宋体">Betting on 3 consecutive numbers</font></p><p class="MsoNormal"><font face="宋体">Betting is placed on the consecutive numbers [123, 234, 345, 456].<br></font><br></p></div></div><div data-v-03b808c2=""><!----></div><div data-v-03b808c2=""><!----></div><div data-v-03b808c2=""><!----></div></div><div data-v-03b808c2="" class="rule-btn c-row m-t-20 c-row-center"><button data-v-03b808c2="" class="btn van-button van-button--default van-button--normal van-button--block van-button--round" style="color: rgb(255, 255, 255); background: rgb(242, 65, 59); border-color: rgb(242, 65, 59);"><div data-v-03b808c2="" class="van-button__content"><span data-v-03b808c2="" class="van-button__text"><span data-v-03b808c2="">Rules</span></span></div></button></div></div></div></div>
<div data-v-03b808c2="" class="van-popup van-popup--center pop-quytac-buy" style="display: none;width: 80%; border-radius: 10px; max-width: 340px; z-index: 2016;"><div data-v-03b808c2="" class="rule-box"><div data-v-03b808c2="" class="title c-row c-row-middle-center">Pre-selling rules</div><div data-v-03b808c2="" class="info"><div data-v-03b808c2="" class="comment"> To protect the legitimate rights and interests of users participating in pre-selling and maintain the normal operation of pre-selling activities, the rules are established in accordance with relevant agreements and regulations of the law, as well as national provisions.

Chapter 1: Definitions
1.1 Pre-selling definition: Refers to the sales model in which the seller provides a product or service package to collect orders from consumers through pre-selling tools and delivers the goods or services to the buyer according to pre-agreed terms.
1.2 Pre-selling model is the "Consignment" model. The term "Consignment" refers to a fixed amount of pre-sold goods that are predetermined. "Consignment" can participate in small games and has the opportunity to gain more profits. The consigned amount can be directly exchanged for goods, but the consigned money cannot be used for exchange.
1.3 Pre-selling products: These are products provided by the seller using pre-selling tools. Only products labeled as pre-selling on the title or product detail page are considered pre-selling products. Other products that do not use pre-selling tools are not pre-selling products.
1.4 Pre-selling system: Refers to the system of pre-selling tools provided to support sales according to the pre-selling model.
1.5 Pre-selling price of goods: The price of the goods before the sale. The pre-selling price of goods includes two parts: the selling price and the payment. </div><div data-v-03b808c2="" class="rule-btn c-row m-t-20 c-row-center"><button data-v-03b808c2="" class="btn van-button van-button--default van-button--normal van-button--block van-button--round" style="color: rgb(255, 255, 255); background: rgb(242, 65, 59); border-color: rgb(242, 65, 59);"><div data-v-03b808c2="" class="van-button__content"><span data-v-03b808c2="" class="van-button__text"><span data-v-03b808c2="">I know</span></span></div></button></div></div></div></div>

<div data-v-7692a079="" data-v-806b9f0e="" class="Loading c-row c-row-middle-center" style="">
    <div data-v-7692a079="" class="van-loading van-loading--circular">
        <span data-v-7692a079="" class="van-loading__spinner van-loading__spinner--circular" style="-webkit-animation-duration: 1s; animation-duration: 1s;">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 200 200" width="200" height="200" preserveAspectRatio="xMidYMid meet" style="width: 100%;height: 100%;transform: translate3d(0px, 0px, 0px);content-visibility: visible;">
                <g transform="matrix(-0.5984085202217102,0.8011911511421204,-0.8011911511421204,-0.5984085202217102,239.9599609375,79.72173309326172)" style="display: block;">
                    <image width="200px" height="200px" preserveAspectRatio="xMidYMid slice" xlink:href="/index_files/loadingspinner.png"></image>
                </g>
                <g transform="matrix(0.9408400058746338,0,0,0.9408400058746338,20.969436645507812,79.30152130126953)" style="display: block;">
                    <image width="168px" height="44px" preserveAspectRatio="xMidYMid slice" xlink:href="/index_files/h5setting_202401100608011fs2.png"></image>
                </g>
            </svg>
        </span>
    </div>
</div>