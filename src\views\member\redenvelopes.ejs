<!DOCTYPE html>
<html translate="no" data-dpr="1" style="font-size: 48.16px" style="min-height: 100vh">

<head>
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="robots" content="noindex,nofollow" />
  <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport" />
  <title>Report</title>
  <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.1/css/all.css">
  <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.1/css/sharp-thin.css">
  <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.1/css/sharp-solid.css">
  <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.1/css/sharp-regular.css">
  <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.1/css/sharp-light.css">
  <link rel="stylesheet" href="./index_files/modules-96c1e775.css">
  <link rel="stylesheet" href="./index_files/page-activity-d48e62db.css">
  <link rel="stylesheet" href="./index_files/index-12322523.css">
  <link rel="stylesheet" href="./index_files/page-home-62c84d06.css">
  <link rel="stylesheet" href="./index_files/page-login-b5988105.css">
  <link rel="stylesheet" href="./index_files/page-main-ff2e7571.css">
  <link rel="shortcut icon" href="/images/bitbug_favicon.ico" type="image/x-icon" />
  <style>
    .msg[data-v-1dcba851] {
      max-width: 12.8rem;
      width: 80%;
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 100000
    }

    .msg .msg-content[data-v-1dcba851] {
      position: relative;
      padding: .32rem .66667rem;
      text-align: center;
      line-height: .64rem;
      word-break: break-all;
      overflow: hidden;
      font-size: .37333rem;
      color: #fff;
      background-color: rgba(0, 0, 0, .8)
    }

    .v-enter[data-v-1dcba851],
    .v-leave-to[data-v-1dcba851] {
      opacity: 0
    }

    .v-enter-active[data-v-1dcba851] {
      transition: opacity .5s;
      animation: bounce-in-data-v-1dcba851 .5s
    }

    .v-leave-active[data-v-1dcba851] {
      transition: opacity .3s;
      animation: bounce-in-data-v-1dcba851 .5s reverse
    }

    @keyframes bounce-in-data-v-1dcba851 {
      0% {
        transform: scale(.4)
      }

      50% {
        transform: scale(1.03)
      }

      to {
        transform: scale(1)
      }
    }
  </style>
</head>

<body style="font-size: 12px;" class="">


  <div id="app" data-v-app="">
    <div data-v-3b4d4aab="" class="redeem-container" style="--7688837a: &#39;Roboto&#39;, &#39;Inter&#39;, sans-serif;">
      <div data-v-3b4d4aab="" class="redeem-container-header">
        <div data-v-58c5e826="" data-v-3b4d4aab="" class="navbar">
          <div data-v-58c5e826="" class="navbar-fixed"
            style="background: linear-gradient(90deg, rgb(196, 147, 63) 0%, rgb(250, 229, 159) 100%);">
            <div data-v-58c5e826="" class="navbar__content">
              <div data-v-58c5e826="" class="navbar__content-left" onclick="location.href='/mian'">
                <i class="fa-light fa-angle-left fa-fade fa-xl center" style="color: #FFF"></i>
              </div>
              <div data-v-58c5e826="" class="navbar__content-center">
                <div data-v-58c5e826="" class="navbar__content-title">Gift</div>
              </div>
              <div data-v-58c5e826="" class="navbar__content-right"></div>
            </div>
          </div>
        </div>
        <div data-v-3b4d4aab="" class="redeem-container-header-belly">
          <img data-v-3b4d4aab="" alt="" class=""
            data-origin="https://www.bigdaddygame2.com/assets/png/gift-0e49be1a.png"
            src="./index_files/gift-0e49be1a.png">
        </div>
      </div>
      <div data-v-3b4d4aab="" class="redeem-container-content">
        <div data-v-3b4d4aab="" class="redeem-container-receive">
          <p data-v-3b4d4aab="">Hi</p>
          <p data-v-3b4d4aab="">We have a gift for you</p>
          <h4 data-v-3b4d4aab="">Please enter the gift code below</h4>
          <input data-v-3b4d4aab="" type="text" auto-complete="new-password" autocomplete="off"
            placeholder="Please enter gift code">
          <button data-v-3b4d4aab="">Receive</button>
        </div>
      </div>
    </div>
    <div class="customer" id="customerId"
      style="--7688837a: &#39;Roboto&#39;, &#39;Inter&#39;, sans-serif; --17a7a9f6: bahnschrift;"
      onclick="window.location='/keFuMenu'">
      <img class="" data-origin="https://www.bigdaddygame2.com/assets/png/icon_sevice-1ca64bcf.png"
        src="./index_files/icon_sevice-1ca64bcf.png">
    </div>
  </div>
  <div class="van-overlay" style="z-index: 2003; display: none;">
    <div data-v-7692a079="" data-v-42f27458="" class="Loading c-row c-row-middle-center"
      style="position: fixed;height: 100vh;width: 100vw;top: 0;left: 0;background: rgba(0,0,0,.6);z-index: 99999;">
      <div data-v-7692a079="" class="van-loading van-loading--circular">
        <span data-v-7692a079="" class="van-loading__spinner van-loading__spinner--circular"
          style="width: auto; height: auto; -webkit-animation-duration: 1s; animation-duration: 1s;">
          <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 200 200"
            width="200" height="200" preserveAspectRatio="xMidYMid meet"
            style="width: 100%;height: 100%;transform: translate3d(0px, 0px, 0px);content-visibility: visible;">
            <g transform="matrix(-0.5984085202217102,0.8011911511421204,-0.8011911511421204,-0.5984085202217102,239.9599609375,79.72173309326172)"
              style="display: block;">
              <image width="200px" height="200px" preserveAspectRatio="xMidYMid slice"
                xlink:href="/index_files/loadingspinner.png"></image>
            </g>
          </svg>
        </span>
        <img src="/index_files/h5setting_202401100608011fs2.png"
          style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 168px; height: 44px;">
      </div>
    </div>
  </div>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
  <script src="/js/client.js"></script>
  <script>
    $(document).ready(function () {
      // Select the overlay element
      $('.van-overlay').fadeIn(10);
    });
    $(window).on('load', function () {
      // Select the overlay element
      $('.van-overlay').fadeIn(10);
      setTimeout(function () {
        $('.van-overlay').fadeOut(300);
      }, 500);
    });
  </script>
  <script>
    function alertMess(text, sic) {
      $('body').append(
        `
                <div data-v-1dcba851="" class="msg">
                    <div data-v-1dcba851="" class="msg-content v-enter-active v-enter-to" style=""> ${text} </div>
                </div>
                `
      );
      setTimeout(() => {
        $('.msg .msg-content').removeClass('v-enter-active v-enter-to');
        $('.msg .msg-content').addClass('v-leave-active v-leave-to');
        setTimeout(() => {
          $('.msg').remove();
        }, 100);
        sic.removeClass('block-click');
      }, 1500);
    }
    $('button').click(function (e) {
      e.preventDefault();
      let value = $('input').val().trim();
      $(this).addClass('block-click');
      let iej = $(this);
      if (value) {
        $.ajax({
          type: "POST",
          url: "/api/webapi/use/redenvelope",
          data: {
            code: value,
          },
          dataType: "json",
          success: function (response) {
            alertMess(response.message, iej);
          }
        });
      } else {
        alertMess('Please enter the gift code', iej);
      }
    });
  </script>
</body>

</html>