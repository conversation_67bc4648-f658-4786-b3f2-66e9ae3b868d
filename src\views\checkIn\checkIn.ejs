<!DOCTYPE html>
<html translate="no" data-dpr="1" style="font-size: 48.24px;">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="robots" content="noindex,nofollow">
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport">
    <title>Check In</title>
    <link rel="stylesheet" href="./index_files/index-96409872.css">
    <link href="/css/checkIn/chunk-vendors.css" rel="stylesheet" />
    <link href="/css/checkIn/app.css" rel="stylesheet" />
    <link href="/css/checkIn/chunk.css" rel="stylesheet" />
    <link rel="shortcut icon" href="/images/bitbug_favicon.ico" type="image/x-icon">
    <style>
        html,
        body {
            height: 100%;
            width: 100%;
            background-color: #090909;
            padding: 0;
            margin: 0;
        }

        .block-click {
            pointer-events: none;
        }
    </style>
</head>

<body style="font-size: 12px;">
    <div id="app">
        <div data-v-11ffe290="" class="mian">
            <div data-v-106b99c8="" data-v-11ffe290="" class="navbar action">
                <div data-v-106b99c8="" class="navbar-left" onclick="location.href='/home'">
                    <div data-v-a9660e98="" data-v-106b99c8="" class="c-row c-row-middle-center">
                        <i class="fa-light fa-angle-left fa-fade fa-xl center" style="color: #FFF"></i>
                    </div>
                </div>
                <div data-v-106b99c8="" class="navbar-title"> Rewards </div>
                <div data-v-106b99c8="" class="navbar-right" onclick="location.href='/keFuMenu'">
                    <div data-v-11ffe290="" data-v-106b99c8="" class="c-row">
                        <i class="fa-fade fa center" style="color: #FFF">
                            <img data-v-11ffe290="" data-v-106b99c8="" src="/images/audio.webp" class="audio">
                        </i>
                    </div>
                </div>
            </div>
            <div data-v-11ffe290="" class="check-box">
                <div data-v-11ffe290="" class="check-header c-tc">
                    <h4 data-v-11ffe290="" class="tit m-b-5">Total Commission</h4>
                    <!-- <div data-v-11ffe290="" class="tit c-tc c-row c-row-middle-center">
                        <div data-v-11ffe290="" class="frequency c-row c-row-middle"> Receive: <p data-v-11ffe290=""
                                class="c-row">Day<span data-v-11ffe290="" class="num">0</span></p>
                        </div>
                    </div> -->
                    <div data-v-11ffe290="" class="c-row c-row-middle-center p-l-20 p-r-20">
                        <div data-v-11ffe290="">
                           <!-- <p data-v-11ffe290="" class="txt" onclick="location.href='/checkDes'">Rule</p> -->
                            <p data-v-11ffe290="" class="txt"  id="totalCommission" >₹&nbsp;0</p>
                        </div>
                    </div>
                </div>
                



         
<style>
 .f12{font-size: 12px!important;}
</style>
                <div data-v-11ffe290="" class="list c-row c-flex-warp">
                    
                 
                    <div data-v-11ffe290="" data-dpr="6" class="item action" onclick="location.href='/history/ReferralBonus'">
                        <!---->
                        <div data-v-11ffe290="" class="num c-row c-row-middle c-row-center" id="refCommission"> ₹&nbsp;<span
                                data-v-11ffe290="" class="des">0</span></div><img data-v-11ffe290=""
                            src="/images/tu.webp" class="img"><span data-v-11ffe290="" class="txt f12" >Referral&nbsp;Bonus</span>
                        <!---->
                    </div>
                    
                    
                      <div data-v-11ffe290="" data-dpr="3" class="item action" onclick="location.href='/history/DailyTrade'">
                        <!---->
                        <div data-v-11ffe290="" class="num c-row c-row-middle c-row-center" id="levelCommission"> ₹&nbsp;<span
                                data-v-11ffe290="" class="des">0</span></div><img data-v-11ffe290=""
                            src="/images/tu.webp" class="img"><span data-v-11ffe290="" class="txt f12" >Daily&nbsp;Trade</span>
                        <!---->
                    </div>



                    
                    <div data-v-11ffe290="" data-dpr="2" class="item action" onclick="location.href='/history/DailySalary'">
                        <!---->
                        <div data-v-11ffe290="" class="num c-row c-row-middle c-row-center" id="totalDailySalary">₹&nbsp; <span
                                data-v-11ffe290="" class="des">0</span></div><img data-v-11ffe290=""
                            src="/images/tu.webp" class="img"><span data-v-11ffe290="" class="txt f12">Daily&nbsp;Salary </span>
                        <!---->
                    </div>
                   
                    <div data-v-11ffe290="" data-dpr="4" class="item action" onclick="location.href='/history/WeeklySalary'">
                        <!---->
                        <div data-v-11ffe290="" class="num c-row c-row-middle c-row-center" id="totalWeeklySalary"> ₹&nbsp;<span
                                data-v-11ffe290="" class="des">0</span></div><img data-v-11ffe290=""
                            src="/images/tu.webp" class="img"><span data-v-11ffe290="" class="txt f12">Weekly&nbsp;Salary</span>
                        <!---->
                    </div>
                    <div data-v-11ffe290="" data-dpr="5" class="item action" onclick="location.href='/history/MonthlySalary'">
                        <!---->
                        <div data-v-11ffe290="" class="num c-row c-row-middle c-row-center" id="totalMonthlySalary"> ₹&nbsp;<span
                                data-v-11ffe290="" class="des">0</span></div><img data-v-11ffe290=""
                            src="/images/tu.webp" class="img"><span data-v-11ffe290="" class="txt f12">Monthly&nbsp;Salary</span>
                        <!---->
                    </div>
                     
                     <div  data-v-11ffe290="" data-dpr="1" class="item action" onclick="location.href='/history/RechargeSalary'">
                        <!---->
                        <div data-v-11ffe290="" class="num c-row c-row-middle c-row-center" id="rechargeCommission">₹&nbsp;<span
                                data-v-11ffe290="" class="des"> 0</span></div><img data-v-11ffe290=""
                            src="/images/tu.webp" class="img"><span data-v-11ffe290="" class="txt f12"  >Recharge&nbsp;Bonus</span>
                        <!---->
                        
                        




                    </div>
                     
                     
                        <div style="display:none;" data-v-11ffe290="" data-dpr="1" class="item action" onclick="location.href='/history/inviteBonus'">
                        <!---->
                        <div data-v-11ffe290="" class="num c-row c-row-middle c-row-center" id="inviteBonus">₹&nbsp;<span
                                data-v-11ffe290="" class="des"> 0</span></div><img data-v-11ffe290=""
                            src="/images/tu.webp" class="img"><span data-v-11ffe290="" class="txt f12"  >Invite&nbsp;Bonus</span>
                        <!---->


                    </div>
                    
                    
                    
                    
                    
                
                
                </div>
            </div>
            <div data-v-7692a079="" data-v-432e6ed0="" class="Loading c-row c-row-middle-center">
                <div data-v-7692a079="" class="van-loading van-loading--circular">
                    <span data-v-7692a079="" class="van-loading__spinner van-loading__spinner--circular"
                        style="width: auto; height: auto; -webkit-animation-duration: 1s; animation-duration: 1s;">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                            viewBox="0 0 200 200" width="200" height="200" preserveAspectRatio="xMidYMid meet"
                            style="width: 100%;height: 100%;transform: translate3d(0px, 0px, 0px);content-visibility: visible;">
                            <g transform="matrix(-0.5984085202217102,0.8011911511421204,-0.8011911511421204,-0.5984085202217102,239.9599609375,79.72173309326172)"
                                style="display: block;">
                                <image width="200px" height="200px" preserveAspectRatio="xMidYMid slice"
                                    xlink:href="/index_files/loadingspinner.png"></image>
                            </g>
                            <g transform="matrix(0.9408400058746338,0,0,0.9408400058746338,20.969436645507812,79.30152130126953)"
                                style="display: block;">
                                
                            </g>
                        </svg>
                    </span>
                    <img src="/index_files/h5setting_202401100608011fs2.png" style="position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);width: 80%;height: 80%;margin-top: 10px;">
                </div>
            </div>
            <%- include('../nav') -%>
        </div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="/js/client.js"></script>
    <script>
        function alertMess(text, sic) {
            $('body').append(
                `
                <div data-v-1dcba851="" class="msg">
                    <div data-v-1dcba851="" class="msg-content v-enter-active v-enter-to" style=""> ${text} </div>
                </div>
                `
            );
            setTimeout(() => {
                $('.msg .msg-content').removeClass('v-enter-active v-enter-to');
                $('.msg .msg-content').addClass('v-leave-active v-leave-to');
                setTimeout(() => {
                    $('.msg').remove();
                }, 100);
                sic.removeClass('block-click');
            }, 1000);
        }
      
        
        
        
     
     
     
     
       $.ajax({
            type: "POST",
            url: "/api/webapi/commission",
            data: {

            },
            dataType: "json",
            success: function (response) {
                $('.Loading').fadeOut(0);
                
                
                $('#totalCommission').text("₹" +response.totalCommission);
                $('#levelCommission').text("₹" +response.levelCommission);
                $('#refCommission').text("₹" +response.refCommission);
                
                $('#totalDailySalary').text("₹" +response.totalDailySalary);
                $('#totalWeeklySalary').text("₹" +response.totalWeeklySalary);
                $('#totalMonthlySalary').text("₹" +response.totalMonthlySalary);
                $('#inviteBonus').text("₹" +response.inviteBonuss);
                
                
                
            //    console.log(response);


                
               // $('#rechargeCommission').text("₹" +response.rechargeCommission);
                
                 
                 
                 
                 
            }
        });
        
        
        
        
         
        
        
        
        
    </script>
    <script>
        $(window).on('load', function () {
            $('.Loading').fadeOut(0);
        });
    </script>
</body>

</html>