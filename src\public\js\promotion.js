/**
 * Promotion Page JavaScript
 * Handles all promotion-related functionality including data fetching and UI updates
 */

class PromotionManager {
    constructor() {
        this.isLoading = false;
        this.refreshInterval = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadAllData();
        this.startAutoRefresh();
    }

    setupEventListeners() {
        // Copy invitation code click handler
        $(document).on('click', '[data-copy-invitation]', (e) => {
            e.preventDefault();
            this.copyInvitationCode();
        });

        // Copy buttons for other elements
        $('.btn-box .btn').on('click', (e) => {
            e.preventDefault();
            const button = $(e.currentTarget);
            button.addClass('block-click');
            const textToCopy = button.attr('data-clipboard-text');
            if (textToCopy) {
                this.copyToClipboard(textToCopy, button);
            }
        });
    }

    async loadAllData() {
        if (this.isLoading) return;

        this.isLoading = true;
        this.showLoading();

        try {
            await Promise.all([
                this.loadPromotionData(),
                this.loadTeamData(),
                this.loadUserInfo()
            ]);
        } catch (error) {
            console.error('Error loading promotion data:', error);
            this.showError('Failed to load promotion data');
        } finally {
            this.isLoading = false;
            this.hideLoading();
        }
    }

    async loadPromotionData() {
        return new Promise((resolve, reject) => {
            $.ajax({
                type: "POST",
                url: "/api/webapi/promotion",
                data: {},
                dataType: "json",
                success: (response) => {
                    console.log('Promotion API Response:', response);

                    if (response.status === false) {
                        window.location.href = '/home';
                        return;
                    }

                    this.updatePromotionUI(response);
                    resolve(response);
                },
                error: (xhr, status, error) => {
                    console.error('Error fetching promotion data:', error);
                    reject(error);
                }
            });
        });
    }

    async loadTeamData() {
        return new Promise((resolve, reject) => {
            $.ajax({
                type: "GET",
                url: "/api/webapi/myTeam",
                dataType: "json",
                success: (response) => {
                    console.log('Team API Response:', response);

                    if (response.status === true) {
                        this.updateTeamUI(response);
                    }
                    resolve(response);
                },
                error: (xhr, status, error) => {
                    console.error('Error fetching team data:', error);
                    reject(error);
                }
            });
        });
    }

    async loadUserInfo() {
        return new Promise((resolve, reject) => {
            fetch('/api/webapi/GetUserInfo')
                .then(response => response.json())
                .then(data => {
                    if (data.status === false) {
                        unsetCookie();
                        return;
                    }
                    this.updateUserInfo(data);
                    resolve(data);
                })
                .catch(error => {
                    console.error('Error fetching user info:', error);
                    reject(error);
                });
        });
    }

    updatePromotionUI(response) {
        // Update commission data
        $('#yesterday-commission').text(this.formatNumber(response.invite.roses_today || 0, 2));

        // Update direct subordinates data
        $('#direct-register-count').text(response.invite.f1 || 0);
        $('#direct-register-today').text(response.invite.f1_today || 0);

        // Update team subordinates data  
        $('#team-register-count').text(response.invite.total_f || 0);
        $('#team-register-today').text(response.invite.f_all_today || 0);

        // Update invitation code
        if (response.info && response.info[0] && response.info[0].code) {
            this.updateInvitationCode(response.info[0].code);
        }

        // Update other elements
        this.updateOtherElements(response);

        // Render level data if available
        if (response.level && typeof RosesRender === 'function') {
            RosesRender(response.level);
        }
    }

    updateTeamUI(response) {
        if (!response.f1) return;

        // Calculate team statistics
        let totalDeposit = 0;
        let totalBetAmount = 0;
        let firstDepositCount = 0;

        response.f1.forEach(member => {
            totalDeposit += parseFloat(member.total_money || 0);
            totalBetAmount += parseFloat(member.total_turn_over || 0);
            // Count members with deposits as first deposit (you can modify this logic)
            if (parseFloat(member.total_money || 0) > 0) {
                firstDepositCount++;
            }
        });

        // Add user's own turnover if available
        const myTurnover = parseFloat(response.myTunrOver || response.myTurnOver || 0);
        totalBetAmount += myTurnover;

        // Update team statistics
        $('#team-deposit-amount').text(this.formatNumber(totalDeposit, 0));
        $('#team-bet-amount').text(this.formatNumber(totalBetAmount, 0));
        $('#team-first-deposit').text(firstDepositCount);

        // Update direct statistics
        if (response.f1_direct) {
            let directDeposit = 0;
            let directBetAmount = 0;
            let directFirstDeposit = 0;

            response.f1_direct.forEach(member => {
                directDeposit += parseFloat(member.total_money || 0);
                directBetAmount += parseFloat(member.total_turn_over || 0);
                if (parseFloat(member.total_money || 0) > 0) {
                    directFirstDeposit++;
                }
            });

            $('#direct-deposit-amount').text(this.formatNumber(directDeposit, 0));
            $('#direct-bet-amount').text(this.formatNumber(directBetAmount, 0));
            $('#direct-first-deposit').text(directFirstDeposit);
        }

        // Update total member display if element exists
        if ($('#total-mem').length) {
            const totalRecharge = totalDeposit;
            const totalTurnover = totalBetAmount;
            $('#total-mem').text(`Total Recharge: ${this.formatNumber(totalRecharge, 0)} | Total Turnover: ${this.formatNumber(totalTurnover, 0)}`);
        }
    }

    updateUserInfo(data) {
        const userData = data.data;

        // Update user elements if they exist
        $('.name').text(userData.name_user || 'Member');
        $('.uid').html(`UID | ${userData.id_user || 'N/A'} &nbsp;<span><i class="fa-regular fa-copy text-white"></i></span>`);
        $('.uid').attr('data-clipboard-text', userData.id_user || '');

        // Update phone number display
        if (userData.phone_user) {
            const phone = userData.phone_user.toString();
            const maskedPhone = `+91 ${phone.slice(0, 2)}****${phone.slice(-4)}`;
            $('.logindate').html(`<span>Phone: </span><span>${maskedPhone}</span>`);
        }

        // Store user data globally
        window.currentUserData = userData;
        window.fullApiResponse = data;
    }

    updateInvitationCode(code) {
        const inviteLink = window.location.hostname + `/register?r_code=${code}`;

        $('#invitation-code-display').html(`${code}<i class="fa-regular fa-copy text-muted"></i>`);
        $('#invite_code').text(inviteLink);

        // Set clipboard data
        $('.btn-box .btn:eq(0)').attr('data-clipboard-text', code);
        $('.btn-box .btn:eq(1)').attr('data-clipboard-text', inviteLink);

        // Generate QR code
        this.generateQRCode(inviteLink);
    }

    updateOtherElements(response) {
        $('#f1').text(response.invite.f1 || 0);
        $('#f1_today').text(response.invite.f1_today || 0);
        $('#f_all_today').text(response.invite.f_all_today || 0);
        $('#f_total').text(response.invite.total_f || 0);
        $('#roses_all').text(this.formatNumber(response.invite.roses_all || 0, 2));
        $('#roses_f1').text("Ref. Rec. Com.(4%) : " + this.formatNumber(response.invite.referralCommission || 0, 2));
        $('#roses_today').text(this.formatNumber(response.invite.roses_today || 0, 2));
    }

    generateQRCode(link) {
        try {
            if (typeof QRCode !== 'undefined' && $('#qrcode').length) {
                $('#qrcode').empty(); // Clear existing QR code
                const qrcode = new QRCode(document.getElementById("qrcode"));
                $('#qrcode').attr('title', link);
                qrcode.makeCode(link);
            }
        } catch (error) {
            console.error('Error generating QR code:', error);
        }
    }

    copyInvitationCode() {
        const codeElement = $('#invitation-code-display');
        const codeText = codeElement.text().replace(/\s*$/, '');
        const actualCode = codeText.split(' ')[0];

        if (actualCode && actualCode !== 'Loading...') {
            this.copyToClipboard(actualCode, codeElement);
        }
    }

    copyToClipboard(text, element) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                this.showSuccessMessage('Copied successfully!', element);
            }).catch((err) => {
                console.error('Could not copy text: ', err);
                this.fallbackCopy(text, element);
            });
        } else {
            this.fallbackCopy(text, element);
        }
    }

    fallbackCopy(text, element) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        this.showSuccessMessage('Copied successfully!', element);
    }

    showSuccessMessage(text, element) {
        $('body').append(`
            <div data-v-1dcba851="" class="msg">
                <div data-v-1dcba851="" class="msg-content v-enter-active v-enter-to">${text}</div>
            </div>
        `);

        setTimeout(() => {
            $('.msg .msg-content').removeClass('v-enter-active v-enter-to');
            $('.msg .msg-content').addClass('v-leave-active v-leave-to');
            setTimeout(() => {
                $('.msg').remove();
            }, 100);
            if (element) {
                element.removeClass('block-click');
            }
        }, 1000);
    }

    showError(message) {
        console.error(message);
        this.showSuccessMessage(`Error: ${message}`, null);

        // Set default values on error
        $('#yesterday-commission').text('0.00');
        $('#direct-register-count').text('0');
        $('#direct-register-today').text('0');
        $('#team-register-count').text('0');
        $('#team-register-today').text('0');
        $('#invitation-code-display').html('Error loading<i class="fa-regular fa-copy text-muted"></i>');
    }

    showLoading() {
        $('.van-overlay').fadeIn(10);

        // Show loading states
        $('#yesterday-commission').text('...');
        $('#direct-register-count').text('...');
        $('#direct-register-today').text('...');
        $('#team-register-count').text('...');
        $('#team-register-today').text('...');
        $('#invitation-code-display').html('Loading...<i class="fa-regular fa-copy text-muted"></i>');
    }

    hideLoading() {
        $('.van-overlay').fadeOut(10);
    }

    formatNumber(num, decimals = 2) {
        return parseFloat(num || 0).toFixed(decimals);
    }

    startAutoRefresh() {
        // Refresh data every 30 seconds
        this.refreshInterval = setInterval(() => {
            this.loadAllData();
        }, 30000);
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    destroy() {
        this.stopAutoRefresh();
        $(document).off('click', '[data-copy-invitation]');
        $('.btn-box .btn').off('click');
    }
}

// Initialize promotion manager when document is ready
$(document).ready(function() {
    window.promotionManager = new PromotionManager();
});