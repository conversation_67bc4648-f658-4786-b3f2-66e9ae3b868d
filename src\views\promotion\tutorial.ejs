<!DOCTYPE html>
<html translate="no" data-dpr="1" style="font-size: 48.24px;">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="robots" content="noindex,nofollow">
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport">
    <title>Level</title>
    <link href="/css/promotion/app.css" rel="stylesheet" />
    <link href="/css/promotion/chunk-0_1.css" rel="stylesheet" />
    <link href="/css/promotion/chunk-0_2.css" rel="stylesheet" />
    <link href="/css/promotion/chunk-0_3.css" rel="stylesheet" />
    <link href="/css/promotion/chunk-0_4.css" rel="stylesheet" />
    <link href="/css/promotion/chunk-0_5.css" rel="stylesheet" />
    <link href="/css/promotion/chunk-0_6.css" rel="stylesheet" />
    <link rel="shortcut icon" href="/images/bitbug_favicon.ico" type="image/x-icon">
    <style>
        html,
        body {
            height: 100%;
            width: 100%;
            background-color: #090909;
            padding: 0;
            margin: 0;
             }

        #nav_checkUrl {
            left: 35vw;
        }
    </style>
</head>

<body style="font-size: 12px;">
    <div id="app">
        <div data-v-7c8bbbf4="" class="mian">
            <div data-v-106b99c8="" data-v-7c8bbbf4="" class="navbar">
                <div data-v-106b99c8="" class="navbar-left" onclick="location.href='/home'">
                    <div data-v-a9660e98="" data-v-106b99c8="" class="c-row c-row-middle-center">
                        <i class="fa-light fa-angle-left fa-fade fa-xl center" style="color: #FFF"></i>
                    </div>
                </div>
                <div data-v-106b99c8="" class="navbar-title"> Tutorial </div>
                <div data-v-106b99c8="" class="navbar-right" onclick="location.href='/promotion/bonusrecord'">
                    <div data-v-7c8bbbf4="" data-v-106b99c8="" class="c-row">
                        <i class="fa-duotone fa-calendar-lines fa-fade fa-lg"
                            style="--fa-primary-color: #fff; --fa-secondary-color: #fff;"></i>
                    </div>
                </div>
            </div>
            <div data-v-7c8bbbf4="" class="promotion">
                <div data-v-7c8bbbf4="" class="tab">
                    <ul data-v-7c8bbbf4="" class="c-row c-row-between">
                        <li data-v-7c8bbbf4="" onclick="location.href='/promotion'">Data</li>
                        <li data-v-7c8bbbf4="" onclick="location.href='/promotion/myTeam'">My Team</li>
                        <li data-v-7c8bbbf4="" onclick="location.href='/promotion/promotionDes'">History</li>
                        <li data-v-7c8bbbf4="" onclick="location.href='/promotion/tutorial'" class="action block-click">
                            Tutorial</li>
                    </ul>
                </div>
                <div data-v-7c8bbbf4="" class="tit c-row c-row-between">Turn over on Level </div>
                <div data-v-7c8bbbf4="" class="table">
                    <div data-v-7c8bbbf4="" class="hd van-row">
                        <div data-v-7c8bbbf4="" class="c-tc van-ellipsis van-col van-col--6" style="    font-size: 12px;">Levels</div>
                        <div data-v-7c8bbbf4="" class="c-tc van-ellipsis van-col van-col--6" style="    font-size: 12px;">Team</div>
                        <div data-v-7c8bbbf4="" class="c-tc van-ellipsis van-col van-col--6" style="    font-size: 12px;">Today Recharge</div>
                        <div data-v-7c8bbbf4="" class="c-tc van-ellipsis van-col van-col--6" style="    font-size: 12px;">Total Recharge</div>
                    </div>
                     <div id="van-list">

                        </div>
                     
                </div>
                <!--      <div data-v-7c8bbbf4="" class="tit c-row c-row-between m-t-15">Commission Calculation Method</div>
                    <div data-v-7c8bbbf4="" class="table">
                        <div data-v-7c8bbbf4="" class="box" style="width: 420px;">
                            <div data-v-7c8bbbf4="" class="hd dl van-row">
                                <div data-v-7c8bbbf4="" class="c-tc van-ellipsis van-col van-col--6">
                                    Hierarchy<p data-v-7c8bbbf4="" class="txt">Rebate Ratio</p>
                                </div>
                                <div data-v-7c8bbbf4="" class="c-tc van-ellipsis van-col van-col--4">
                                    Tier 1<p data-v-7c8bbbf4="" class="txt">Commission</p>
                                </div>
                                <div data-v-7c8bbbf4="" class="c-tc van-ellipsis van-col van-col--4">
                                    Tier 2<p data-v-7c8bbbf4="" class="txt">Commission</p>
                                </div>
                                <div data-v-7c8bbbf4="" class="c-tc van-ellipsis van-col van-col--5">
                                    Tier 3<p data-v-7c8bbbf4="" class="txt">Commission</p>
                                </div>
                                <div data-v-7c8bbbf4="" class="c-tc van-ellipsis van-col van-col--5">
                                    Tier 4<p data-v-7c8bbbf4="" class="txt">Commission</p>
                                </div>
                            </div> -->
            </div>
        </div>
    </div>
    </div>
    <!---->
    <%- include('../nav') -%>
        </div>
        </div>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
        <script src="/js/client.js"></script>
        <script>
            $('.nav .van-tabbar-item:eq(0) img').attr('src', '/images/home1.png');
            $('.nav .van-tabbar-item:eq(0) .name').removeClass('action');
            $('.nav .van-tabbar-item:eq(2) .name').addClass('action');
            function RosesRender(datas) {
                let html = datas.map((e) => {
                    let html = '';
                    
                    

 



                    return html += `
                    
                    <div data-v-7c8bbbf4="" class="bd van-row">
                        <div data-v-7c8bbbf4="" class="c-tc van-ellipsis van-col van-col--6">Lvl  ${e.level}</div>
                        <div data-v-7c8bbbf4="" class="c-tc van-col van-col--6">${e.total}</div>
                        <div data-v-7c8bbbf4="" class="c-tc van-col van-col--6">${e.amount}</div>
                        <div data-v-7c8bbbf4="" class="c-tc van-col van-col--6">${e.totalAmount}</div>
                    </div> `;
                });
                $('#van-list').append(html);

                $('.table:eq(1) .box').append(`
                <div data-v-7c8bbbf4="" class="bd van-row">
                    <div data-v-7c8bbbf4="" class="c-tc fw van-col van-col--6">...</div>
                    <div data-v-7c8bbbf4="" class="c-tc fw van-col van-col--4">...</div>
                    <div data-v-7c8bbbf4="" class="c-tc fw van-col van-col--4">...</div>
                    <div data-v-7c8bbbf4="" class="c-tc fw van-col van-col--5">...</div>
                    <div data-v-7c8bbbf4="" class="c-tc fw van-col van-col--5">...</div>
                </div>
                `);
            }
            $.ajax({
                type: "POST",
                url: "/api/webapi/LevelTurnOver",
                data: {

                },
                dataType: "json",
                success: function (response) {
                    if (response.status === false) return location.href = '/home';
                     RosesRender(response.level);
                }
            });
        </script>
</body>

</html>