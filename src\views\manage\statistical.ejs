<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Statisticals</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <link rel="stylesheet" href="/plugins/fontawesome-free/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
    <link rel="stylesheet" href="/dist/css/adminlte.min.css">
    <link href="//cdn.jsdelivr.net/npm/@sweetalert2/theme-dark@4/dark.css" rel="stylesheet">
    <link rel="stylesheet" href="/css/admin.css">
</head>

<body class="dark-mode sidebar-mini layout-fixed layout-navbar-fixed layout-footer-fixed">
    <div class="wrapper">
        <%- include('nav') %>

            <!-- Content Wrapper. Contains page content -->
            <div class="content-wrapper">
                <!-- Content Header (Page header) -->
                <section class="content-header">
                    <div class="container-fluid">
                        <div class="row mb-2">
                            <div class="col-sm-6">
                                <h1>Statistics</h1>
                            </div>
                        </div>
                    </div>
                    <!-- /.container-fluid -->
                </section>

                <!-- Main content -->
                <section class="content">
                    <div class="container-fluid">
                        <!-- <h5 class="mb-2">Info Box</h5> -->
                        <div class="row">
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box">
                                    <span class="info-box-icon bg-info"><i class="fa fa-user-circle" aria-hidden="true"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Number Of Member</span>
                                        <span class="info-box-number totalMember">0</span>
                                    </div>
                                    <!-- /.info-box-content -->
                                </div>
                                <!-- /.info-box -->
                             </div>
                            <!-- /.col -->
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box">
                                    <span class="info-box-icon bg-success"><i class="far fa-flag"></i></span>

                                    <div class="info-box-content">
                                        <span class="info-box-text">Win</span>
                                        <span class="info-box-number totalWin">0</span>
                                    </div>
                                    <!-- /.info-box-content -->
                                </div>
                                <!-- /.info-box -->
                            </div>
                            <!-- /.col -->
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box">
                                    <span class="info-box-icon bg-danger"><i class="far fa-flag"></i></span>

                                    <div class="info-box-content">
                                        <span class="info-box-text">Loss</span>
                                        <span class="info-box-number totalLoss">0</span>
                                    </div>
                                    <!-- /.info-box-content -->
                                </div>
                                <!-- /.info-box -->
                            </div>
                            <!-- /.col -->
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box">
                                    <span class="info-box-icon bg-warning"><i class="fa fa-user-times" aria-hidden="true"></i></span>

                                    <div class="info-box-content">
                                        <span class="info-box-text">Account is Locked</span>
                                        <span class="info-box-number totalMemberFail">0</span>
                                    </div>
                                    <!-- /.info-box-content -->
                                </div>
                                <!-- /.info-box -->
                            </div>
                            <!-- /.col -->
                        </div>
                        <!-- /.row -->

                        <!-- =========================================================== -->
                        <!-- <h5 class="mb-2">Info Box</h5> -->
                        <div class="row">
                            <!-- /.col -->
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box shadow-sm">
                                    <span class="info-box-icon bg-success"><i class="fa fa-check" aria-hidden="true"></i></span>

                                    <div class="info-box-content">
                                        <span class="info-box-text">Recharge</span>
                                        <span class="info-box-number totalRecharge">0</span>
                                    </div>
                                    <!-- /.info-box-content -->
                                </div>
                                <!-- /.info-box -->
                            </div>
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box shadow-sm">
                                    <span class="info-box-icon bg-success"><i class="fa fa-check" aria-hidden="true"></i></span>

                                    <div class="info-box-content">
                                        <span class="info-box-text">Recharge Today</span>
                                        <span class="info-box-number totalRechargeToday">0</span>
                                    </div>
                                    <!-- /.info-box-content -->
                                </div>
                                <!-- /.info-box -->
                            </div>
                            <!-- /.col -->
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box shadow">
                                    <span class="info-box-icon bg-warning"><i class="fa fa-university" aria-hidden="true"></i></span>

                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Withdrawal</span>
                                        <span class="info-box-number totalwithdraw">0</span>
                                    </div>
                                    <!-- /.info-box-content -->
                                </div>
                                <!-- /.info-box -->
                            </div>
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box shadow">
                                    <span class="info-box-icon bg-warning"><i class="fa fa-university" aria-hidden="true"></i></span>

                                    <div class="info-box-content">
                                        <span class="info-box-text">Withdraw Money Today</span>
                                        <span class="info-box-number totalwithdrawToday">0</span>
                                    </div>
                                    <!-- /.info-box-content -->
                                </div>
                                <!-- /.info-box -->
                            </div>
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box shadow">
                                    <span class="info-box-icon totalTodays bg-success"><i class="fa fa-balance-scale" aria-hidden="true"></i></span>

                                    <div class="info-box-content">
                                        <span class="info-box-text">Profit</span>
                                        <span class="info-box-number totalToday">0</span>
                                    </div>
                                    <!-- /.info-box-content -->
                                </div>
                                <!-- /.info-box -->
                            </div>
                        </div>

                        <div class="row info-box">
                            <div class="form-group" style="width: 100%;">
                                <div class="text-center">
                                    <label for="telegram">Create a Collaborator Account</label>
                                </div>
                                <label for="username" style="color: #3498db;">Account</label>
                                <input type="text" class="form-control" id="username" placeholder="Enter Account Number" value=""><br>
                                <label for="password" style="color: #3498db;">Account Number</label>
                                <input type="text" class="form-control" id="password" placeholder="Enter Password" value=""><br>
                                <button type="submit" class="btn btn-danger create_ctv" style="width: 100%;">Create</button>
                            </div>
                        </div>

                        <div class="row info-box">
                            <div class="form-group" style="width: 100%;">
                                <div class="text-center">
                                    <label for="">Increase / Decrease Money (Giftcode) For all CTV</label>
                                </div>
                                <label for="username" style="color: #3498db;">Increase | Decrease</label>
                                <select class="form-select mb-4" id="buff-all-ctv" style="width: 100%;padding: 5px;">
                                    <option selected value="">-------- Select Function --------</option>
                                    <option value="1">Plus Money (+)</option>
                                    <option value="2">Deduct Money (-)</option>
                                </select>
                                <label for="money-buff-all" style="color: #3498db;">Amount Of Money</label>
                                <input type="text" class="form-control" id="money-buff-all" placeholder="Enter Amount" value=""><br>
                                <button type="submit" class="btn btn-primary buff-all" style="width: 100%;">Submit</button>
                            </div>
                        </div>

                        <div class="row info-box">
                            <div class="form-group" style="width: 100%;">
                                <div class="text-center">
                                    <label for="phone">Increase/Decrease The Amount Generated (Giftcode) for CTV</label>
                                </div>
                                <label for="phone" style="color: #3498db;">Account</label>
                                <input type="text" class="form-control" id="phone" placeholder="Enter Collaborator Account" value=""><br>
                                <label for="buff-one" style="color: #3498db;">Increase | Decrease</label>
                                <select class="form-select mb-4" id="buff-one" style="width: 100%;padding: 5px;">
                                    <option selected value="">-------- Select Function --------</option>
                                    <option value="1">Plus Money (+)</option>
                                    <option value="2">Deduct Money (-)</option>
                                </select>
                                <label for="money-buff-one" style="color: #3498db;">Amount Of Money</label>
                                <input type="text" class="form-control" id="money-buff-one" placeholder="Enter Amount" value=""><br>
                                <button type="submit" class="btn btn-primary buff-one" style="width: 100%;">Submit</button>
                            </div>
                        </div>

                        <div class="row info-box">
                            <div class="form-group" style="width: 100%;">
                                <div class="text-center">
                                    <label for="">Points to add & subtract / membership (ALL)</label>
                                </div>
                                <label for="username" style="color: #3498db;">Increase | Decrease</label>
                                <!-- <select class="form-select mb-4" id="buff-two-ctv" style="width: 100%;padding: 5px;">
                                    <option selected value=""> Select Option </option>
                                    <option value="1">添加 (+)</option>
                                    <option value="2">减少 (-)</option>
                                </select> -->
                                <select class="form-select mb-4" id="buff-two-ctv" style="width: 100%;padding: 5px;">
                                    <option selected value="">-------- Select Function --------</option>
                                    <option value="1">Increase (+)</option>
                                    <option value="2">Decrease (-)</option>
                                </select>
                                <label for="money-buff-two" style="color: #3498db;">Amount Of Money</label>
                                <input type="text" class="form-control" id="money-buff-two" placeholder="Enter Amount" value=""><br>
                                <button type="submit" class="btn btn-primary buff-two" style="width: 100%;">Submit</button>
                            </div>
                        </div>
                        <div class="row info-box">
                            <div class="form-group" style="width: 100%;">
                                <div class="text-center">
                                    <label for="phone">Number of points to add and subtract / membership money</label>
                                </div>
                                <label for="phone" style="color: #3498db;">Account</label>
                                <input type="text" class="form-control" id="phone2" placeholder="Enter collaborator account" value=""><br>
                                <label for="buff-three" style="color: #3498db;">up or down</label>
                                <select class="form-select mb-4" id="buff-three" style="width: 100%;padding: 5px;">
                                    <option selected value="">-------- select function --------</option>
                                    <option value="1">Increase (+)</option>
                                    <option value="2">Decrease (-)</option>
                                </select>
                                <label for="money-buff-three" style="color: #3498db;">Amount Of Money</label>
                                <input type="text" class="form-control" id="money-buff-three" placeholder="Enter Amount" value=""><br>
                                <button type="submit" class="btn btn-primary buff-three" style="width: 100%;">Submit</button>
                            </div>
                        </div>

                    </div>
                    <!-- /.container-fluid -->
                </section>
                <!-- /.content -->
                

                <a id="back-to-top" href="#" class="btn btn-primary back-to-top" role="button" aria-label="Scroll to top">
                    <i class="fas fa-chevron-up"></i>
                </a>
            </div>
    </div>
    <script src="/plugins/jquery/jquery.min.js"></script>
    <script src="/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="/dist/js/adminlte.min.js"></script>
    <script src="/js/admin/admin.js"></script>
    <script src="//cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.js"></script>
    <script>
        $.ajax({
            type: "POST",
            url: "/api/webapi/admin/statistical",
            data: {

            },
            dataType: "json",
            success: function (response) {
                let usersOnline = (response.usersOnline) ? response.usersOnline : 0;
                let win = (response.win) ? response.win : 0;
                let loss = (response.loss) ? response.loss : 0;
                let usersOffline = (response.usersOffline) ? response.usersOffline : 0;
                let recharges = (response.recharges) ? response.recharges : 0;
                let withdraws = (response.withdraws) ? response.withdraws : 0;
                let rechargeToday = (response.rechargeToday) ? response.rechargeToday : 0;
                let withdrawToday = (response.withdrawToday) ? response.withdrawToday : 0;
                $('.totalMember').text((usersOnline));
                $('.totalWin').text((win));
                $('.totalLoss').text((loss));
                $('.totalMemberFail').text((usersOffline));
                $('.totalRecharge').text((recharges));
                $('.totalwithdraw').text((withdraws));
                $('.totalRechargeToday').text((rechargeToday));
                $('.totalwithdrawToday').text((withdrawToday));

                $('.totalToday').text((rechargeToday - withdrawToday));
                if (rechargeToday - withdrawToday < 0) {
                    $('.totalTodays').removeClass('bg-success');
                    $('.totalTodays').addClass('bg-danger');
                } else {
                    $('.totalTodays').addClass('bg-success');
                    $('.totalTodays').removeClass('bg-danger');

                }
            }
        });

        $('.buff-two').click(function (e) { 
            e.preventDefault();
            let select = $('#buff-two-ctv').val();
            let money = $('#money-buff-two').val();
            if (select && money) {
                $.ajax({
                    type: "POST",
                    url: "/admin/manager/createBonus",
                    data: {
                        select: select,
                        type: 'two',
                        money: money,
                    },
                    dataType: "json",
                    success: function (response) {
                        if (response.status == true) {
                            Swal.fire({
                                 icon: 'success',
                                title: `${response.message}`,
                            })
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: `${response.message}`,
                            })
                        }
                    }
                });
            } else {
                alert('Please enter complete information');
            }
        });

        $('.buff-three').click(function (e) { 
            e.preventDefault();
            let select = $('#buff-three').val();
            let money = $('#money-buff-three').val();
            let phone = $('#phone2').val();
            console.log(select);
            console.log(money);
            console.log(phone);
            if (select && money) {
                $.ajax({
                    type: "POST",
                    url: "/admin/manager/createBonus",
                    data: {
                        select: select,
                        type: 'three',
                        money: money,
                        phone: phone,
                    },
                    dataType: "json",
                    success: function (response) {
                        if (response.status == true) {
                            Swal.fire({
                                icon: 'success',
                                title: `${response.message}`,
                            })
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: `${response.message}`,
                            })
                        }
                    }
                });
            } else {
                alert('Please enter complete information');
            }
        });

        $('.buff-one').click(function (e) { 
            e.preventDefault();
            let select = $('#buff-one').val();
            let money = $('#money-buff-one').val();
            let phone = $('#phone').val();
            if (select && money) {
                $.ajax({
                    type: "POST",
                    url: "/admin/manager/createBonus",
                    data: {
                        select: select,
                        type: 'one',
                        money: money,
                        phone: phone,
                    },
                    dataType: "json",
                    success: function (response) {
                        if (response.status == true) {
                            Swal.fire({
                                icon: 'success',
                                title: `${response.message}`,
                            })
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: `${response.message}`,
                            })
                        }
                    }
                });
            } else {
                alert('Please enter complete information');
            }
        });

        $('.buff-all').click(function (e) { 
            e.preventDefault();
            let select = $('#buff-all-ctv').val();
            let money = $('#money-buff-all').val();
            if (select && money) {
                $.ajax({
                    type: "POST",
                    url: "/admin/manager/createBonus",
                    data: {
                        select: select,
                        type: 'all',
                        money: money,
                    },
                    dataType: "json",
                    success: function (response) {
                        if (response.status == true) {
                            Swal.fire({
                                icon: 'success',
                                title: `${response.message}`,
                            })
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: `${response.message}`,
                            })
                        }
                    }
                });
            } else {
                alert('Please enter complete information');
            }
        });

        $('.create_ctv').click(function (e) { 
            e.preventDefault();
            let username = $('#username').val().trim(); 
            let password = $('#password').val().trim();
            if (username && password) {
                $.ajax({
                    type: "POST",
                    url: "/admin/manager/create/ctv",
                    data: {
                        username: username,
                        password: password,
                    },
                    dataType: "json",
                    success: function (response) {
                        if (response.status == true) {
                            Swal.fire({
                                icon: 'success',
                                title: `${response.message}`,
                            })
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: `${response.message}`,
                            })
                        }
                    }
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Please enter complete information',
                })
            }
        });
    </script>
</body>

</html>