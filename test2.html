 <!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Daily Trade Volume Income</title>
  <link rel="stylesheet"
    href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />
  <link rel="stylesheet" href="/plugins/fontawesome-free/css/all.min.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" />
  <link href="//cdn.jsdelivr.net/npm/@sweetalert2/theme-dark@4/dark.css" rel="stylesheet" />
  <link rel="stylesheet" href="/dist/css/adminlte.min.css" />
  <link rel="stylesheet" href="/css/admin.css" />
  <style>
    .block-click {
      pointer-events: none;
    }
  </style>
</head>

<body class="hold-transition sidebar-mini">
  <div class="wrapper">
    <%- include('nav') %>
      <div class="content-wrapper">
        <section class="content-header">
          <div class="container-fluid">
            <div class="row mb-2">
              <div class="col-sm-6">
                <h1>Daily Trade Volume Income</h1>
              </div>
            </div>
          </div>
          <!-- /.container-fluid -->
        </section>
<!-- 
        <div class="form-group" style="text-align: center">
          <input type="text" id="search" placeholder="Enter the member you are looking for" />
        </div>
 -->
        <!-- Main content -->
        <section class="content">
          <!-- Default box -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">Daily Trade Volume Income</h3>
              <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse" title="Collapse">
                  <i class="fas fa-minus"></i>
                </button>
                <button type="button" class="btn btn-tool" data-card-widget="remove" title="Remove">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
            <div class="card-body p-0" style="overflow-y: hidden">
              <table class="table table-striped projects" id="table1">
                <thead>
                  <tr>
                    <th class="text-center">#</th>
         
                    <th class="text-center">Used By</td>
                    <th class="text-center">GiftCode</id>
                    
                    <th class="text-center">Amount</th>
                 
                    <th class="text-center">Date</th>
                  </tr>
                </thead>
                <tbody>
                  <!-- #region -->
                </tbody>
              </table>
            </div>
            <nav aria-label="Page navigation example" style="margin-top: 20px; display: flex; justify-content: center">
                <ul class="pagination table1">
                    <li class="page-item previous" id="previous">
                        <a class="page-link" href="#" tabindex="-1">Previous</a>
                    </li>
                    <div id="numbers" style="display: flex">
                        <li class="page-item">
                            <a class="page-link active text-white" id="text-page"></a>
                        </li>
                    </div>
                    <li class="page-item next" id="next">
                        <a class="page-link" href="#">Next</a>
                    </li>
                </ul>
            </nav>
          </div>
        </section>
      </div>
  </div> 
  <script src="/plugins/jquery/jquery.min.js"></script>
  <script src="/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="/dist/js/adminlte.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.js"></script>
  <script src="/js/admin/admin.js"></script> 
  <script>
    $("#search").on("keyup", function() {
        var value = $(this).val().toLowerCase();
        $("tbody tr").filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });
  </script>
   <script>
    const Render = (datas, pageno, limit) => {
      let html = '';
      datas.forEach((data, index) => {
        let serialNumber = pageno + index + 1;
        html += `
        <tr class="text-center" style="">
            <td>${serialNumber}</td>
            <td>${data.phone_used} <b style="color: #2003db">[ ${data.uid} ]</b></td>
            <td>${data.id_redenvelops}</td>
            <td>${data.money}</td>
            <td><b>${timerJoin(data.added_date_time)}</b></td>
        </tr>`;
      });
      $("tbody").html(html);
    };

    let pageno = 0;
    let limit = 30;
    let page = 1;

    const fetchGiftHistory = () => {
      $.ajax({
        type: "POST",
        url: "/admin/manager/getGiftHistory",
        data: {
          typeid: "1",
          pageno: pageno,
          limit: limit,
          language: "vi",
        },
        dataType: "json",
        success: function (response) {
          if (response.status === true) {
            Render(response.datas, pageno, limit);
            $('#text-page').text(page + ' / ' + response.page_total);
          }
        },
      });
    };

    $(document).ready(() => {
      fetchGiftHistory();

      $('#next').click(function (e) {
        e.preventDefault();
        pageno += limit;
        page++;
        fetchGiftHistory();
      });

      $('#previous').click(function (e) {
        e.preventDefault();
        pageno -= limit;
        if (pageno < 0) pageno = 0;
        else page--;
        fetchGiftHistory();
      });
    });

    function timerJoin(Reqdate) {
      let dateObj = new Date(Reqdate);
      dateObj.setDate(dateObj.getDate() + 1);
      let formattedNewDate = dateObj.toISOString().slice(0, 10);
      return formattedNewDate;
    }
</script>

</body>

</html>