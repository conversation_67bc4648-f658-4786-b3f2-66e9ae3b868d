<!DOCTYPE html>
<html translate="no" data-dpr="1" style="font-size: 48.16px" style="min-height: 100vh">

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="robots" content="noindex,nofollow" />
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport" />
    <title>Forgot</title>
    <link href="/css/member/main.css" rel="stylesheet" />
    <link href="/css/member/chunk-1.css" rel="stylesheet" />
    <link href="/css/member/chunk-2.css" rel="stylesheet" />
    <link href="/css/member/chunk-3.css" rel="stylesheet" />
    <link href="/css/member/chunk-4.css" rel="stylesheet" />
    <link href="/css/member/chunk-5.css" rel="stylesheet" />
    <link href="/css/member/chunk-6.css" rel="stylesheet" />
    <link href="/css/member/chunk-7.css" rel="stylesheet" />
    <link rel="shortcut icon" href="/images/bitbug_favicon.ico" type="image/x-icon" />
    <style>
        html,
        body {
            height: 100%;
            width: 100%;
            background-color: #090909;
            padding: 0;
            margin: 0;
        }

        .block-click {
            pointer-events: none;
        }
        .mian[data-v-51f72da1]{min-height:100vh;background-color:#292929}.mian .audio[data-v-51f72da1]{width:.66667rem;height:.66667rem}.mian.login[data-v-51f72da1]{padding-top:0}.mian .login-banner[data-v-51f72da1]{position:relative;background:url(../img/login_banner.34f15f30.png) no-repeat top;background-size:100% 100%;min-height:6.4rem;max-height:6.66667rem}.mian .login-banner .bankPage[data-v-51f72da1]{height:1.33333rem;width:1.33333rem}.mian .login-banner .bankPage .bank[data-v-51f72da1]{border:.05333rem solid #fff;width:.69333rem;height:.69333rem;border-radius:.69333rem}.mian .login-banner .logo[data-v-51f72da1]{position:absolute;top:50%;margin-top:-.53333rem;right:.4rem;width:3.2rem;height:1.06667rem}.mian .login-box[data-v-51f72da1]{position:relative;z-index:9;padding:.66667rem .8rem;border-radius:.53333rem .53333rem 0 0;margin-top:-.53333rem;background-color:#fff}.mian .login-box .tit[data-v-51f72da1]{text-align:center;font-size:.8rem;color:#670001}.mian .forgot-box[data-v-51f72da1]{width:100%;padding:.13333rem .8rem .8rem;border-top-left-radius:.66667rem;border-top-right-radius:.66667rem;position:relative}.mian-from[data-v-51f72da1]{margin-top:.4rem}.mian-from .lab[data-v-51f72da1]{font-size:.37333rem;color:#959595}.mian-from .tip[data-v-51f72da1]{margin:-.53333rem 0 .26667rem;font-size:.34667rem;color:#333}.mian-from .item[data-v-51f72da1]{position:relative;height:1.06667rem;line-height:.96rem;margin:.4rem 0 .8rem;border:.05333rem solid #f3c300;border-radius:.21333rem;overflow:hidden;padding-left:.88rem}.mian-from .item .pwd[data-v-51f72da1]{font-size:.32rem}.mian-from .item.first[data-v-51f72da1]{padding-left:1.6rem}.mian-from .item .img[data-v-51f72da1]{position:absolute;left:.26667rem;top:50%;transform:translateY(-50%)}.mian-from .item .mobile[data-v-51f72da1]{height:.53333rem;width:.37333rem}.mian-from .item .password[data-v-51f72da1]{height:.48rem;width:.42667rem}.mian-from .item .code[data-v-51f72da1]{height:.4rem;width:.48rem}.mian-from .item .number[data-v-51f72da1]{position:absolute;left:.26667rem;font-style:normal;font-size:.4rem;line-height:.96rem;padding-right:.08rem;color:#f4453f}.mian-from .item input[data-v-51f72da1]{background:transparent;color:#f3c300;border:none;width:100%;height:.96rem;font-size:.4rem}.mian-from .item .otp[data-v-51f72da1]{border:none;position:absolute;top:0;right:0;font-size:.37333rem;height:.96rem;border-radius:.13333rem;width:1.86667rem;text-align:center;line-height:.96rem;background-color:#ffebeb;border-color:#ffebeb!important;color:#000}.mian-from .agree[data-v-51f72da1]{line-height:.53333rem;height:.53333rem;color:#000;font-size:.4rem}.mian-from .txt[data-v-51f72da1]{font-size:.4rem;color:#f4453e}.mian-from .mian-btn .gradient[data-v-51f72da1]{width:50%;height:1.06667rem;margin:0 auto;border:none;border-radius:1.06667rem;color:#8f5206;background:-webkit-linear-gradient(top,#FAE59F 0%,#C4933F 100%);background:linear-gradient(180deg,#FAE59F 0%,#C4933F 100%)}.mian-from .mian-btn .gradient span[data-v-51f72da1]{font-size:.53333rem}.mian-from .mian-btn .text[data-v-51f72da1]{height:.53333rem;line-height:.53333rem;font-size:.4rem;color:#f4453f}.mian .AreaCode-box[data-v-51f72da1]{padding:.26667rem .4rem;border-bottom:.02667rem solid #eee}.mian .AreaCode-box .item .img[data-v-51f72da1]{display:block}.mian .AreaCode-box .item .right .chek[data-v-51f72da1]{height:.53333rem;width:.53333rem;line-height:.53333rem;text-align:center;border-radius:.53333rem;border:.05333rem solid #eee}.mian .AreaCode-box .item .right .chek.action[data-v-51f72da1]{background-color:#f2413b;border:.05333rem solid #f2413b}
    </style>
</head>

<body>
    <div id="app">
        <div data-v-51f72da1="" class="mian forgot">
            <div data-v-106b99c8="" data-v-51f72da1="" class="navbar">
                <div data-v-106b99c8="" class="navbar-left" onclick="location.href='/mian'">
                    <div data-v-106b99c8="" class="bank c-row c-row-middle-center">
                        <img data-v-106b99c8="" src="/images/back.c3244ab0.png" class="navbar-back" />
                    </div>
                </div>
                <div data-v-106b99c8="" class="navbar-title">Reset Password</div>
                <div data-v-106b99c8="" class="navbar-right">
                    <div data-v-51f72da1="" data-v-106b99c8="" class="c-row">
                        <img data-v-51f72da1="" data-v-106b99c8="" src="/images/audio.webp" class="audio" />
                    </div>
                </div>
            </div>
            <div data-v-51f72da1="" class="forgot-box">
                <div data-v-51f72da1="" class="mian-from m-t-20">
                    <div data-v-51f72da1="" class="item c-row c-row-center c-row-middle m-b-30">
                        <span data-v-51f72da1="" class="img c-row c-row-middle-center">
                            <img data-v-51f72da1="" height="20px" width="18px" src="../index_files/password-b827c2b3.png" class="password" />
                        </span>
                        <input data-v-d8986e5e="" data-v-51f72da1="" id="password" placeholder="Old Password" οnkeyup="value=value.replace(/[^\w\.\/]/ig,'')" class="pw-input" />
                    </div>
                    <!-- <div data-v-51f72da1="" class="item c-row c-row-center c-row-middle m-b-30">
                        <span data-v-51f72da1="" class="img c-row c-row-middle-center">
                            <img data-v-51f72da1="" height="22px" width="15px" src="/images/smart_phone.png" class="mobile">
                        </span>
                        <input data-v-51f72da1="" type="number" id="otp" placeholder="Verification">
                        <button data-v-51f72da1="" class="otp block-click">OTP</button>
                    </div>
                    <div data-v-51f72da1="" class="tip">Not receiving OTP code? Please contact customer support.</div> -->
                    <div data-v-51f72da1="" class="item c-row c-row-center c-row-middle m-b-30">
                        <span data-v-51f72da1="" class="img c-row c-row-middle-center">
                            <img data-v-51f72da1="" height="20px" width="18px" src="../index_files/password-b827c2b3.png" class="password" />
                        </span>
                        <input type="text" data-v-d8986e5e="" data-v-51f72da1="" id="newPassWord" placeholder="New Password" οnkeyup="value=value.replace(/[^\w\.\/]/ig,'')" class="pw-input" />
                    </div>
                    <div data-v-51f72da1="" class="item c-row c-row-center c-row-middle m-b-30">
                        <span data-v-51f72da1="" class="img c-row c-row-middle-center">
                            <img data-v-51f72da1="" height="20px" width="18px" src="../index_files/password-b827c2b3.png" class="password" />
                        </span>
                        <input type="text" data-v-d8986e5e="" data-v-51f72da1="" id="RePassWord" placeholder="Confirm Password" οnkeyup="value=value.replace(/[^\w\.\/]/ig,'')" class="pw-input" />
                    </div>
                    <div data-v-51f72da1="" class="mian-btn m-t-40">
                        <button data-v-51f72da1="" id="submit" class="gradient van-button van-button--default van-button--normal van-button--block van-button--round">
                            <div data-v-51f72da1="" class="van-button__content">
                                <span data-v-51f72da1="" class="van-button__text">
                                    <span data-v-51f72da1="">Confirm</span>
                                </span>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
            <!---->
            <div data-v-7692a079="" data-v-51f72da1="" class="Loading c-row c-row-middle-center" style="display: none">
                <div data-v-7692a079="" class="van-loading van-loading--circular">
                    <span data-v-7692a079="" class="van-loading__spinner van-loading__spinner--circular" style="width: auto; height: auto; -webkit-animation-duration: 1s; animation-duration: 1s;">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 200 200" width="200" height="200" preserveAspectRatio="xMidYMid meet" style="width: 100%;height: 100%;transform: translate3d(0px, 0px, 0px);content-visibility: visible;">
                            <g transform="matrix(-0.5984085202217102,0.8011911511421204,-0.8011911511421204,-0.5984085202217102,239.9599609375,79.72173309326172)" style="display: block;">
                                <image width="200px" height="200px" preserveAspectRatio="xMidYMid slice" xlink:href="/index_files/loadingspinner.png"></image>
                            </g>
                            <g transform="matrix(0.9408400058746338,0,0,0.9408400058746338,20.969436645507812,79.30152130126953)" style="display: block;">
                                <image width="168px" height="44px" preserveAspectRatio="xMidYMid slice" xlink:href="/index_files/h5setting_202401100608011fs2.png"></image>
                            </g>
                        </svg>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="/js/client.js"></script>
    <script>
        function alertMess(text, sic) {
            $('body').append(
                `
                <div data-v-1dcba851="" class="msg">
                    <div data-v-1dcba851="" class="msg-content v-enter-active v-enter-to" style=""> ${text} </div>
                </div>
                `
            );
            setTimeout(() => {
                $('.msg .msg-content').removeClass('v-enter-active v-enter-to');
                $('.msg .msg-content').addClass('v-leave-active v-leave-to');
                setTimeout(() => {
                    $('.msg').remove();
                }, 100);
                sic.removeClass('block-click');
            }, 1000);
        }
        $('#submit').click(function (e) { 
            e.preventDefault();
            let password = $('#password').val().trim();
            let newPassWord = $('#newPassWord').val().trim();
            let RePassWord = $('#RePassWord').val().trim();
            // let otp = $('#otp').val().trim();
            if (password && newPassWord && RePassWord && RePassWord == newPassWord) {
                $.ajax({
                    type: "PUT",
                    url: "/api/webapi/change/pass",
                    data: {
                        password: password,
                        newPassWord: newPassWord,
                        RePassWord: RePassWord,
                    },
                    dataType: "json",
                    success: function (response) {
                        alertMess(`${response.message}`, $(this));
                        if (response.status === true) {
                            setTimeout(() => {
                                history.back();
                            }, 1000);
                        }
                    }
                });
            } else if (!password || !newPassWord || !RePassWord) {
                $(this).addClass('block-click');
				alertMess('Please enter all required information', $(this));
            } else {
                $(this).addClass('block-click');
				alertMess('Incorrect password confirmation', $(this));
            }
        });

        // function timer(endTime) {
        //     let timeClocks = document.querySelector('.otp');
        //     let getTimeEnd = Number(endTime);
        //     let tet = new Date(getTimeEnd).getTime();
        //     let countDowns = setInterval(run,0);
        //     function formatTime(time) {
        //         return (time <= 9) ? "0" + time : time;
        //     } 
        //     function run(){
        //         var now = new Date().getTime();
        //         var timeRest = tet - now;
        //         var sec = Math.floor(timeRest%(1000*120)/(1000));
        //         timeClocks.innerHTML = formatTime(sec) + "S";
        //         if(timeRest <= 0) {
        //             clearInterval(countDowns);
        //             $('.otp').removeClass('block-click');
        //             timeClocks.innerHTML = "OTP";
        //         }              
        //     }
        // }
        // timer("<%=time%>");
        // $('.otp').click(function (e) { 
        //     e.preventDefault();
        //     $(this).addClass('block-click');
        //     let wi = $(this);
        //     $.ajax({
        //         type: "POST",
        //         url: "/api/webapi/otp",
        //         data: {
                    
        //         },
        //         dataType: "json",
        //         success: function (response) {
        //             alertMess(response.message, wi);
        //             if (response.status === true) {
        //                 setTimeout(() => {
        //                     location.reload();
        //                 }, 400);
        //             }
        //         }
        //     });
        // });
    </script>
</body>

</html>