<!DOCTYPE html>
<html translate="no" data-dpr="1" style="font-size: 48.16px;">

<head>
    <meta charset="UTF-8">
    <link rel="icon" type="image/svg+xml" href="/logo.png">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeltInWin</title>
    <!-- Foontawsome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
    <link rel="stylesheet" crossorigin="" href="/index_files/index-BUBUniRp.css">
    <style id="_goober">
        @keyframes go2264125279 {
            from {
                transform: scale(0) rotate(45deg);
                opacity: 0;
            }
            to {
                transform: scale(1) rotate(45deg);
                opacity: 1;
            }
        }
        
        @keyframes go3020080000 {
            from {
                transform: scale(0);
                opacity: 0;
            }
            to {
                transform: scale(1);
                opacity: 1;
            }
        }
        
        @keyframes go463499852 {
            from {
                transform: scale(0) rotate(90deg);
                opacity: 0;
            }
            to {
                transform: scale(1) rotate(90deg);
                opacity: 1;
            }
        }
        
        @keyframes go1268368563 {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }
        
        @keyframes go1310225428 {
            from {
                transform: scale(0) rotate(45deg);
                opacity: 0;
            }
            to {
                transform: scale(1) rotate(45deg);
                opacity: 1;
            }
        }
        
        @keyframes go651618207 {
            0% {
                height: 0;
                width: 0;
                opacity: 0;
            }
            40% {
                height: 0;
                width: 6px;
                opacity: 1;
            }
            100% {
                opacity: 1;
                height: 10px;
            }
        }
        
        @keyframes go901347462 {
            from {
                transform: scale(0.6);
                opacity: 0.4;
            }
            to {
                transform: scale(1);
                opacity: 1;
            }
        }
        
        .go4109123758 {
            z-index: 9999;
        }
        
        .go4109123758>* {
            pointer-events: auto;
        }
        
        .go2072408551 {
            display: flex;
            align-items: center;
            background: #fff;
            color: #363636;
            line-height: 1.3;
            will-change: transform;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
            max-width: 350px;
            pointer-events: auto;
            padding: 8px 10px;
            border-radius: 8px;
        }
        
        .go685806154 {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            min-width: 20px;
            min-height: 20px;
        }
        
        .go1858758034 {
            width: 12px;
            height: 12px;
            box-sizing: border-box;
            border: 2px solid;
            border-radius: 100%;
            border-color: #e0e0e0;
            border-right-color: #616161;
            animation: go1268368563 1s linear infinite;
        }
        
        .go1579819456 {
            position: absolute;
        }
        
        .go2344853693 {
            width: 20px;
            opacity: 0;
            height: 20px;
            border-radius: 10px;
            background: #61d345;
            position: relative;
            transform: rotate(45deg);
            animation: go1310225428 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
            animation-delay: 100ms;
        }
        
        .go2344853693:after {
            content: '';
            box-sizing: border-box;
            animation: go651618207 0.2s ease-out forwards;
            opacity: 0;
            animation-delay: 200ms;
            position: absolute;
            border-right: 2px solid;
            border-bottom: 2px solid;
            border-color: #fff;
            bottom: 6px;
            left: 6px;
            height: 10px;
            width: 6px;
        }
        
        .go3958317564 {
            display: flex;
            justify-content: center;
            margin: 4px 10px;
            color: inherit;
            flex: 1 1 auto;
            white-space: pre-line;
        }
        
        @keyframes go3223188581 {
            0% {
                transform: translate3d(0, -200%, 0) scale(.6);
                opacity: .5;
            }
            100% {
                transform: translate3d(0, 0, 0) scale(1);
                opacity: 1;
            }
        }
        
        @keyframes go502128938 {
            0% {
                transform: translate3d(0, 0, -1px) scale(1);
                opacity: 1;
            }
            100% {
                transform: translate3d(0, -150%, -1px) scale(.6);
                opacity: 0;
            }
        }
    </style>
</head>

<body style="font-size: 12px;">
    <div id="root">
        <div class="">
            <a href="/customerSupport">
                <div class="iconrob" style="position: fixed; right: 0px; bottom: 0px; width: 40px; height: 40px;"><img src="/images/icon-BMOz9JeZ.png" alt="Draggable Icon" style="position: absolute; left: -50px; top: -50px; cursor: move;"></div>
            </a>
        </div>
        <div class="mainApp">
            <div style="position: fixed;z-index: 9999;inset: 16px;pointer-events: none;margin: 0;max-width: 423px;padding: 0;"></div>
            <div class="container-fluid subordinate headertop  bg-white rounded-0 " style="
    background: var(--main_gradient-color);
    color: #fff !important;
">
                <div class="row justify-content-between align-items-center">
                    <div class="col-4 px-0"><a class="btn hedbtn text-black shadow-none" href="/accounts"><span><i class="fa-solid fa-angle-left text-center text-black"></i></span></a></div>
                    <div class="col-4 px-0">
                        <div class="headertext text-center fw-bold text-black">Withdraw</div>
                    </div>
                    <div class="col-4 px-0"><a class="headertext2 text-center fw-normal text-decoration-none" href="/withdrawhistory" style="
    color: #f7f6f6;
"><small>Withdrawal History</small></a></div>
                </div>
            </div>
            <div class="container-fluid withdrawCard my-2 mb-2 pb-2" style="
    padding: 0px 11px;
">
                <div class="container">
                    <div class="row" style="
">
                        <div class="col-12 px-1 px-sm" style="
    background: var(--main_gradient-color);
    border-radius: 11px;
">
                            <div class="card innercard border-0 shadow-none" style="
    background: var(--main_gradient-color);
">
                                <div class="card-body" style="
    padding: 11px 2px;
">
                                    <div class="heading text-white mb-2"><span><i class="fa-solid fa-wallet me-1"></i></span>Wallet balance</div>
                                    <div class="walletpayment text-white fw-bold " style="
    margin-left: 16px;
    margin-top: -18px;
">₹4255.39</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row my-1">
                        <ul class="nav nav-pills ms-0 mb-3 gap-2" id="pills-tab" role="tablist">
                            <li class="nav-item" role="presentation"><button class="nav-link active" id="pills-USDT-tab" data-bs-toggle="pill" data-bs-target="#pills-USDT" type="button" role="tab" aria-controls="pills-USDT" aria-selected="false"><div class="imagetext"><div class="image"><img class="w-100 h-100" src="data:image/png;base64,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" alt=""></div><div class="text">USDT</div></div></button></li>
                            <li class="nav-item" role="presentation"><button class="nav-link " id="pills-BankCard-tab" data-bs-toggle="pill" data-bs-target="#pills-BankCard" type="button" role="tab" aria-controls="pills-BankCard" aria-selected="true"><div class="imagetext"><div class="image"><img class="w-100 h-100 imgfil" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEYAAABGCAYAAABxLuKEAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAALPSURBVHgB7ZvZkdNAEIb/lgnARIAywERgEwFksEsE6wOe2Xfw2kSAyGBDEBHgjQCRgQjAalqHqyh7Woflha1Rfw+WNIfK89dojp5uwDAMwzAMwzCGALUtyPP5GDR6A+aJPIZSc1JlhXiapPJfk+IK2oF4hyCIab3+1aZyozAiSAgEGyn5Fj7AiDCi2yaBaoXh5fKlvCiW2zH8IpWWz+ju7kEroApT9BQKfsA/UQ6kCGii9ZxArxd8hL+i5IyxzyIt09ljqt7yE0OAs+e03abHye4eQzTFUKDRlSvZLQzTDEOhXH6coPWYEEOBqIMw4BCDgZ0TjDYr+TwbHWPCKHQSZvCYMAomjIImTIrh4GyrssDDFkOBce9KVnpM9kWr4BcUS1sXzpy6ajx/P5OKIbwkSGj7OYZhGMZ/oH7wXSzEYBWE8JIsoc3mu5armzZBX8VWMYPPsJw1gV+3N23mhnDfRckpjFRyZuZAseB5crjWBqWtZo8xe0w3TBgFE0bBhFEwYRRMGAUTRsGEUXiGPpS24YeaElNZWV4fpSWy3NzKPuU3uuN636PQQxi6pu36W0OhiBcfImAfV88JOHvl2rS1JOL5MncTm+OROfdTSmnTKEoBbT7lW/ukeGBEPUSpEEP9P+Dcc6WujRsfXftw6X1ch3Ol5oaHYsS6Qgt4sbrBoTEyPvBq9QK9CG5wWZxtdY8xzPIdN7kAU/69z3D4TNyIvYP/3tbnDoGx1ItwFjzL3VBxSUon6ROUwZfEsoUJmjhnhii9tW5xFq0d2bu8M3Glaq5mOwwFyk8jT1HOrvetZhwvCArPd0eyg3JKdSvpFfnyobNnOO/fwW93kDQPttAyVWGk1yTVDOCjOGWQRU0ESu3Kt4jOkCV8EcriDTJE5MEVNZEnRSm0pIwvGE0LT+rSaXhc+QM/1ROFpPhl7Kp7ESK7778lMQzDMAzDMAyf+ANvUMciKd3CqQAAAABJRU5ErkJggg==" alt=""></div><div class="text">Bank Card</div></div></button></li>
                            <li class="nav-item" role="presentation">
                                <a class="nav-link" type="button" role="tab" aria-selected="false" href="/Updatecurrency">
                                    <div class="imagetext">
                                        <div class="image"><img class="w-100 h-100" src="data:image/png;base64,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"
                                                alt=""></div>
                                        <div class="text">View USDT</div>
                                    </div>
                                </a>
                            </li>
                            <li class="nav-item" role="presentation">
                                <a class="nav-link" type="button" role="tab" aria-selected="false" href="/Updatebank">
                                    <div class="imagetext">
                                        <div class="image"><img class="w-100 h-100" src="/images/bank-DZRsnqdj.png" alt=""></div>
                                        <div class="text">View Bank</div>
                                    </div>
                                </a>
                            </li>
                        </ul>
                        <div class="tab-content" id="pills-tabContent" style="
    padding: 0px 0px;
">
                            <div class="tab-pane fade " id="pills-BankCard" role="tabpanel" aria-labelledby="pills-BankCard-tab" tabindex="0">
                                <div class="row g-3 mx-0 conactusdt">
                                    <div class="col-12">
                                        <div class="card border-0">
                                            <div class="card-body p-2">
                                                <div class="inputcontrol mb-2">
                                                    <div class="icon">₹</div><input class="form-control shadow-none border-0 ps-5" type="text" placeholder="Please enter withdrawal amount" value=""></div>
                                            </div>
                                            <div class="d-flex align-items-center gap-3 position-relative">
                                                <div class="icon ico2"><i class="fa-solid fa-lock text-theme1 pe-2"></i></div><input type="password" class="form-control shadow-none border-0 ps-5" id="exampleFormControlInputpasswoerd" placeholder="Enter Password" maxlength="20"
                                                    value=""><button type="button" style="margin-left: 10px; border-width: 0px;">👁️</button></div>
                                        </div>
                                    </div>
                                    <div class="col-12"><button type="button" class="btn text-white text-center w-100 py-2 rounded-pill maingridentcolor1">Withdraw</button></div>
                                </div>
                                <div class="container-fluid withhdrawlist mt-4">
                                    <div class="container">
                                        <div class="row g-2">
                                            <div class="col-12">
                                                <div class="list">
                                                    <div class="headinglist"><span><i class="fa-solid fa-money-bill-trend-up"></i></span>1. Withdrawal Limit 110 ₹ to 1Cr ₹ Maximum.</div>
                                                </div>
                                                <div class="list">
                                                    <div class="headinglist"><span><i class="fa-solid fa-wallet"></i></span>2. Remaining Betting Amount For Withdrawal: ₹ 36</div>
                                                </div>
                                                <div class="list">
                                                    <div class="headinglist"><span><i class="fa-solid fa-coins"></i></span>3. Withdrawal 24/7</div>
                                                </div>
                                                <div class="list">
                                                    <div class="headinglist"><span><i class="fa-solid fa-hand-holding-dollar"></i></span>4. Daily Withdrawal Limit: 3 Time</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade show active" id="pills-USDT" role="tabpanel" aria-labelledby="pills-USDT-tab" tabindex="0">
                                <div class="row g-3 mx-0 conactusdt">
                                    <div class="col-12" style="
    padding: 0px 3px;
    margin-top: 13px;
">
                                        <div class="card border-0">
                                            <div class="card-body p-2">
                                                <div class="heading mb-1"><span><img class="w-100 h-100" src="data:image/png;base64,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" alt=""></span>Select
                                                    amount of USDT</div>
                                                <div class="inputcontrol mb-1">
                                                    <div class="icon">₹</div><input class="form-control shadow-none border-0 ps-3" type="text" placeholder="Please enter withdrawal amount" value="" style="font-size: 16px;padding: 0px;margin: 0;"></div>
                                                <div class="inputcontrol"><label for="turnover text-theme1"></label>
                                                    <div class="icon ico2" style="
    top: 29px;
">$</div><input id="turnover" class="form-control shadow-none border-0 ps-3 " type="text" placeholder="UDST amount" disabled="" value="" style="
    font-size: 16px;
"></div>
                                            </div>
                                            <div class="d-flex align-items-center gap-3 position-relative" style="
    padding: 16px 34px;
">
                                                <div class="icon ico2"><i class="fa-solid fa-lock text-theme1 pe-2"></i></div><input type="password" class="form-control shadow-none border-0" id="exampleFormControlInputpasswoerd" placeholder="Enter Password" maxlength="20" value=""
                                                    style="
    font-size: 16px;
    padding: 0;
    margin: 0;
"><button type="button" style="margin-left: 10px; border-width: 0px;">👁️</button></div>
                                        </div>
                                    </div>
                                    <div class="col-12" style="
    margin: 17px 0px;
"><button type="button" class="btn text-white text-center w-100 py-1 rounded-pill maingridentcolor1" style="
">Withdraw</button></div>
                                </div>
                                <div class="container-fluid withhdrawlist mt-1" style="
">
                                    <div class="container">
                                        <div class="row g-2">
                                            <div class="col-12">
                                                <div class="list">
                                                    <div class="headinglist"><span><i class="fa-solid fa-money-bill-trend-up"></i></span>1. Withdrawal Limit $900 to $1Cr Maximum.</div>
                                                </div>
                                                <div class="list">
                                                    <div class="headinglist"><span><i class="fa-solid fa-wallet"></i></span>2. Remaining Betting Amount For Withdrawal: ₹ 36</div>
                                                </div>
                                                <div class="list">
                                                    <div class="headinglist"><span><i class="fa-solid fa-coins"></i></span>3. Withdrawal 24/7</div>
                                                </div>
                                                <div class="list">
                                                    <div class="headinglist"><span><i class="fa-solid fa-hand-holding-dollar"></i></span>4. Daily Withdrawal Limit: 3 Time</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade show" id="pills-USDN" role="tabpanel" aria-labelledby="pills-USDN-tab" tabindex="0">
                                <div class="row g-3 mx-0 conactusdt">
                                    <div class="col-12">
                                        <div class="card border-0">
                                            <div class="card-body p-2">
                                                <div class="heading mb-2"><span><img class="w-100 h-100" src="/images/usdn-DSlTlwPc.png" alt=""></span>Select amount of USDN</div>
                                                <div class="inputcontrol mb-2">
                                                    <div class="icon">₹</div><input class="form-control shadow-none border-0 ps-5" type="text" placeholder="Please enter withdrawal amount" value=""></div>
                                                <div class="inputcontrol"><label for="turnover text-theme1"></label>
                                                    <div class="icon ico2">$</div><input id="turnover" class="form-control shadow-none border-0 ps-5" type="text" placeholder="UDST amount" disabled="" value=""></div>
                                            </div>
                                            <div class="d-flex align-items-center gap-3 position-relative">
                                                <div class="icon ico2"><i class="fa-solid fa-lock text-theme1 pe-2"></i></div><input type="password" class="form-control shadow-none border-0 ps-5" id="exampleFormControlInputpasswoerd" placeholder="Enter Password" maxlength="20"
                                                    value=""><button type="button" style="margin-left: 10px; border-width: 0px;">👁️</button></div>
                                        </div>
                                    </div>
                                    <div class="col-12"><button type="button" class="btn text-white text-center w-100 py-2 rounded-pill maingridentcolor1">Withdraw</button></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <footer class="container-fluid footermain mainApp px-1">
                <div class="row w-100">
                    <div class="col-12 px-1 w-100">
                        <div class="footerlist">
                            <a class="tabbar__container-item text-decoration-none active" href="/">
                                <div class="icon"><img class="w-100 h-100" src="data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewBox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20opacity='0.3'%20d='M27%2046C36.9411%2046%2045%2037.9411%2045%2028C45%2018.0589%2036.9411%2010%2027%2010C17.0589%2010%209%2018.0589%209%2028C9%2037.9411%2017.0589%2046%2027%2046Z'%20fill='black'/%3e%3cpath%20d='M23.6599%205.27799L5.76039%2017.3574V42.8644H15.8404V32.4244C15.8404%2029.6409%2018.0969%2027.3844%2020.8804%2027.3844H27.2404C30.0239%2027.3844%2032.2804%2029.6409%2032.2804%2032.4244V36.8044H28.9204V32.4244C28.9204%2031.4965%2028.1682%2030.7444%2027.2404%2030.7444H20.8804C19.9526%2030.7444%2019.2004%2031.4965%2019.2004%2032.4244V43.5844C19.2004%2045.0424%2018.0184%2046.2244%2016.5604%2046.2244H5.04039C3.58236%2046.2244%202.40039%2045.0424%202.40039%2043.5844V16.9747C2.40039%2016.0973%202.83631%2015.2772%203.56361%2014.7863L22.4377%202.04925C23.1552%201.56501%2024.0926%201.55594%2024.8194%202.02622L44.5146%2014.7702C45.2664%2015.2567%2045.7204%2016.0911%2045.7204%2016.9866V43.5844C45.7204%2045.0424%2044.5384%2046.2244%2043.0804%2046.2244H30.6004V42.8644H42.3604V17.3783L23.6599%205.27799Z'%20fill='black'/%3e%3cpath%20d='M32.4%2044.5443C32.4%2045.4722%2031.6478%2046.2243%2030.72%2046.2243C29.7922%2046.2243%2029.04%2045.4722%2029.04%2044.5443C29.04%2043.6165%2029.7922%2042.8643%2030.72%2042.8643C31.6478%2042.8643%2032.4%2043.6165%2032.4%2044.5443Z'%20fill='black'/%3e%3cpath%20d='M32.2799%2036.7445C32.2799%2037.6724%2031.5277%2038.4245%2030.5999%2038.4245C29.6721%2038.4245%2028.9199%2037.6724%2028.9199%2036.7445C28.9199%2035.8167%2029.6721%2035.0645%2030.5999%2035.0645C31.5277%2035.0645%2032.2799%2035.8167%2032.2799%2036.7445Z'%20fill='black'/%3e%3c/svg%3e"
                                        alt=""></div><span>Home</span></a>
                            <a class="tabbar__container-item text-decoration-none" href="/Activity">
                                <div class="icon"><img class="w-100 h-100" src="data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewBox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20opacity='0.3'%20d='M27%2042C36.9411%2042%2045%2033.9411%2045%2024C45%2014.0589%2036.9411%206%2027%206C17.0589%206%209%2014.0589%209%2024C9%2033.9411%2017.0589%2042%2027%2042Z'%20fill='black'/%3e%3cpath%20d='M17.4489%2016.6808C17.4491%2016.6697%2017.4492%2016.6586%2017.4492%2016.6475C17.4492%2015.681%2016.6833%2014.8975%2015.7168%2014.8975C14.7529%2014.8975%2013.9263%2015.6767%2013.9219%2016.6396C13.9222%2016.6432%2013.9215%2016.6469%2013.9219%2016.6505C13.9221%2016.7723%2013.9355%2016.8911%2013.9606%2017.0059C14.5925%2021.9877%2018.8462%2025.8397%2023.9996%2025.8397C29.1037%2025.8397%2033.3252%2022.0611%2034.0195%2017.1487C34.0666%2016.9905%2034.0918%2016.8229%2034.0918%2016.6494C34.0918%2015.6829%2033.3083%2014.8994%2032.3418%2014.8994C31.3753%2014.8994%2030.5469%2015.6829%2030.5469%2016.6494C30.5469%2016.6662%2030.5471%2016.6829%2030.5476%2016.6996C30.0741%2019.8911%2027.3228%2022.3397%2023.9996%2022.3397C20.67%2022.3397%2017.9144%2019.8815%2017.4489%2016.6808Z'%20fill='black'/%3e%3cpath%20d='M10.5119%205.2H37.487C40.0599%205.2%2042.1753%207.22874%2042.2828%209.79945L42.793%2022H42.7995C42.7995%2022.8836%2043.5158%2023.6005%2044.3995%2023.6005C45.2832%2023.6005%2045.9995%2022.8841%2045.9995%2022.0005C45.9995%2021.9341%2045.9955%2021.8686%2045.9876%2021.8044L45.48%209.66575C45.3008%205.38123%2041.7752%202%2037.487%202H10.5119C6.22361%202%202.69803%205.38123%202.51886%209.66575L1.34795%2037.6657C1.15787%2042.2112%204.79154%2046%209.34096%2046H38.6579C43.2073%2046%2046.841%2042.2112%2046.6509%2037.6657L46.3303%2030C46.3069%2029.1368%2045.5993%2028.4442%2044.7304%2028.4442C43.8615%2028.4442%2043.1544%2029.1368%2043.131%2030H43.1275L43.1307%2030.0763C43.1312%2030.1004%2043.1322%2030.1243%2043.1337%2030.148L43.4537%2037.7994C43.5677%2040.5267%2041.3875%2042.8%2038.6579%2042.8H9.34096C6.61131%2042.8%204.43111%2040.5267%204.54516%2037.7994L5.71607%209.79945C5.82357%207.22874%207.93892%205.2%2010.5119%205.2Z'%20fill='black'/%3e%3c/svg%3e"
                                        alt=""></div><span>Activity</span></a>
                            <a class="tabbar__container-item text-decoration-none " href="/promotion">
                                <div class="icon diamondimage">
                                    <div class="image"><svg id="icon-promotion" viewBox="0 0 57 49" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.93876 1.50122C9.69785 0.55236 10.8471 0 12.0622 0H44.2172C45.4324 0 46.5816 0.552359 47.3407 1.50122L55.0792 11.1744C55.5056 11.7073 55.828 12.2943 56.0469 12.9092H0.232598C0.451468 12.2943 0.773925 11.7073 1.20023 11.1744L8.93876 1.50122ZM0 16.091H56.2795C56.0896 17.0496 55.664 17.9709 55.0034 18.7637L31.2126 47.3125C29.6134 49.2316 26.666 49.2316 25.0669 47.3125L1.27612 18.7637C0.615521 17.9709 0.189841 17.0496 0 16.091ZM20.5563 22.0266L27.7513 32.1286C27.9512 32.4093 28.3685 32.4083 28.5671 32.1267L35.6853 22.0338C36.1425 21.3856 36.8863 21 37.6795 21C39.0272 21 40.1198 22.0925 40.1198 23.4403V23.6393H39.8972C39.5712 23.6393 39.1148 23.8877 38.5931 24.5708C38.0874 25.2331 32.1271 33.2938 28.9417 37.6047C28.7578 37.8535 28.467 38 28.1577 38C27.8515 38 27.5632 37.8562 27.379 37.6117L17.3204 24.2603C17.3204 24.2603 16.9258 23.6393 16.2608 23.6393H16.1198V23.445C16.1198 22.0947 17.2144 21 18.5648 21C19.3556 21 20.0975 21.3825 20.5563 22.0266Z" fill="white"></path></svg></div>
                                </div><span class="permotiontext">Promotion</span></a>
                            <a class="tabbar__container-item text-decoration-none" href="/wallet">
                                <div class="icon"><img src="data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewBox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20opacity='0.3'%20d='M28%2042C37.9411%2042%2046%2033.9411%2046%2024C46%2014.0589%2037.9411%206%2028%206C18.0589%206%2010%2014.0589%2010%2024C10%2033.9411%2018.0589%2042%2028%2042Z'%20fill='black'/%3e%3cpath%20d='M3%2023C3%2017.016%206.526%2012.836%2012.0085%2012.132C12.5675%2012.044%2013.148%2012%2013.75%2012H35.25C35.809%2012%2036.3465%2012.022%2036.8625%2012.11C42.4095%2012.77%2046%2016.972%2046%2023V34C46%2040.6%2041.7%2045%2035.25%2045H13.75C7.3%2045%203%2040.6%203%2034V31.822'%20stroke='black'/%3e%3cpath%20d='M46%2023.7241H39.548C37.1822%2023.7241%2035.2466%2025.5862%2035.2466%2027.8621C35.2466%2030.1379%2037.1822%2032%2039.548%2032H46M37%2012C36.4838%2011.9172%2035.8058%2012%2035.2466%2012H14C13.3978%2012%2012.5592%2011.9172%2012%2012C12%2012%2012.7312%2011.3517%2013.2474%2010.8551L20.2371%204.11027C21.6566%202.75836%2023.5733%202%2025.5708%202C27.5682%202%2029.4849%202.75836%2030.9044%204.11027L34.6681%207.77235C36.0445%209.0758%2039.548%2012%2037%2012Z'%20stroke='black'/%3e%3c/svg%3e"
                                        alt="" class="w-100 h-100"></div><span>Wallet</span></a>
                            <a class="tabbar__container-item text-decoration-none" href="/accounts">
                                <div class="icon"><img src="data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewBox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20opacity='0.3'%20d='M28%2042C37.9411%2042%2046%2033.9411%2046%2024C46%2014.0589%2037.9411%206%2028%206C18.0589%206%2010%2014.0589%2010%2024C10%2033.9411%2018.0589%2042%2028%2042Z'%20fill='black'/%3e%3cpath%20d='M24.08%205.27992C13.7412%205.27992%205.36%2013.6612%205.36%2023.9999C5.36%2034.3387%2013.7412%2042.7199%2024.08%2042.7199C34.4188%2042.7199%2042.8%2034.3387%2042.8%2023.9999V15.2399H46.16V23.9999C46.16%2036.1944%2036.2744%2046.0799%2024.08%2046.0799C11.8856%2046.0799%202%2036.1944%202%2023.9999C2%2011.8055%2011.8856%201.91992%2024.08%201.91992H44.36V5.27992H24.08Z'%20fill='black'/%3e%3cpath%20d='M46.1598%203.59992C46.1598%204.52776%2045.4076%205.27992%2044.4798%205.27992C43.552%205.27992%2042.7998%204.52776%2042.7998%203.59992C42.7998%202.67208%2043.552%201.91992%2044.4798%201.91992C45.4076%201.91992%2046.1598%202.67208%2046.1598%203.59992Z'%20fill='black'/%3e%3cpath%20d='M46.1598%2015.1195C46.1598%2016.0474%2045.4076%2016.7995%2044.4798%2016.7995C43.552%2016.7995%2042.7998%2016.0474%2042.7998%2015.1195C42.7998%2014.1917%2043.552%2013.4395%2044.4798%2013.4395C45.4076%2013.4395%2046.1598%2014.1917%2046.1598%2015.1195Z'%20fill='black'/%3e%3cpath%20d='M15.8064%2029.5825C16.501%2028.9674%2017.5627%2029.0317%2018.1779%2029.7263C19.3275%2031.0242%2020.9265%2032.5202%2023.6403%2032.5202C26.5117%2032.5202%2028.4971%2031.0925%2029.4448%2029.9868C30.0486%2029.2824%2031.1092%2029.2008%2031.8136%2029.8046C32.5181%2030.4085%2032.5997%2031.469%2031.9959%2032.1735C30.5435%2033.8679%2027.6809%2035.8802%2023.6403%2035.8802C19.4421%2035.8802%2016.9931%2033.4562%2015.6627%2031.9541C15.0475%2031.2595%2015.1118%2030.1977%2015.8064%2029.5825Z'%20fill='black'/%3e%3c/svg%3e"
                                        alt="" class="w-100 h-100"></div><span>Account</span></a>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="/js/client.js"></script>
    <script>
        $.ajax({
            type: "GET",
            url: "/api/webapi/GetUserInfo",
            dataType: "json",
            success: function(response) {
                let user = response.data;

                $('#accountName').text(user.banks.name_user);
                $('#accountNumber').text(user.banks.stk);
                $('#ifscCode').text(user.banks.email);

                $('#CryptoAdd').text(user.banks.cryptoAdd);

            }
        });

        $('.txtBox .van-image').click(function(e) {
            e.preventDefault();
            $(this).addClass('action block-click');
            setTimeout(() => {
                $(this).removeClass('action block-click');
            }, 2500);
        });
        $.ajax({
            type: "POST",
            url: "/api/webapi/check/Info",
            dataType: "json",
            success: function(response) {
                let user = response.userInfo[0];
                $('.money').text("₹" + user.money.toFixed(2));

                $('.withdrawMoney').text("₹" + response.result);


                console.log(user);
                $('#remAmt').html(`2. Remaining Betting Amount<br>For Withdrawal: ₹ ${response.result}`);
                if (!response.datas[0]) return;
                let data = response.datas[0];
                // $('.conBox .list').append(
                //     `
                //     <div data-v-25d9c352="" class="item c-row c-row-between c-row-middle-center">
                //         <div data-v-25d9c352="" class="name van-ellipsis">
                //             <span data-v-25d9c352="">${data.stk}</span>
                //         </div>
                //         <div data-v-25d9c352="" class="icon action c-row c-row-middle-center">
                //             <i data-v-25d9c352="" class="van-icon van-icon-success" style="color: #F3C300; font-size: 16px;">
                //                 <!---->
                //             </i>
                //         </div>
                //     </div>
                //     `
                // );
            }
        });
    </script>
    <script>
        function alertMess(text, sic) {
            $('body').append(
                `
                <div data-v-1dcba851="" class="msg">
                    <div data-v-1dcba851="" class="msg-content v-enter-active v-enter-to" style=""> ${text} </div>
                </div>
                `
            );
            setTimeout(() => {
                $('.msg .msg-content').removeClass('v-enter-active v-enter-to');
                $('.msg .msg-content').addClass('v-leave-active v-leave-to');
                setTimeout(() => {
                    $('.msg').remove();
                }, 100);
                sic.removeClass('block-click');
            }, 1000);
        }
        $('button').click(function(e) {
            e.preventDefault();
            let money = $('input:eq(0)').val().trim();
            let password = $('input:eq(1)').val().trim();
            let withdrawMethod = $('#withdrawMethod').val().trim();
            if (money && password && money >= 100) {
                $.ajax({
                    type: "POST",
                    url: "/api/webapi/withdrawal",
                    data: {
                        money: money,
                        password: password,
                        withdrawMethod: withdrawMethod,
                    },
                    dataType: "json",
                    success: function(response) {
                        $(this).addClass('block-click');
                        if (response.status == true) {
                            $('.money').text("₹" + response.money.toFixed(2));
                        }
                        alertMess(response.message, $(this));

                        console.log(response.timeStamp);
                    }
                });
            } else if (!money || !password) {
                $(this).addClass('block-click');
                alertMess('Please Enter Complete Information', $(this));
            } else {
                $(this).addClass('block-click');
                alertMess('Withdrawal Amount Range: <br>100 - Unlimited', $(this));
            }
        });
    </script>
</body>

</html>